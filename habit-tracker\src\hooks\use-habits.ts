'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { localStorageAPI, Habit, HabitCompletion, Category, HabitStreak } from '@/lib/local-storage';
import toast from 'react-hot-toast';

type HabitWithExtras = Habit & {
  category?: Category;
  streak?: HabitStreak;
  completedToday?: boolean;
  todayCompletion?: HabitCompletion;
};

type CreateHabitData = Omit<Habit, 'id' | 'created_at' | 'updated_at'>;
type UpdateHabitData = Partial<Habit>;

export function useHabits() {
  const { user } = useAuth();
  const [habits, setHabits] = useState<HabitWithExtras[]>([]);
  const [completions, setCompletions] = useState<HabitCompletion[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch habits
  const fetchHabits = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);

      const userHabits = localStorageAPI.getHabits(user.id);
      const categories = localStorageAPI.getCategories();
      const streaks = localStorageAPI.getHabitStreaks(user.id);

      // Enhance habits with category and streak data
      const enhancedHabits = userHabits.map(habit => ({
        ...habit,
        category: categories.find(c => c.id === habit.category_id),
        streak: streaks.find(s => s.habit_id === habit.id),
      }));

      setHabits(enhancedHabits);
    } catch (err: any) {
      setError(err.message);
      toast.error('Failed to load habits');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Fetch completions for today
  const fetchTodayCompletions = useCallback(async () => {
    if (!user) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const todayCompletions = localStorageAPI.getHabitCompletions(user.id, today, tomorrow);
      setCompletions(todayCompletions);
    } catch (err: any) {
      setError(err.message);
    }
  }, [user]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const categories = localStorageAPI.getCategories();
      setCategories(categories);
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  // Create habit
  const createHabit = async (habitData: CreateHabitData) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const newHabit = localStorageAPI.createHabit({
        ...habitData,
        user_id: user.id,
      });

      toast.success('Habit created successfully!');
      await fetchHabits();

      return { data: newHabit, error: null };
    } catch (err: any) {
      toast.error('Failed to create habit');
      return { data: null, error: err.message };
    }
  };

  // Update habit
  const updateHabit = async (habitId: string, updates: UpdateHabitData) => {
    try {
      const updatedHabit = localStorageAPI.updateHabit(habitId, updates);

      if (!updatedHabit) throw new Error('Habit not found');

      toast.success('Habit updated successfully!');
      await fetchHabits();

      return { error: null };
    } catch (err: any) {
      toast.error('Failed to update habit');
      return { error: err.message };
    }
  };

  // Delete habit
  const deleteHabit = async (habitId: string) => {
    try {
      const success = localStorageAPI.deleteHabit(habitId);

      if (!success) throw new Error('Habit not found');

      toast.success('Habit deleted successfully!');
      await fetchHabits();

      return { error: null };
    } catch (err: any) {
      toast.error('Failed to delete habit');
      return { error: err.message };
    }
  };

  // Complete habit
  const completeHabit = async (habitId: string, value = 1, notes?: string) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const completion = localStorageAPI.completeHabit({
        habit_id: habitId,
        user_id: user.id,
        value,
        notes,
        completed_at: new Date().toISOString(),
      });

      toast.success('Habit completed! 🎉');
      await fetchTodayCompletions();
      await fetchHabits(); // Refresh to update streaks

      return { data: completion, error: null };
    } catch (err: any) {
      toast.error('Failed to complete habit');
      return { data: null, error: err.message };
    }
  };

  // Uncomplete habit
  const uncompleteHabit = async (habitId: string) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const today = new Date().toISOString().split('T')[0];
      const success = localStorageAPI.uncompleteHabit(habitId, user.id, today);

      if (!success) throw new Error('Failed to uncomplete habit');

      toast.success('Habit unmarked');
      await fetchTodayCompletions();
      await fetchHabits(); // Refresh to update streaks

      return { error: null };
    } catch (err: any) {
      toast.error('Failed to uncomplete habit');
      return { error: err.message };
    }
  };

  // Check if habit is completed today
  const isHabitCompletedToday = (habitId: string) => {
    return completions.some(completion => completion.habit_id === habitId);
  };

  // Get habit completion for today
  const getHabitCompletionToday = (habitId: string) => {
    return completions.find(completion => completion.habit_id === habitId);
  };

  // Get habits with completion status
  const habitsWithStatus = habits.map(habit => ({
    ...habit,
    completedToday: isHabitCompletedToday(habit.id),
    todayCompletion: getHabitCompletionToday(habit.id),
  }));

  // Statistics
  const stats = {
    totalHabits: habits.length,
    activeHabits: habits.filter(h => h.is_active).length,
    completedToday: completions.length,
    completionRate: habits.length > 0 ? Math.round((completions.length / habits.length) * 100) : 0,
    longestStreak: Math.max(...habits.map(h => h.streak?.longest_streak || 0), 0),
    currentStreaks: habits.reduce((sum, h) => sum + (h.streak?.current_streak || 0), 0),
  };

  // Initialize data
  useEffect(() => {
    if (user) {
      fetchHabits();
      fetchTodayCompletions();
      fetchCategories();
    }
  }, [user, fetchHabits, fetchTodayCompletions, fetchCategories]);

  return {
    habits: habitsWithStatus,
    completions,
    categories,
    loading,
    error,
    stats,
    actions: {
      createHabit,
      updateHabit,
      deleteHabit,
      completeHabit,
      uncompleteHabit,
      refreshHabits: fetchHabits,
      refreshCompletions: fetchTodayCompletions,
    },
    utils: {
      isHabitCompletedToday,
      getHabitCompletionToday,
    },
  };
}

// Hook for habit analytics
export function useHabitAnalytics() {
  const { user } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  const fetchAnalytics = useCallback(async (metricType: string, timePeriod: string) => {
    if (!user) return;

    try {
      const { data, error } = await db.getAnalyticsData(user.id, metricType, timePeriod);
      
      if (error) throw error;
      
      setAnalyticsData(prev => ({
        ...prev,
        [`${metricType}_${timePeriod}`]: data,
      }));
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
    }
  }, [user]);

  const getStreakTrends = async (days = 30) => {
    if (!user) return [];

    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
      
      const { data, error } = await db.getHabitCompletions(
        user.id,
        startDate.toISOString(),
        endDate.toISOString()
      );

      if (error) throw error;

      // Process data to create trend analysis
      const trendData = data?.reduce((acc: any, completion) => {
        const date = completion.completed_at.split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {}) || {};

      return Object.entries(trendData).map(([date, count]) => ({
        date,
        completions: count,
      }));
    } catch (err: any) {
      console.error('Failed to fetch streak trends:', err);
      return [];
    }
  };

  useEffect(() => {
    if (user) {
      setLoading(true);
      Promise.all([
        fetchAnalytics('completions', 'daily'),
        fetchAnalytics('streaks', 'weekly'),
        fetchAnalytics('mood_correlation', 'monthly'),
      ]).finally(() => setLoading(false));
    }
  }, [user, fetchAnalytics]);

  return {
    analyticsData,
    loading,
    getStreakTrends,
    fetchAnalytics,
  };
}
