import { createClient } from '@supabase/supabase-js';
import { Database } from './database.types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo_key';

// Check if we have real Supabase credentials
export const isSupabaseConfigured = supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'demo_key';

export const supabase = isSupabaseConfigured ? createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
}) : null;

// Auth helpers
export const auth = {
  signUp: async (email: string, password: string, metadata?: any) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
  },

  signIn: async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({
      email,
      password,
    });
  },

  signInWithGoogle: async () => {
    return await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });
  },

  signOut: async () => {
    return await supabase.auth.signOut();
  },

  getCurrentUser: async () => {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  getCurrentSession: async () => {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  },
};

// Database helpers
export const db = {
  // Profiles
  getProfile: async (userId: string) => {
    return await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
  },

  updateProfile: async (userId: string, updates: any) => {
    return await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId);
  },

  // Habits
  getHabits: async (userId: string) => {
    return await supabase
      .from('habits')
      .select(`
        *,
        category:habit_categories(*),
        streak:habit_streaks(*)
      `)
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
  },

  createHabit: async (habit: any) => {
    return await supabase
      .from('habits')
      .insert(habit)
      .select()
      .single();
  },

  updateHabit: async (habitId: string, updates: any) => {
    return await supabase
      .from('habits')
      .update(updates)
      .eq('id', habitId);
  },

  deleteHabit: async (habitId: string) => {
    return await supabase
      .from('habits')
      .update({ is_active: false })
      .eq('id', habitId);
  },

  // Habit Completions
  getHabitCompletions: async (userId: string, startDate?: string, endDate?: string) => {
    let query = supabase
      .from('habit_completions')
      .select(`
        *,
        habit:habits(*)
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: false });

    if (startDate) {
      query = query.gte('completed_at', startDate);
    }
    if (endDate) {
      query = query.lte('completed_at', endDate);
    }

    return await query;
  },

  completeHabit: async (completion: any) => {
    return await supabase
      .from('habit_completions')
      .insert(completion)
      .select()
      .single();
  },

  uncompleteHabit: async (habitId: string, userId: string, date: string) => {
    return await supabase
      .from('habit_completions')
      .delete()
      .eq('habit_id', habitId)
      .eq('user_id', userId)
      .gte('completed_at', date)
      .lt('completed_at', new Date(new Date(date).getTime() + 24 * 60 * 60 * 1000).toISOString());
  },

  // Mood Entries
  getMoodEntries: async (userId: string, startDate?: string, endDate?: string) => {
    let query = supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .order('recorded_for_date', { ascending: false });

    if (startDate) {
      query = query.gte('recorded_for_date', startDate);
    }
    if (endDate) {
      query = query.lte('recorded_for_date', endDate);
    }

    return await query;
  },

  createMoodEntry: async (moodEntry: any) => {
    return await supabase
      .from('mood_entries')
      .insert(moodEntry)
      .select()
      .single();
  },

  updateMoodEntry: async (entryId: string, updates: any) => {
    return await supabase
      .from('mood_entries')
      .update(updates)
      .eq('id', entryId);
  },

  // Categories
  getCategories: async () => {
    return await supabase
      .from('habit_categories')
      .select('*')
      .order('name');
  },

  // Achievements
  getAchievements: async () => {
    return await supabase
      .from('achievements')
      .select('*')
      .order('points');
  },

  getUserAchievements: async (userId: string) => {
    return await supabase
      .from('user_achievements')
      .select(`
        *,
        achievement:achievements(*)
      `)
      .eq('user_id', userId)
      .order('unlocked_at', { ascending: false });
  },

  unlockAchievement: async (userId: string, achievementId: string) => {
    return await supabase
      .from('user_achievements')
      .insert({
        user_id: userId,
        achievement_id: achievementId,
      })
      .select()
      .single();
  },

  // Streaks
  getHabitStreaks: async (userId: string) => {
    return await supabase
      .from('habit_streaks')
      .select(`
        *,
        habit:habits(*)
      `)
      .eq('user_id', userId)
      .order('current_streak', { ascending: false });
  },

  // Analytics
  getAnalyticsData: async (userId: string, metricType: string, timePeriod: string) => {
    return await supabase
      .from('analytics_cache')
      .select('*')
      .eq('user_id', userId)
      .eq('metric_type', metricType)
      .eq('time_period', timePeriod)
      .order('date_key', { ascending: false });
  },

  // Habit Stacks
  getHabitStacks: async (userId: string) => {
    return await supabase
      .from('habit_stacks')
      .select(`
        *,
        items:habit_stack_items(
          *,
          habit:habits(*)
        )
      `)
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
  },

  createHabitStack: async (stack: any) => {
    return await supabase
      .from('habit_stacks')
      .insert(stack)
      .select()
      .single();
  },

  // Notifications
  getNotifications: async (userId: string, unreadOnly = false) => {
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (unreadOnly) {
      query = query.is('read_at', null);
    }

    return await query;
  },

  markNotificationAsRead: async (notificationId: string) => {
    return await supabase
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId);
  },
};

// Real-time subscriptions
export const subscriptions = {
  subscribeToHabits: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('habits')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'habits',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToCompletions: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('completions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'habit_completions',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToMoodEntries: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('mood_entries')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'mood_entries',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToNotifications: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },
};

export default supabase;
