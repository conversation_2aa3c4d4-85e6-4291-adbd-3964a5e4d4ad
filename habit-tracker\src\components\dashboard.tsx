'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/components/auth/auth-provider';
import { useHabits } from '@/hooks/use-habits';
import { useMood } from '@/hooks/use-mood';
import {
  Calendar,
  Target,
  TrendingUp,
  Heart,
  Settings,
  Plus,
  BarChart3,
  Flame,
  Award,
  Moon,
  Sun
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { ThemeSelectorButton } from './theme-selector';
import { HabitManagement } from './habit-management';
import { Analytics } from './analytics';
import { MoodTracking } from './mood-tracking';
import { Settings as SettingsPanel } from './settings';

export function Dashboard() {
  const { user, profile, signOut } = useAuth();
  const { theme, setTheme } = useTheme();
  const { habits, stats: habitStats, loading: habitsLoading } = useHabits();
  const { stats: moodStats, utils: { isMoodLoggedToday } } = useMood();
  const [selectedTab, setSelectedTab] = useState('dashboard');

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'habits', label: 'Habits', icon: Target },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'mood', label: 'Mood', icon: Heart },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Flame className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold text-foreground">
                  HabitFlow
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium">{profile?.full_name || user?.email}</p>
                  <p className="text-xs text-muted-foreground">
                    {habitStats.completedToday}/{habitStats.totalHabits} habits today
                  </p>
                </div>
                {profile?.avatar_url && (
                  <img
                    src={profile.avatar_url}
                    alt="Profile"
                    className="w-8 h-8 rounded-full"
                  />
                )}
              </div>
              <ThemeSelectorButton />
              <button
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="p-2 rounded-lg hover:bg-accent transition-colors"
                title="Toggle Dark Mode"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </button>
              <button
                onClick={signOut}
                className="p-2 rounded-lg hover:bg-accent transition-colors text-muted-foreground"
                title="Sign Out"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      selectedTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-accent text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedTab === 'dashboard' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Today's Progress</p>
                        <p className="text-2xl font-bold text-foreground">
                          {habitStats.completedToday}/{habitStats.totalHabits}
                        </p>
                        <p className="text-sm text-muted-foreground">{habitStats.completionRate}% Complete</p>
                      </div>
                      <Target className="h-8 w-8 text-primary" />
                    </div>
                  </div>

                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Longest Streak</p>
                        <p className="text-2xl font-bold text-foreground">{habitStats.longestStreak}</p>
                        <p className="text-sm text-muted-foreground">Days</p>
                      </div>
                      <Flame className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>

                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Average Mood</p>
                        <p className="text-2xl font-bold text-foreground">{moodStats.averageMood.toFixed(1)}/5</p>
                        <p className="text-sm text-muted-foreground">
                          {isMoodLoggedToday() ? 'Logged today' : 'Not logged today'}
                        </p>
                      </div>
                      <Heart className="h-8 w-8 text-red-500" />
                    </div>
                  </div>
                </div>

                {/* Today's Habits */}
                <div className="bg-card p-6 rounded-lg border border-border">
                  <h2 className="text-xl font-semibold mb-4">Today's Habits</h2>
                  {habitsLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : habits.length === 0 ? (
                    <div className="text-center py-8">
                      <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No habits yet. Create your first habit to get started!</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {habits.slice(0, 5).map((habit) => (
                        <div
                          key={habit.id}
                          className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                        >
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                                habit.completedToday
                                  ? 'bg-primary border-primary'
                                  : 'border-muted-foreground'
                              }`}
                            >
                              {habit.completedToday && (
                                <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                              )}
                            </div>
                            <div>
                              <p className="font-medium">{habit.name}</p>
                              <p className="text-sm text-muted-foreground">{habit.category?.name}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Flame className="h-4 w-4 text-orange-500" />
                            <span className="text-sm font-medium">{habit.streak?.current_streak || 0}</span>
                          </div>
                        </div>
                      ))}
                      {habits.length > 5 && (
                        <p className="text-sm text-muted-foreground text-center pt-2">
                          And {habits.length - 5} more habits...
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="bg-card p-6 rounded-lg border border-border">
                  <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Plus className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Add Habit</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Heart className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Track Mood</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <TrendingUp className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">View Analytics</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Calendar className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Calendar</span>
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {selectedTab === 'habits' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <HabitManagement />
              </motion.div>
            )}

            {selectedTab === 'analytics' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Analytics />
              </motion.div>
            )}

            {selectedTab === 'mood' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MoodTracking />
              </motion.div>
            )}

            {selectedTab === 'settings' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <SettingsPanel />
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
