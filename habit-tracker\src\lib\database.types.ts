export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          timezone: string | null
          created_at: string
          updated_at: string
          preferences: J<PERSON>
        }
        Insert: {
          id: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          timezone?: string | null
          created_at?: string
          updated_at?: string
          preferences?: Json
        }
        Update: {
          id?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          timezone?: string | null
          created_at?: string
          updated_at?: string
          preferences?: Json
        }
      }
      habit_categories: {
        Row: {
          id: string
          name: string
          icon: string
          color: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          icon: string
          color: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          icon?: string
          color?: string
          description?: string | null
          created_at?: string
        }
      }
      habits: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          name: string
          description: string | null
          icon: string
          color: string
          frequency: 'daily' | 'weekly' | 'weekdays' | 'weekends' | 'custom'
          target_value: number
          unit: string
          reminder_time: string | null
          reminder_enabled: boolean
          is_active: boolean
          created_at: string
          updated_at: string
          custom_frequency: Json | null
          tags: string[]
          difficulty_level: number
          estimated_duration: number | null
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          name: string
          description?: string | null
          icon?: string
          color?: string
          frequency?: 'daily' | 'weekly' | 'weekdays' | 'weekends' | 'custom'
          target_value?: number
          unit?: string
          reminder_time?: string | null
          reminder_enabled?: boolean
          is_active?: boolean
          created_at?: string
          updated_at?: string
          custom_frequency?: Json | null
          tags?: string[]
          difficulty_level?: number
          estimated_duration?: number | null
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          icon?: string
          color?: string
          frequency?: 'daily' | 'weekly' | 'weekdays' | 'weekends' | 'custom'
          target_value?: number
          unit?: string
          reminder_time?: string | null
          reminder_enabled?: boolean
          is_active?: boolean
          created_at?: string
          updated_at?: string
          custom_frequency?: Json | null
          tags?: string[]
          difficulty_level?: number
          estimated_duration?: number | null
        }
      }
      habit_completions: {
        Row: {
          id: string
          habit_id: string
          user_id: string
          completed_at: string
          value: number
          notes: string | null
          mood_before: number | null
          mood_after: number | null
          location: string | null
          weather: string | null
          energy_level: number | null
          created_at: string
        }
        Insert: {
          id?: string
          habit_id: string
          user_id: string
          completed_at?: string
          value?: number
          notes?: string | null
          mood_before?: number | null
          mood_after?: number | null
          location?: string | null
          weather?: string | null
          energy_level?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          habit_id?: string
          user_id?: string
          completed_at?: string
          value?: number
          notes?: string | null
          mood_before?: number | null
          mood_after?: number | null
          location?: string | null
          weather?: string | null
          energy_level?: number | null
          created_at?: string
        }
      }
      mood_entries: {
        Row: {
          id: string
          user_id: string
          mood: 'terrible' | 'poor' | 'okay' | 'good' | 'excellent'
          mood_value: number
          notes: string | null
          energy_level: number | null
          stress_level: number | null
          sleep_quality: number | null
          weather: string | null
          location: string | null
          tags: string[]
          created_at: string
          recorded_for_date: string
        }
        Insert: {
          id?: string
          user_id: string
          mood: 'terrible' | 'poor' | 'okay' | 'good' | 'excellent'
          mood_value: number
          notes?: string | null
          energy_level?: number | null
          stress_level?: number | null
          sleep_quality?: number | null
          weather?: string | null
          location?: string | null
          tags?: string[]
          created_at?: string
          recorded_for_date?: string
        }
        Update: {
          id?: string
          user_id?: string
          mood?: 'terrible' | 'poor' | 'okay' | 'good' | 'excellent'
          mood_value?: number
          notes?: string | null
          energy_level?: number | null
          stress_level?: number | null
          sleep_quality?: number | null
          weather?: string | null
          location?: string | null
          tags?: string[]
          created_at?: string
          recorded_for_date?: string
        }
      }
      achievements: {
        Row: {
          id: string
          name: string
          description: string
          icon: string
          type: 'streak' | 'completion' | 'consistency' | 'milestone'
          criteria: Json
          points: number
          rarity: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          icon: string
          type: 'streak' | 'completion' | 'consistency' | 'milestone'
          criteria: Json
          points?: number
          rarity?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          icon?: string
          type?: 'streak' | 'completion' | 'consistency' | 'milestone'
          criteria?: Json
          points?: number
          rarity?: string
          created_at?: string
        }
      }
      user_achievements: {
        Row: {
          id: string
          user_id: string
          achievement_id: string
          unlocked_at: string
          progress: Json
        }
        Insert: {
          id?: string
          user_id: string
          achievement_id: string
          unlocked_at?: string
          progress?: Json
        }
        Update: {
          id?: string
          user_id?: string
          achievement_id?: string
          unlocked_at?: string
          progress?: Json
        }
      }
      habit_streaks: {
        Row: {
          id: string
          habit_id: string
          user_id: string
          current_streak: number
          longest_streak: number
          last_completed_date: string | null
          streak_start_date: string | null
          updated_at: string
        }
        Insert: {
          id?: string
          habit_id: string
          user_id: string
          current_streak?: number
          longest_streak?: number
          last_completed_date?: string | null
          streak_start_date?: string | null
          updated_at?: string
        }
        Update: {
          id?: string
          habit_id?: string
          user_id?: string
          current_streak?: number
          longest_streak?: number
          last_completed_date?: string | null
          streak_start_date?: string | null
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: 'push' | 'email' | 'sms'
          title: string
          message: string
          data: Json
          scheduled_for: string | null
          sent_at: string | null
          read_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: 'push' | 'email' | 'sms'
          title: string
          message: string
          data?: Json
          scheduled_for?: string | null
          sent_at?: string | null
          read_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'push' | 'email' | 'sms'
          title?: string
          message?: string
          data?: Json
          scheduled_for?: string | null
          sent_at?: string | null
          read_at?: string | null
          created_at?: string
        }
      }
      habit_stacks: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      habit_stack_items: {
        Row: {
          id: string
          stack_id: string
          habit_id: string
          order_index: number
          is_required: boolean
          created_at: string
        }
        Insert: {
          id?: string
          stack_id: string
          habit_id: string
          order_index: number
          is_required?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          stack_id?: string
          habit_id?: string
          order_index?: number
          is_required?: boolean
          created_at?: string
        }
      }
      analytics_cache: {
        Row: {
          id: string
          user_id: string
          metric_type: string
          time_period: string
          date_key: string
          data: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          metric_type: string
          time_period: string
          date_key: string
          data: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          metric_type?: string
          time_period?: string
          date_key?: string
          data?: Json
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      habit_frequency: 'daily' | 'weekly' | 'weekdays' | 'weekends' | 'custom'
      mood_level: 'terrible' | 'poor' | 'okay' | 'good' | 'excellent'
      notification_type: 'push' | 'email' | 'sms'
      achievement_type: 'streak' | 'completion' | 'consistency' | 'milestone'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
