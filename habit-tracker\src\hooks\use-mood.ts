'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { db, subscriptions, isSupabaseConfigured } from '@/lib/supabase';
import { Database } from '@/lib/database.types';
import toast from 'react-hot-toast';

type MoodEntry = Database['public']['Tables']['mood_entries']['Row'];
type CreateMoodData = Database['public']['Tables']['mood_entries']['Insert'];
type UpdateMoodData = Database['public']['Tables']['mood_entries']['Update'];

interface MoodStats {
  averageMood: number;
  moodTrend: number;
  moodDistribution: { [key: string]: number };
  totalEntries: number;
  streakDays: number;
}

interface MoodCorrelation {
  habitCompletions: number;
  averageMood: number;
  dataPoints: number;
}

// Demo mood data for when Supabase is not configured
const demoMoodEntries = [
  {
    id: '1',
    user_id: 'demo-user-123',
    mood: 'good' as const,
    mood_value: 4,
    notes: 'Had a productive day with good habits!',
    energy_level: 4,
    stress_level: 2,
    sleep_quality: 4,
    weather: 'sunny',
    location: null,
    tags: [],
    created_at: new Date().toISOString(),
    recorded_for_date: new Date().toISOString().split('T')[0],
  },
  {
    id: '2',
    user_id: 'demo-user-123',
    mood: 'excellent' as const,
    mood_value: 5,
    notes: 'Amazing day! Completed all my habits.',
    energy_level: 5,
    stress_level: 1,
    sleep_quality: 5,
    weather: 'sunny',
    location: null,
    tags: ['productive', 'happy'],
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    recorded_for_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  },
  {
    id: '3',
    user_id: 'demo-user-123',
    mood: 'okay' as const,
    mood_value: 3,
    notes: 'Average day, could be better.',
    energy_level: 3,
    stress_level: 3,
    sleep_quality: 3,
    weather: 'cloudy',
    location: null,
    tags: [],
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    recorded_for_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  },
];

export function useMood() {
  const { user } = useAuth();
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch mood entries
  const fetchMoodEntries = useCallback(async (startDate?: string, endDate?: string) => {
    if (!user) return;

    try {
      setLoading(true);

      if (!isSupabaseConfigured) {
        // Use demo data
        let filteredEntries = demoMoodEntries;

        if (startDate || endDate) {
          filteredEntries = demoMoodEntries.filter(entry => {
            const entryDate = entry.recorded_for_date;
            if (startDate && entryDate < startDate) return false;
            if (endDate && entryDate > endDate) return false;
            return true;
          });
        }

        setMoodEntries(filteredEntries);
        setLoading(false);
        return;
      }

      const { data, error } = await db.getMoodEntries(user.id, startDate, endDate);

      if (error) throw error;

      setMoodEntries(data || []);
    } catch (err: any) {
      setError(err.message);
      toast.error('Failed to load mood entries');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Create mood entry
  const createMoodEntry = async (moodData: Omit<CreateMoodData, 'user_id'>) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const { data, error } = await db.createMoodEntry({
        ...moodData,
        user_id: user.id,
      });

      if (error) throw error;

      toast.success('Mood logged successfully! 😊');
      await fetchMoodEntries();
      
      return { data, error: null };
    } catch (err: any) {
      toast.error('Failed to log mood');
      return { data: null, error: err.message };
    }
  };

  // Update mood entry
  const updateMoodEntry = async (entryId: string, updates: UpdateMoodData) => {
    try {
      const { error } = await db.updateMoodEntry(entryId, updates);

      if (error) throw error;

      toast.success('Mood entry updated!');
      await fetchMoodEntries();
      
      return { error: null };
    } catch (err: any) {
      toast.error('Failed to update mood entry');
      return { error: err.message };
    }
  };

  // Get mood entry for specific date
  const getMoodForDate = (date: string) => {
    return moodEntries.find(entry => entry.recorded_for_date === date);
  };

  // Check if mood is logged for today
  const isMoodLoggedToday = () => {
    const today = new Date().toISOString().split('T')[0];
    return moodEntries.some(entry => entry.recorded_for_date === today);
  };

  // Calculate mood statistics
  const calculateMoodStats = (): MoodStats => {
    if (moodEntries.length === 0) {
      return {
        averageMood: 0,
        moodTrend: 0,
        moodDistribution: {},
        totalEntries: 0,
        streakDays: 0,
      };
    }

    // Average mood
    const averageMood = moodEntries.reduce((sum, entry) => sum + entry.mood_value, 0) / moodEntries.length;

    // Mood trend (last 7 days vs previous 7 days)
    const sortedEntries = [...moodEntries].sort((a, b) => 
      new Date(b.recorded_for_date).getTime() - new Date(a.recorded_for_date).getTime()
    );
    
    const recentEntries = sortedEntries.slice(0, 7);
    const previousEntries = sortedEntries.slice(7, 14);
    
    const recentAverage = recentEntries.length > 0 
      ? recentEntries.reduce((sum, entry) => sum + entry.mood_value, 0) / recentEntries.length 
      : 0;
    
    const previousAverage = previousEntries.length > 0 
      ? previousEntries.reduce((sum, entry) => sum + entry.mood_value, 0) / previousEntries.length 
      : recentAverage;

    const moodTrend = previousAverage > 0 ? ((recentAverage - previousAverage) / previousAverage) * 100 : 0;

    // Mood distribution
    const moodDistribution = moodEntries.reduce((acc, entry) => {
      acc[entry.mood] = (acc[entry.mood] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // Calculate streak days
    const streakDays = calculateMoodStreak();

    return {
      averageMood: Math.round(averageMood * 10) / 10,
      moodTrend: Math.round(moodTrend * 10) / 10,
      moodDistribution,
      totalEntries: moodEntries.length,
      streakDays,
    };
  };

  // Calculate mood logging streak
  const calculateMoodStreak = (): number => {
    if (moodEntries.length === 0) return 0;

    const sortedEntries = [...moodEntries].sort((a, b) => 
      new Date(b.recorded_for_date).getTime() - new Date(a.recorded_for_date).getTime()
    );

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const entry of sortedEntries) {
      const entryDate = new Date(entry.recorded_for_date);
      entryDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor((currentDate.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
        currentDate = new Date(entryDate.getTime() - 24 * 60 * 60 * 1000);
      } else {
        break;
      }
    }

    return streak;
  };

  // Get mood correlation with habits
  const getMoodHabitCorrelation = async (): Promise<MoodCorrelation[]> => {
    if (!user) return [];

    try {
      // Get habit completions for the same period as mood entries
      const { data: completions, error } = await db.getHabitCompletions(user.id);
      
      if (error) throw error;

      // Group by date and calculate correlations
      const correlationData: { [date: string]: { mood: number; habits: number } } = {};

      // Add mood data
      moodEntries.forEach(entry => {
        correlationData[entry.recorded_for_date] = {
          mood: entry.mood_value,
          habits: 0,
        };
      });

      // Add habit completion data
      completions?.forEach(completion => {
        const date = completion.completed_at.split('T')[0];
        if (correlationData[date]) {
          correlationData[date].habits += completion.value;
        }
      });

      // Convert to correlation format
      const correlations: { [habits: number]: { totalMood: number; count: number } } = {};

      Object.values(correlationData).forEach(({ mood, habits }) => {
        if (!correlations[habits]) {
          correlations[habits] = { totalMood: 0, count: 0 };
        }
        correlations[habits].totalMood += mood;
        correlations[habits].count += 1;
      });

      return Object.entries(correlations).map(([habits, data]) => ({
        habitCompletions: parseInt(habits),
        averageMood: Math.round((data.totalMood / data.count) * 10) / 10,
        dataPoints: data.count,
      })).sort((a, b) => a.habitCompletions - b.habitCompletions);

    } catch (err: any) {
      console.error('Failed to calculate mood-habit correlation:', err);
      return [];
    }
  };

  // Get mood trends for visualization
  const getMoodTrends = (days = 30) => {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

    return moodEntries
      .filter(entry => {
        const entryDate = new Date(entry.recorded_for_date);
        return entryDate >= startDate && entryDate <= endDate;
      })
      .sort((a, b) => new Date(a.recorded_for_date).getTime() - new Date(b.recorded_for_date).getTime())
      .map(entry => ({
        date: entry.recorded_for_date,
        mood: entry.mood_value,
        mood_label: entry.mood,
        notes: entry.notes,
        energy_level: entry.energy_level,
        stress_level: entry.stress_level,
      }));
  };

  // Initialize data and subscriptions
  useEffect(() => {
    if (user) {
      fetchMoodEntries();

      // Set up real-time subscription only if Supabase is configured
      if (isSupabaseConfigured) {
        const subscription = subscriptions.subscribeToMoodEntries(user.id, (payload) => {
          console.log('Mood entries updated:', payload);
          fetchMoodEntries();
        });

        return () => {
          subscription.unsubscribe();
        };
      }
    }
  }, [user, fetchMoodEntries]);

  const stats = calculateMoodStats();

  return {
    moodEntries,
    loading,
    error,
    stats,
    actions: {
      createMoodEntry,
      updateMoodEntry,
      refreshMoodEntries: fetchMoodEntries,
    },
    utils: {
      getMoodForDate,
      isMoodLoggedToday,
      getMoodHabitCorrelation,
      getMoodTrends,
    },
  };
}

// Hook for mood insights and analysis
export function useMoodInsights() {
  const { moodEntries } = useMood();
  const [insights, setInsights] = useState<string[]>([]);

  const generateInsights = useCallback(() => {
    if (moodEntries.length < 7) {
      setInsights(['Log more mood entries to get personalized insights!']);
      return;
    }

    const newInsights: string[] = [];
    
    // Analyze mood patterns
    const recentEntries = moodEntries.slice(-7);
    const averageRecentMood = recentEntries.reduce((sum, entry) => sum + entry.mood_value, 0) / recentEntries.length;

    if (averageRecentMood >= 4) {
      newInsights.push('🌟 You\'ve been feeling great lately! Keep up the positive momentum.');
    } else if (averageRecentMood <= 2.5) {
      newInsights.push('💙 Your mood has been lower recently. Consider reaching out to someone or trying a mood-boosting activity.');
    }

    // Analyze mood consistency
    const moodVariance = recentEntries.reduce((sum, entry) => 
      sum + Math.pow(entry.mood_value - averageRecentMood, 2), 0
    ) / recentEntries.length;

    if (moodVariance < 0.5) {
      newInsights.push('📊 Your mood has been very stable recently - that\'s a sign of good emotional regulation!');
    } else if (moodVariance > 2) {
      newInsights.push('🎢 Your mood has been quite variable. Consider what factors might be influencing these changes.');
    }

    // Analyze weekly patterns
    const weekdayMoods = recentEntries.reduce((acc, entry) => {
      const day = new Date(entry.recorded_for_date).getDay();
      acc[day] = acc[day] || [];
      acc[day].push(entry.mood_value);
      return acc;
    }, {} as { [key: number]: number[] });

    const weekdayAverages = Object.entries(weekdayMoods).map(([day, moods]) => ({
      day: parseInt(day),
      average: moods.reduce((sum, mood) => sum + mood, 0) / moods.length,
    }));

    const bestDay = weekdayAverages.reduce((best, current) => 
      current.average > best.average ? current : best
    );

    const worstDay = weekdayAverages.reduce((worst, current) => 
      current.average < worst.average ? current : worst
    );

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    if (weekdayAverages.length >= 3) {
      newInsights.push(`📅 Your best day tends to be ${dayNames[bestDay.day]}, while ${dayNames[worstDay.day]} is more challenging.`);
    }

    setInsights(newInsights);
  }, [moodEntries]);

  useEffect(() => {
    generateInsights();
  }, [generateInsights]);

  return {
    insights,
    generateInsights,
  };
}
