'use client';

import { ThemeProvider } from 'next-themes';
import { AuthProvider, ProtectedRoute } from '@/components/auth/auth-provider';
import { Toaster } from 'react-hot-toast';

// Available themes
const themes = [
  'light',
  'dark',
  'ocean',
  'forest',
  'sunset',
  'minimal',
  'vibrant',
  'pastel'
];

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      themes={themes}
      storageKey="habitflow-theme"
    >
      <AuthProvider>
        <ProtectedRoute>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--card))',
                color: 'hsl(var(--card-foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </ProtectedRoute>
      </AuthProvider>
    </ThemeProvider>
  );
}

export { themes };
