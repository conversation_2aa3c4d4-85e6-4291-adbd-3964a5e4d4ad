'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHabits } from '@/hooks/use-habits';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Target, 
  Calendar,
  Clock,
  Tag,
  Palette,
  Save,
  X,
  Check,
  Flame,
  Star,
  Heart,
  Zap,
  Coffee,
  Book,
  Dumbbell,
  Droplets,
  Moon,
  Sun
} from 'lucide-react';

// Habit categories with icons and colors
const categories = [
  { id: 'health', name: 'Health', icon: Heart, color: 'text-red-500' },
  { id: 'fitness', name: 'Fitness', icon: Dumbbell, color: 'text-orange-500' },
  { id: 'learning', name: 'Learning', icon: Book, color: 'text-blue-500' },
  { id: 'wellness', name: 'Wellness', icon: Star, color: 'text-purple-500' },
  { id: 'productivity', name: 'Productivity', icon: Zap, color: 'text-yellow-500' },
  { id: 'lifestyle', name: 'Lifestyle', icon: Coffee, color: 'text-green-500' },
  { id: 'mindfulness', name: 'Mindfulness', icon: Moon, color: 'text-indigo-500' },
  { id: 'other', name: 'Other', icon: Target, color: 'text-gray-500' },
];

// Habit icons
const habitIcons = [
  Droplets, Heart, Dumbbell, Book, Coffee, Moon, Sun, Star, 
  Zap, Target, Calendar, Clock, Tag, Flame
];

// Frequency options
const frequencies = [
  { id: 'daily', name: 'Daily', description: 'Every day' },
  { id: 'weekly', name: 'Weekly', description: 'Once a week' },
  { id: 'weekdays', name: 'Weekdays', description: 'Monday to Friday' },
  { id: 'weekends', name: 'Weekends', description: 'Saturday and Sunday' },
  { id: 'custom', name: 'Custom', description: 'Choose specific days' },
];

interface Habit {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  frequency: string;
  reminderTime?: string;
  streak: number;
  completedToday: boolean;
  createdAt: Date;
  lastCompleted?: Date;
}

interface HabitFormProps {
  habit?: Habit;
  isOpen: boolean;
  onClose: () => void;
  onSave: (habit: Partial<Habit>) => void;
}

function HabitForm({ habit, isOpen, onClose, onSave }: HabitFormProps) {
  const [formData, setFormData] = useState({
    name: habit?.name || '',
    description: habit?.description || '',
    category: habit?.category || 'health',
    icon: habit?.icon || 'Heart',
    color: habit?.color || 'text-red-500',
    frequency: habit?.frequency || 'daily',
    reminderTime: habit?.reminderTime || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  const selectedCategory = categories.find(c => c.id === formData.category);
  const SelectedIcon = habitIcons.find(icon => icon.name === formData.icon) || Heart;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-card border border-border rounded-lg shadow-lg z-50 w-full max-w-md max-h-[90vh] overflow-y-auto"
          >
            <form onSubmit={handleSubmit} className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">
                  {habit ? 'Edit Habit' : 'Create New Habit'}
                </h2>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                {/* Habit Name */}
                <div>
                  <label className="block text-sm font-medium mb-2">Habit Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g., Drink 8 glasses of water"
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium mb-2">Description (Optional)</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Add more details about your habit..."
                    rows={3}
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <div className="grid grid-cols-2 gap-2">
                    {categories.map((category) => {
                      const Icon = category.icon;
                      return (
                        <button
                          key={category.id}
                          type="button"
                          onClick={() => setFormData({ ...formData, category: category.id, color: category.color })}
                          className={`p-3 rounded-lg border transition-all ${
                            formData.category === category.id
                              ? 'border-primary bg-primary/10'
                              : 'border-border hover:border-primary/50'
                          }`}
                        >
                          <Icon className={`h-5 w-5 mx-auto mb-1 ${category.color}`} />
                          <span className="text-xs">{category.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Icon Selection */}
                <div>
                  <label className="block text-sm font-medium mb-2">Icon</label>
                  <div className="grid grid-cols-7 gap-2">
                    {habitIcons.map((Icon, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setFormData({ ...formData, icon: Icon.name })}
                        className={`p-2 rounded-lg border transition-all ${
                          formData.icon === Icon.name
                            ? 'border-primary bg-primary/10'
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <Icon className={`h-4 w-4 mx-auto ${selectedCategory?.color || 'text-gray-500'}`} />
                      </button>
                    ))}
                  </div>
                </div>

                {/* Frequency */}
                <div>
                  <label className="block text-sm font-medium mb-2">Frequency</label>
                  <select
                    value={formData.frequency}
                    onChange={(e) => setFormData({ ...formData, frequency: e.target.value })}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {frequencies.map((freq) => (
                      <option key={freq.id} value={freq.id}>
                        {freq.name} - {freq.description}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Reminder Time */}
                <div>
                  <label className="block text-sm font-medium mb-2">Reminder Time (Optional)</label>
                  <input
                    type="time"
                    value={formData.reminderTime}
                    onChange={(e) => setFormData({ ...formData, reminderTime: e.target.value })}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>{habit ? 'Update' : 'Create'}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

interface HabitCardProps {
  habit: Habit;
  onEdit: (habit: Habit) => void;
  onDelete: (id: string) => void;
  onToggleComplete: (id: string) => void;
}

function HabitCard({ habit, onEdit, onDelete, onToggleComplete }: HabitCardProps) {
  const category = categories.find(c => c.id === habit.category);
  const Icon = habitIcons.find(icon => icon.name === habit.icon) || Heart;

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-accent/50`}>
            <Icon className={`h-5 w-5 ${category?.color || 'text-gray-500'}`} />
          </div>
          <div>
            <h3 className="font-medium">{habit.name}</h3>
            <p className="text-sm text-muted-foreground">{category?.name}</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => onEdit(habit)}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(habit.id)}
            className="p-1 text-muted-foreground hover:text-destructive transition-colors"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {habit.description && (
        <p className="text-sm text-muted-foreground mb-3">{habit.description}</p>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Flame className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium">{habit.streak}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground capitalize">{habit.frequency}</span>
          </div>
        </div>

        <button
          onClick={() => onToggleComplete(habit.id)}
          className={`p-2 rounded-lg transition-all ${
            habit.completedToday
              ? 'bg-primary text-primary-foreground'
              : 'border border-border hover:border-primary'
          }`}
        >
          <Check className="h-4 w-4" />
        </button>
      </div>
    </motion.div>
  );
}

export function HabitManagement() {
  const {
    habits,
    categories,
    loading,
    error,
    stats,
    actions: { createHabit, updateHabit, deleteHabit, completeHabit, uncompleteHabit }
  } = useHabits();

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingHabit, setEditingHabit] = useState<any>();
  const [filter, setFilter] = useState('all');

  const handleSaveHabit = async (habitData: any) => {
    if (editingHabit) {
      await updateHabit(editingHabit.id, habitData);
    } else {
      // Map category name to category_id
      const category = categories.find(c => c.name.toLowerCase() === habitData.category);
      await createHabit({
        ...habitData,
        category_id: category?.id || null,
      });
    }
    setEditingHabit(undefined);
  };

  const handleEditHabit = (habit: any) => {
    setEditingHabit(habit);
    setIsFormOpen(true);
  };

  const handleDeleteHabit = async (id: string) => {
    if (confirm('Are you sure you want to delete this habit?')) {
      await deleteHabit(id);
    }
  };

  const handleToggleComplete = async (id: string) => {
    const habit = habits.find(h => h.id === id);
    if (!habit) return;

    if (habit.completedToday) {
      await uncompleteHabit(id);
    } else {
      await completeHabit(id);
    }
  };

  const filteredHabits = habits.filter(habit => {
    if (filter === 'all') return true;
    if (filter === 'completed') return habit.completedToday;
    if (filter === 'pending') return !habit.completedToday;
    return habit.category?.name.toLowerCase() === filter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Habit Management</h2>
        <button
          onClick={() => {
            setEditingHabit(undefined);
            setIsFormOpen(true);
          }}
          className="bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Habit</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-2">
        {[
          { id: 'all', name: 'All Habits' },
          { id: 'completed', name: 'Completed Today' },
          { id: 'pending', name: 'Pending' },
          ...categories,
        ].map((filterOption) => (
          <button
            key={filterOption.id}
            onClick={() => setFilter(filterOption.id)}
            className={`px-3 py-1 rounded-full text-sm transition-colors ${
              filter === filterOption.id
                ? 'bg-primary text-primary-foreground'
                : 'bg-accent hover:bg-accent/80'
            }`}
          >
            {filterOption.name}
          </button>
        ))}
      </div>

      {/* Habits Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {filteredHabits.map((habit) => (
            <HabitCard
              key={habit.id}
              habit={habit}
              onEdit={handleEditHabit}
              onDelete={handleDeleteHabit}
              onToggleComplete={handleToggleComplete}
            />
          ))}
        </AnimatePresence>
      </div>

      {filteredHabits.length === 0 && (
        <div className="text-center py-12">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No habits found</h3>
          <p className="text-muted-foreground mb-4">
            {filter === 'all' 
              ? "Start building better habits by creating your first one!"
              : "No habits match your current filter."
            }
          </p>
          {filter === 'all' && (
            <button
              onClick={() => {
                setEditingHabit(undefined);
                setIsFormOpen(true);
              }}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Create Your First Habit
            </button>
          )}
        </div>
      )}

      {/* Habit Form Modal */}
      <HabitForm
        habit={editingHabit}
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setEditingHabit(undefined);
        }}
        onSave={handleSaveHabit}
      />
    </div>
  );
}
