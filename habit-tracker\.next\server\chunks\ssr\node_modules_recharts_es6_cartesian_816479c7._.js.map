{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/ErrorBar.js"], "sourcesContent": ["var _excluded = [\"direction\", \"width\", \"dataKey\", \"isAnimationActive\", \"animationBegin\", \"animationDuration\", \"animationEasing\"];\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Render a group of error bar\n */\nimport * as React from 'react';\nimport { Component, createContext, useContext } from 'react';\nimport { Layer } from '../container/Layer';\nimport { filterProps } from '../util/ReactUtils';\nimport { ReportErrorBarSettings, useErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { useXAxis, useYAxis } from '../hooks';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * So usually the direction is decided by the chart layout.\n * Horizontal layout means error bars are vertical means direction=y\n * Vertical layout means error bars are horizontal means direction=x\n *\n * Except! In Scatter chart, error bars can go both ways.\n *\n * So this property is only ever used in Scatter chart, and ignored elsewhere.\n */\n\n/**\n * External ErrorBar props, visible for users of the library\n */\n\n/**\n * Props after defaults, and required props have been applied.\n */\n\nfunction ErrorBarImpl(props) {\n  var {\n      direction,\n      width,\n      dataKey,\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var svgProps = filterProps(others, false);\n  var {\n    data,\n    dataPointFormatter,\n    xAxisId,\n    yAxisId,\n    errorBarOffset: offset\n  } = useErrorBarContext();\n  var xAxis = useXAxis(xAxisId);\n  var yAxis = useYAxis(yAxisId);\n  if ((xAxis === null || xAxis === void 0 ? void 0 : xAxis.scale) == null || (yAxis === null || yAxis === void 0 ? void 0 : yAxis.scale) == null || data == null) {\n    return null;\n  }\n\n  // ErrorBar requires type number XAxis, why?\n  if (direction === 'x' && xAxis.type !== 'number') {\n    return null;\n  }\n  var errorBars = data.map(entry => {\n    var {\n      x,\n      y,\n      value,\n      errorVal\n    } = dataPointFormatter(entry, dataKey, direction);\n    if (!errorVal) {\n      return null;\n    }\n    var lineCoordinates = [];\n    var lowBound, highBound;\n    if (Array.isArray(errorVal)) {\n      [lowBound, highBound] = errorVal;\n    } else {\n      lowBound = highBound = errorVal;\n    }\n    if (direction === 'x') {\n      // error bar for horizontal charts, the y is fixed, x is a range value\n      var {\n        scale\n      } = xAxis;\n      var yMid = y + offset;\n      var yMin = yMid + width;\n      var yMax = yMid - width;\n      var xMin = scale(value - lowBound);\n      var xMax = scale(value + highBound);\n\n      // the right line of |--|\n      lineCoordinates.push({\n        x1: xMax,\n        y1: yMin,\n        x2: xMax,\n        y2: yMax\n      });\n      // the middle line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMid,\n        x2: xMax,\n        y2: yMid\n      });\n      // the left line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMin,\n        x2: xMin,\n        y2: yMax\n      });\n    } else if (direction === 'y') {\n      // error bar for horizontal charts, the x is fixed, y is a range value\n      var {\n        scale: _scale\n      } = yAxis;\n      var xMid = x + offset;\n      var _xMin = xMid - width;\n      var _xMax = xMid + width;\n      var _yMin = _scale(value - lowBound);\n      var _yMax = _scale(value + highBound);\n\n      // the top line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMax,\n        x2: _xMax,\n        y2: _yMax\n      });\n      // the middle line\n      lineCoordinates.push({\n        x1: xMid,\n        y1: _yMin,\n        x2: xMid,\n        y2: _yMax\n      });\n      // the bottom line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMin,\n        x2: _xMax,\n        y2: _yMin\n      });\n    }\n    var transformOrigin = \"\".concat(x + offset, \"px \").concat(y + offset, \"px\");\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-errorBar\",\n      key: \"bar-\".concat(lineCoordinates.map(c => \"\".concat(c.x1, \"-\").concat(c.x2, \"-\").concat(c.y1, \"-\").concat(c.y2)))\n    }, svgProps), lineCoordinates.map(coordinates => {\n      var lineStyle = isAnimationActive ? {\n        transformOrigin: \"\".concat(coordinates.x1 - 5, \"px\")\n      } : undefined;\n      return /*#__PURE__*/React.createElement(Animate, {\n        from: {\n          transform: 'scaleY(0)',\n          transformOrigin\n        },\n        to: {\n          transform: 'scaleY(1)',\n          transformOrigin\n        },\n        begin: animationBegin,\n        easing: animationEasing,\n        isActive: isAnimationActive,\n        duration: animationDuration,\n        key: \"line-\".concat(coordinates.x1, \"-\").concat(coordinates.x2, \"-\").concat(coordinates.y1, \"-\").concat(coordinates.y2)\n        // @ts-expect-error TODO - fix the type error\n        ,\n        style: {\n          transformOrigin\n        }\n      }, /*#__PURE__*/React.createElement(\"line\", _extends({}, coordinates, {\n        style: lineStyle\n      })));\n    }));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-errorBars\"\n  }, errorBars);\n}\nvar ErrorBarPreferredDirection = /*#__PURE__*/createContext(undefined);\nfunction useErrorBarDirection(directionFromProps) {\n  var preferredDirection = useContext(ErrorBarPreferredDirection);\n  if (directionFromProps != null) {\n    return directionFromProps;\n  }\n  if (preferredDirection != null) {\n    return preferredDirection;\n  }\n  return 'x';\n}\nexport function SetErrorBarPreferredDirection(_ref) {\n  var {\n    direction,\n    children\n  } = _ref;\n  return /*#__PURE__*/React.createElement(ErrorBarPreferredDirection.Provider, {\n    value: direction\n  }, children);\n}\nvar errorBarDefaultProps = {\n  stroke: 'black',\n  strokeWidth: 1.5,\n  width: 5,\n  offset: 0,\n  isAnimationActive: true,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease-in-out'\n};\nfunction ErrorBarInternal(props) {\n  var realDirection = useErrorBarDirection(props.direction);\n  var {\n    width,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = resolveDefaultProps(props, errorBarDefaultProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportErrorBarSettings, {\n    dataKey: props.dataKey,\n    direction: realDirection\n  }), /*#__PURE__*/React.createElement(ErrorBarImpl, _extends({}, props, {\n    direction: realDirection,\n    width: width,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ErrorBar extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ErrorBarInternal, this.props);\n  }\n}\n_defineProperty(ErrorBar, \"defaultProps\", errorBarDefaultProps);\n_defineProperty(ErrorBar, \"displayName\", 'ErrorBar');"], "names": [], "mappings": ";;;;AAOA;;CAEC,GACD;AAEA;AACA;AACA;AACA;AACA;AACA;AAjBA,IAAI,YAAY;IAAC;IAAa;IAAS;IAAW;IAAqB;IAAkB;IAAqB;CAAkB;AAChI,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;;;;;;;;;AAatM;;;;;;;;CAQC,GAED;;CAEC,GAED;;CAEC,GAED,SAAS,aAAa,KAAK;IACzB,IAAI,EACA,SAAS,EACT,KAAK,EACL,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EAChB,GAAG,OACJ,SAAS,yBAAyB,OAAO;IAC3C,IAAI,WAAW,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;IACnC,IAAI,EACF,IAAI,EACJ,kBAAkB,EAClB,OAAO,EACP,OAAO,EACP,gBAAgB,MAAM,EACvB,GAAG,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD;IACrB,IAAI,QAAQ,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IACrB,IAAI,QAAQ,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IACrB,IAAI,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,QAAQ,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,QAAQ,QAAQ,MAAM;QAC9J,OAAO;IACT;IAEA,4CAA4C;IAC5C,IAAI,cAAc,OAAO,MAAM,IAAI,KAAK,UAAU;QAChD,OAAO;IACT;IACA,IAAI,YAAY,KAAK,GAAG,CAAC,CAAA;QACvB,IAAI,EACF,CAAC,EACD,CAAC,EACD,KAAK,EACL,QAAQ,EACT,GAAG,mBAAmB,OAAO,SAAS;QACvC,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QACA,IAAI,kBAAkB,EAAE;QACxB,IAAI,UAAU;QACd,IAAI,MAAM,OAAO,CAAC,WAAW;YAC3B,CAAC,UAAU,UAAU,GAAG;QAC1B,OAAO;YACL,WAAW,YAAY;QACzB;QACA,IAAI,cAAc,KAAK;YACrB,sEAAsE;YACtE,IAAI,EACF,KAAK,EACN,GAAG;YACJ,IAAI,OAAO,IAAI;YACf,IAAI,OAAO,OAAO;YAClB,IAAI,OAAO,OAAO;YAClB,IAAI,OAAO,MAAM,QAAQ;YACzB,IAAI,OAAO,MAAM,QAAQ;YAEzB,yBAAyB;YACzB,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YACA,0BAA0B;YAC1B,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YACA,wBAAwB;YACxB,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;QACF,OAAO,IAAI,cAAc,KAAK;YAC5B,sEAAsE;YACtE,IAAI,EACF,OAAO,MAAM,EACd,GAAG;YACJ,IAAI,OAAO,IAAI;YACf,IAAI,QAAQ,OAAO;YACnB,IAAI,QAAQ,OAAO;YACnB,IAAI,QAAQ,OAAO,QAAQ;YAC3B,IAAI,QAAQ,OAAO,QAAQ;YAE3B,eAAe;YACf,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YACA,kBAAkB;YAClB,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YACA,kBAAkB;YAClB,gBAAgB,IAAI,CAAC;gBACnB,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;QACF;QACA,IAAI,kBAAkB,GAAG,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,IAAI,QAAQ;QACtE,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;YACtD,WAAW;YACX,KAAK,OAAO,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAA,IAAK,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE;QAClH,GAAG,WAAW,gBAAgB,GAAG,CAAC,CAAA;YAChC,IAAI,YAAY,oBAAoB;gBAClC,iBAAiB,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,GAAG;YACjD,IAAI;YACJ,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAO,EAAE;gBAC/C,MAAM;oBACJ,WAAW;oBACX;gBACF;gBACA,IAAI;oBACF,WAAW;oBACX;gBACF;gBACA,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,KAAK,QAAQ,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,MAAM,CAAC,YAAY,EAAE;gBAGtH,OAAO;oBACL;gBACF;YACF,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,SAAS,CAAC,GAAG,aAAa;gBACpE,OAAO;YACT;QACF;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;QAC7C,WAAW;IACb,GAAG;AACL;AACA,IAAI,6BAA6B,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAC5D,SAAS,qBAAqB,kBAAkB;IAC9C,IAAI,qBAAqB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACpC,IAAI,sBAAsB,MAAM;QAC9B,OAAO;IACT;IACA,IAAI,sBAAsB,MAAM;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AACO,SAAS,8BAA8B,IAAI;IAChD,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2BAA2B,QAAQ,EAAE;QAC3E,OAAO;IACT,GAAG;AACL;AACA,IAAI,uBAAuB;IACzB,QAAQ;IACR,aAAa;IACb,OAAO;IACP,QAAQ;IACR,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;AACnB;AACA,SAAS,iBAAiB,KAAK;IAC7B,IAAI,gBAAgB,qBAAqB,MAAM,SAAS;IACxD,IAAI,EACF,KAAK,EACL,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EAChB,GAAG,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAC/B,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,yBAAsB,EAAE;QACrH,SAAS,MAAM,OAAO;QACtB,WAAW;IACb,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,cAAc,SAAS,CAAC,GAAG,OAAO;QACrE,WAAW;QACX,OAAO;QACP,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;IACnB;AACF;AAGO,MAAM,iBAAiB,qMAAA,CAAA,YAAS;IACrC,SAAS;QACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,kBAAkB,IAAI,CAAC,KAAK;IACtE;AACF;AACA,gBAAgB,UAAU,gBAAgB;AAC1C,gBAAgB,UAAU,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/GraphicalItemClipPath.js"], "sourcesContent": ["import * as React from 'react';\nimport { useAppSelector } from '../state/hooks';\nimport { implicitXAxis, implicitYAxis, selectXAxisSettings, selectYAxisSettings } from '../state/selectors/axisSelectors';\nimport { usePlotArea } from '../hooks';\nexport function useNeedsClip(xAxisId, yAxisId) {\n  var _xAxis$allowDataOverf, _yAxis$allowDataOverf;\n  var xAxis = useAppSelector(state => selectXAxisSettings(state, xAxisId));\n  var yAxis = useAppSelector(state => selectYAxisSettings(state, yAxisId));\n  var needClipX = (_xAxis$allowDataOverf = xAxis === null || xAxis === void 0 ? void 0 : xAxis.allowDataOverflow) !== null && _xAxis$allowDataOverf !== void 0 ? _xAxis$allowDataOverf : implicitXAxis.allowDataOverflow;\n  var needClipY = (_yAxis$allowDataOverf = yAxis === null || yAxis === void 0 ? void 0 : yAxis.allowDataOverflow) !== null && _yAxis$allowDataOverf !== void 0 ? _yAxis$allowDataOverf : implicitYAxis.allowDataOverflow;\n  var needClip = needClipX || needClipY;\n  return {\n    needClip,\n    needClipX,\n    needClipY\n  };\n}\nexport function GraphicalItemClipPath(_ref) {\n  var {\n    xAxisId,\n    yAxisId,\n    clipPathId\n  } = _ref;\n  var plotArea = usePlotArea();\n  var {\n    needClipX,\n    needClipY,\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  if (!needClip) {\n    return null;\n  }\n  var {\n    x,\n    y,\n    width,\n    height\n  } = plotArea;\n  return /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clipPath-\".concat(clipPathId)\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: needClipX ? x : x - width / 2,\n    y: needClipY ? y : y - height / 2,\n    width: needClipX ? width : width * 2,\n    height: needClipY ? height : height * 2\n  }));\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,aAAa,OAAO,EAAE,OAAO;IAC3C,IAAI,uBAAuB;IAC3B,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAC/D,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAC/D,IAAI,YAAY,CAAC,wBAAwB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,iBAAiB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,sKAAA,CAAA,gBAAa,CAAC,iBAAiB;IACtN,IAAI,YAAY,CAAC,wBAAwB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,iBAAiB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,sKAAA,CAAA,gBAAa,CAAC,iBAAiB;IACtN,IAAI,WAAW,aAAa;IAC5B,OAAO;QACL;QACA;QACA;IACF;AACF;AACO,SAAS,sBAAsB,IAAI;IACxC,IAAI,EACF,OAAO,EACP,OAAO,EACP,UAAU,EACX,GAAG;IACJ,IAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD;IACzB,IAAI,EACF,SAAS,EACT,SAAS,EACT,QAAQ,EACT,GAAG,aAAa,SAAS;IAC1B,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,IAAI,EACF,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACP,GAAG;IACJ,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;QAClD,IAAI,YAAY,MAAM,CAAC;IACzB,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1C,GAAG,YAAY,IAAI,IAAI,QAAQ;QAC/B,GAAG,YAAY,IAAI,IAAI,SAAS;QAChC,OAAO,YAAY,QAAQ,QAAQ;QACnC,QAAQ,YAAY,SAAS,SAAS;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/Bar.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onMouseLeave\", \"onClick\"],\n  _excluded2 = [\"value\", \"background\", \"tooltipPosition\"],\n  _excluded3 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Render a group of bar\n */\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { interpolateNumber, isNan, isNullish, mathSign, uniqueId } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getBaseValueOfBar, getCateCoordinateOfBar, getNormalizedStackId, getTooltipNameProp, getValueByDataKey, truncateByDomain } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { selectBarRectangles } from '../state/selectors/barSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipDataKey, selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar computeLegendPayloadFromBarData = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.fill,\n      unit\n    }\n  };\n}\nfunction BarBackground(props) {\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n    data,\n    dataKey,\n    background: backgroundFromProps,\n    allOtherBarProps\n  } = props;\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      onClick: onItemClickFromProps\n    } = allOtherBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherBarProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!backgroundFromProps || data == null) {\n    return null;\n  }\n  var backgroundProps = filterProps(backgroundFromProps, false);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    var {\n        value,\n        background: backgroundFromDataEntry,\n        tooltipPosition\n      } = entry,\n      rest = _objectWithoutProperties(entry, _excluded2);\n    if (!backgroundFromDataEntry) {\n      return null;\n    }\n\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onClick = onClickFromContext(entry, i);\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      option: backgroundFromProps,\n      isActive: String(i) === activeIndex\n    }, rest), {}, {\n      // @ts-expect-error BarRectangle props do not accept `fill` property.\n      fill: '#eee'\n    }, backgroundFromDataEntry), backgroundProps), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      dataKey,\n      index: i,\n      className: 'recharts-bar-background-rectangle'\n    });\n    return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n      key: \"background-bar-\".concat(i)\n    }, barRectangleProps));\n  }));\n}\nfunction BarRectangles(_ref) {\n  var {\n    data,\n    props,\n    showLabels\n  } = _ref;\n  var baseProps = filterProps(props, false);\n  var {\n    shape,\n    dataKey,\n    activeBar\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var activeDataKey = useAppSelector(selectActiveTooltipDataKey);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = props,\n    restOfAllOtherProps = _objectWithoutProperties(props, _excluded3);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!data) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    /*\n     * Bars support stacking, meaning that there can be multiple bars at the same x value.\n     * With Tooltip shared=false we only want to highlight the currently active Bar, not all.\n     *\n     * Also, if the tooltip is shared, we want to highlight all bars at the same x value\n     * regardless of the dataKey.\n     *\n     * With shared Tooltip, the activeDataKey is undefined.\n     */\n    var isActive = activeBar && String(i) === activeIndex && (activeDataKey == null || dataKey === activeDataKey);\n    var option = isActive ? activeBar : shape;\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n      isActive,\n      option,\n      index: i,\n      dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-bar-rectangle\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onClick: onClickFromContext(entry, i)\n      // https://github.com/recharts/recharts/issues/5415\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(BarRectangle, barRectangleProps));\n  }), showLabels && LabelList.renderCallByParent(props, data));\n}\nfunction RectanglesWithAnimation(_ref2) {\n  var {\n    props,\n    previousRectanglesRef\n  } = _ref2;\n  var {\n    data,\n    layout,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevData = previousRectanglesRef.current;\n  var animationId = useAnimationId(props, 'recharts-bar-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : data.map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorX = interpolateNumber(prev.x, entry.x);\n        var interpolatorY = interpolateNumber(prev.y, entry.y);\n        var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n        var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: interpolatorX(t),\n          y: interpolatorY(t),\n          width: interpolatorWidth(t),\n          height: interpolatorHeight(t)\n        });\n      }\n      if (layout === 'horizontal') {\n        var _interpolatorHeight = interpolateNumber(0, entry.height);\n        var h = _interpolatorHeight(t);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          y: entry.y + entry.height - h,\n          height: h\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.width);\n      var w = interpolator(t);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        width: w\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousRectanglesRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(BarRectangles, {\n      props: props,\n      data: stepData,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderRectangles(props) {\n  var {\n    data,\n    isAnimationActive\n  } = props;\n  var previousRectanglesRef = useRef(null);\n  if (isAnimationActive && data && data.length && (previousRectanglesRef.current == null || previousRectanglesRef.current !== data)) {\n    return /*#__PURE__*/React.createElement(RectanglesWithAnimation, {\n      previousRectanglesRef: previousRectanglesRef,\n      props: props\n    });\n  }\n  return /*#__PURE__*/React.createElement(BarRectangles, {\n    props: props,\n    data: data,\n    showLabels: true\n  });\n}\nvar defaultMinPointSize = 0;\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  /**\n   * if the value coming from `selectBarRectangles` is an array then this is a stacked bar chart.\n   * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n   * */\n  var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nclass BarWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-bar-'));\n  }\n  render() {\n    var {\n      hide,\n      data,\n      dataKey,\n      className,\n      xAxisId,\n      yAxisId,\n      needClip,\n      background,\n      id,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-bar', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    })), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-bar-rectangles\",\n      clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n    }, /*#__PURE__*/React.createElement(BarBackground, {\n      data: data,\n      dataKey: dataKey,\n      background: background,\n      allOtherBarProps: this.props\n    }), /*#__PURE__*/React.createElement(RenderRectangles, this.props)), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, this.props.children));\n  }\n}\nvar defaultBarProps = {\n  activeBar: false,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'rect',\n  minPointSize: defaultMinPointSize,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction BarImpl(props) {\n  var {\n    xAxisId,\n    yAxisId,\n    hide,\n    legendType,\n    minPointSize,\n    activeBar,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    isAnimationActive\n  } = resolveDefaultProps(props, defaultBarProps);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var barSettings = useMemo(() => ({\n    barSize: props.barSize,\n    data: undefined,\n    dataKey: props.dataKey,\n    maxBarSize: props.maxBarSize,\n    minPointSize,\n    stackId: getNormalizedStackId(props.stackId)\n  }), [props.barSize, props.dataKey, props.maxBarSize, minPointSize, props.stackId]);\n  var cells = findAllByType(props.children, Cell);\n  var rects = useAppSelector(state => selectBarRectangles(state, xAxisId, yAxisId, isPanorama, barSettings, cells));\n  if (layout !== 'vertical' && layout !== 'horizontal') {\n    return null;\n  }\n  var errorBarOffset;\n  var firstDataPoint = rects === null || rects === void 0 ? void 0 : rects[0];\n  if (firstDataPoint == null || firstDataPoint.height == null || firstDataPoint.width == null) {\n    errorBarOffset = 0;\n  } else {\n    errorBarOffset = layout === 'vertical' ? firstDataPoint.height / 2 : firstDataPoint.width / 2;\n  }\n  return /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: rects,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: errorBarOffset\n  }, /*#__PURE__*/React.createElement(BarWithState, _extends({}, props, {\n    layout: layout,\n    needClip: needClip,\n    data: rects,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    hide: hide,\n    legendType: legendType,\n    minPointSize: minPointSize,\n    activeBar: activeBar,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive\n  })));\n}\nexport function computeBarRectangles(_ref4) {\n  var {\n    layout,\n    barSettings: {\n      dataKey,\n      minPointSize: minPointSizeProp\n    },\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  } = _ref4;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error this assumes that the domain is always numeric, but doesn't check for it\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis\n  });\n  return displayedData.map((entry, index) => {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      // we don't need to use dataStartIndex here, because stackedData is already sliced from the selector\n      value = truncateByDomain(stackedData[index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, defaultMinPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref5;\n      var [baseValueScale, currentValueScale] = [yAxis.scale(value[0]), yAxis.scale(value[1])];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      y = (_ref5 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref5 !== void 0 ? _ref5 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = isNan(computedHeight) ? 0 : computedHeight;\n      background = {\n        x,\n        y: offset.top,\n        width,\n        height: offset.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var [_baseValueScale, _currentValueScale] = [xAxis.scale(value[0]), xAxis.scale(value[1])];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: offset.left,\n        y,\n        width: offset.width,\n        height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    var barRectangleItem = _objectSpread(_objectSpread({}, entry), {}, {\n      x,\n      y,\n      width,\n      height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background,\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    }, cells && cells[index] && cells[index].props);\n    return barRectangleItem;\n  });\n}\nexport class Bar extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"bar\"\n      // Bar does not allow setting data directly on the graphical item (why?)\n      ,\n      data: null,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: this.props.barSize\n    }, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromBarData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(BarImpl, this.props));\n  }\n}\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", defaultBarProps);"], "names": [], "mappings": ";;;;AAWA;;CAEC,GACD,gDAAgD;AAChD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzCA,IAAI,YAAY;IAAC;IAAgB;IAAgB;CAAU,EACzD,aAAa;IAAC;IAAS;IAAc;CAAkB,EACvD,aAAa;IAAC;IAAgB;IAAW;CAAe;AAC1D,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCtM,IAAI,kCAAkC,CAAA;IACpC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,IAAI,EACL,GAAG;IACJ,OAAO;QAAC;YACN,UAAU;YACV;YACA,MAAM;YACN,OAAO;YACP,OAAO,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAChC,SAAS;QACX;KAAE;AACJ;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,EACF,OAAO,EACP,MAAM,EACN,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACL,GAAG;IACJ,OAAO;QACL,mBAAmB;QACnB,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;YACA,SAAS;YACT,MAAM,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC/B;YACA,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,IAAI;YACjB;QACF;IACF;AACF;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,cAAc,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,2BAAwB;IACzD,IAAI,EACF,IAAI,EACJ,OAAO,EACP,YAAY,mBAAmB,EAC/B,gBAAgB,EACjB,GAAG;IACJ,IAAI,EACA,cAAc,qBAAqB,EACnC,cAAc,qBAAqB,EACnC,SAAS,oBAAoB,EAC9B,GAAG,kBACJ,sBAAsB,yBAAyB,kBAAkB;IACnE,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,uBAAuB;IAC/E,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE;IACxD,IAAI,qBAAqB,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,sBAAsB;IACzE,IAAI,CAAC,uBAAuB,QAAQ,MAAM;QACxC,OAAO;IACT;IACA,IAAI,kBAAkB,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,qBAAqB;IACvD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO;QAC7E,IAAI,EACA,KAAK,EACL,YAAY,uBAAuB,EACnC,eAAe,EAChB,GAAG,OACJ,OAAO,yBAAyB,OAAO;QACzC,IAAI,CAAC,yBAAyB;YAC5B,OAAO;QACT;QAEA,0HAA0H;QAC1H,IAAI,eAAe,wBAAwB,OAAO;QAClD,0HAA0H;QAC1H,IAAI,eAAe,wBAAwB,OAAO;QAClD,0HAA0H;QAC1H,IAAI,UAAU,mBAAmB,OAAO;QACxC,IAAI,oBAAoB,cAAc,cAAc,cAAc,cAAc,cAAc;YAC5F,QAAQ;YACR,UAAU,OAAO,OAAO;QAC1B,GAAG,OAAO,CAAC,GAAG;YACZ,qEAAqE;YACrE,MAAM;QACR,GAAG,0BAA0B,kBAAkB,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,OAAO,KAAK,CAAC,GAAG;YACrG;YACA;YACA;YACA;YACA,OAAO;YACP,WAAW;QACb;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mJAAA,CAAA,eAAY,EAAE,SAAS;YAC7D,KAAK,kBAAkB,MAAM,CAAC;QAChC,GAAG;IACL;AACF;AACA,SAAS,cAAc,IAAI;IACzB,IAAI,EACF,IAAI,EACJ,KAAK,EACL,UAAU,EACX,GAAG;IACJ,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACnC,IAAI,EACF,KAAK,EACL,OAAO,EACP,SAAS,EACV,GAAG;IACJ,IAAI,cAAc,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,2BAAwB;IACzD,IAAI,gBAAgB,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,6BAA0B;IAC7D,IAAI,EACA,cAAc,qBAAqB,EACnC,SAAS,oBAAoB,EAC7B,cAAc,qBAAqB,EACpC,GAAG,OACJ,sBAAsB,yBAAyB,OAAO;IACxD,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,uBAAuB;IAC/E,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE;IACxD,IAAI,qBAAqB,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,sBAAsB;IACzE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO;QAC7E;;;;;;;;KAQC,GACD,IAAI,WAAW,aAAa,OAAO,OAAO,eAAe,CAAC,iBAAiB,QAAQ,YAAY,aAAa;QAC5G,IAAI,SAAS,WAAW,YAAY;QACpC,IAAI,oBAAoB,cAAc,cAAc,cAAc,CAAC,GAAG,YAAY,QAAQ,CAAC,GAAG;YAC5F;YACA;YACA,OAAO;YACP;QACF;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;YACtD,WAAW;QACb,GAAG,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,OAAO,IAAI;YACpD,0HAA0H;YAC1H,cAAc,wBAAwB,OAAO;YAG7C,cAAc,wBAAwB,OAAO;YAG7C,SAAS,mBAAmB,OAAO;YAInC,KAAK,aAAa,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,KAAK,MAAM,CAAC;QACxO,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mJAAA,CAAA,eAAY,EAAE;IACrD,IAAI,cAAc,yJAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,OAAO;AACxD;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,EACF,KAAK,EACL,qBAAqB,EACtB,GAAG;IACJ,IAAI,EACF,IAAI,EACJ,MAAM,EACN,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,cAAc,EACd,gBAAgB,EACjB,GAAG;IACJ,IAAI,WAAW,sBAAsB,OAAO;IAC5C,IAAI,cAAc,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IACxC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,OAAO,mBAAmB,YAAY;YACxC;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAe;IACnB,IAAI,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,OAAO,qBAAqB,YAAY;YAC1C;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAiB;IACrB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAO,EAAE;QAC/C,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,MAAM;YACJ,GAAG;QACL;QACA,IAAI;YACF,GAAG;QACL;QACA,gBAAgB;QAChB,kBAAkB;QAClB,KAAK;IACP,GAAG,CAAA;QACD,IAAI,EACF,CAAC,EACF,GAAG;QACJ,IAAI,WAAW,MAAM,IAAI,OAAO,KAAK,GAAG,CAAC,CAAC,OAAO;YAC/C,IAAI,OAAO,YAAY,QAAQ,CAAC,MAAM;YACtC,IAAI,MAAM;gBACR,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC;gBACrD,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC;gBACrD,IAAI,oBAAoB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK,EAAE,MAAM,KAAK;gBACjE,IAAI,qBAAqB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM;gBACpE,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;oBACjD,GAAG,cAAc;oBACjB,GAAG,cAAc;oBACjB,OAAO,kBAAkB;oBACzB,QAAQ,mBAAmB;gBAC7B;YACF;YACA,IAAI,WAAW,cAAc;gBAC3B,IAAI,sBAAsB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,MAAM,MAAM;gBAC3D,IAAI,IAAI,oBAAoB;gBAC5B,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;oBACjD,GAAG,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG;oBAC5B,QAAQ;gBACV;YACF;YACA,IAAI,eAAe,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,MAAM,KAAK;YACnD,IAAI,IAAI,aAAa;YACrB,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACjD,OAAO;YACT;QACF;QACA,IAAI,IAAI,GAAG;YACT,6CAA6C;YAC7C,sBAAsB,OAAO,GAAG;QAClC;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe;YACnG,OAAO;YACP,MAAM;YACN,YAAY,CAAC;QACf;IACF;AACF;AACA,SAAS,iBAAiB,KAAK;IAC7B,IAAI,EACF,IAAI,EACJ,iBAAiB,EAClB,GAAG;IACJ,IAAI,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnC,IAAI,qBAAqB,QAAQ,KAAK,MAAM,IAAI,CAAC,sBAAsB,OAAO,IAAI,QAAQ,sBAAsB,OAAO,KAAK,IAAI,GAAG;QACjI,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,yBAAyB;YAC/D,uBAAuB;YACvB,OAAO;QACT;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe;QACrD,OAAO;QACP,MAAM;QACN,YAAY;IACd;AACF;AACA,IAAI,sBAAsB;AAC1B,IAAI,6BAA6B,CAAC,WAAW;IAC3C;;;KAGG,GACH,IAAI,QAAQ,MAAM,OAAO,CAAC,UAAU,KAAK,IAAI,UAAU,KAAK,CAAC,EAAE,GAAG,UAAU,KAAK;IACjF,OAAO;QACL,GAAG,UAAU,CAAC;QACd,GAAG,UAAU,CAAC;QACd;QACA,uEAAuE;QACvE,UAAU,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;IACzC;AACF;AACA,MAAM,qBAAqB,qMAAA,CAAA,gBAAa;IACtC,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;IACvC;IACA,SAAS;QACP,IAAI,EACF,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,EAAE,EACF,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,MAAM;YACR,OAAO;QACT;QACA,IAAI,aAAa,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB;QACtC,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,IAAI,CAAC,EAAE,GAAG;QAC3C,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YAC7C,WAAW;QACb,GAAG,YAAY,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qKAAA,CAAA,wBAAqB,EAAE;YACpH,YAAY;YACZ,SAAS;YACT,SAAS;QACX,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YAC3C,WAAW;YACX,UAAU,WAAW,iBAAiB,MAAM,CAAC,YAAY,OAAO;QAClE,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe;YACjD,MAAM;YACN,SAAS;YACT,YAAY;YACZ,kBAAkB,IAAI,CAAC,KAAK;QAC9B,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,kBAAkB,IAAI,CAAC,KAAK,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,wJAAA,CAAA,gCAA6B,EAAE;YACnI,WAAW,WAAW,eAAe,MAAM;QAC7C,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;IACxB;AACF;AACA,IAAI,kBAAkB;IACpB,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;IACjB,MAAM;IACN,mBAAmB,CAAC,iJAAA,CAAA,SAAM,CAAC,KAAK;IAChC,YAAY;IACZ,cAAc;IACd,SAAS;IACT,SAAS;AACX;AACA,SAAS,QAAQ,KAAK;IACpB,IAAI,EACF,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,iBAAiB,EAClB,GAAG,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAC/B,IAAI,EACF,QAAQ,EACT,GAAG,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,SAAS;IAC1B,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC1B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC/B,SAAS,MAAM,OAAO;YACtB,MAAM;YACN,SAAS,MAAM,OAAO;YACtB,YAAY,MAAM,UAAU;YAC5B;YACA,SAAS,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,OAAO;QAC7C,CAAC,GAAG;QAAC,MAAM,OAAO;QAAE,MAAM,OAAO;QAAE,MAAM,UAAU;QAAE;QAAc,MAAM,OAAO;KAAC;IACjF,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ,EAAE,oJAAA,CAAA,OAAI;IAC9C,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS,YAAY,aAAa;IAC1G,IAAI,WAAW,cAAc,WAAW,cAAc;QACpD,OAAO;IACT;IACA,IAAI;IACJ,IAAI,iBAAiB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE;IAC3E,IAAI,kBAAkB,QAAQ,eAAe,MAAM,IAAI,QAAQ,eAAe,KAAK,IAAI,MAAM;QAC3F,iBAAiB;IACnB,OAAO;QACL,iBAAiB,WAAW,aAAa,eAAe,MAAM,GAAG,IAAI,eAAe,KAAK,GAAG;IAC9F;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,qBAAkB,EAAE;QAC1D,SAAS;QACT,SAAS;QACT,MAAM;QACN,oBAAoB;QACpB,gBAAgB;IAClB,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,cAAc,SAAS,CAAC,GAAG,OAAO;QACpE,QAAQ;QACR,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,MAAM;QACN,YAAY;QACZ,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,mBAAmB;IACrB;AACF;AACO,SAAS,qBAAqB,KAAK;IACxC,IAAI,EACF,MAAM,EACN,aAAa,EACX,OAAO,EACP,cAAc,gBAAgB,EAC/B,EACD,GAAG,EACH,QAAQ,EACR,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,EACV,WAAW,EACX,aAAa,EACb,MAAM,EACN,KAAK,EACN,GAAG;IACJ,IAAI,cAAc,WAAW,eAAe,QAAQ;IACpD,4FAA4F;IAC5F,IAAI,gBAAgB,cAAc,YAAY,KAAK,CAAC,MAAM,KAAK;IAC/D,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE;QAChC;IACF;IACA,OAAO,cAAc,GAAG,CAAC,CAAC,OAAO;QAC/B,IAAI,OAAO,GAAG,GAAG,OAAO,QAAQ;QAChC,IAAI,aAAa;YACf,oGAAoG;YACpG,QAAQ,CAAA,GAAA,qJAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE;QAC/C,OAAO;YACL,QAAQ,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YACjC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,QAAQ;oBAAC;oBAAW;iBAAM;YAC5B;QACF;QACA,IAAI,eAAe,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB,qBAAqB,KAAK,CAAC,EAAE,EAAE;QACzF,IAAI,WAAW,cAAc;YAC3B,IAAI;YACJ,IAAI,CAAC,gBAAgB,kBAAkB,GAAG;gBAAC,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;gBAAG,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;aAAE;YACxF,IAAI,CAAA,GAAA,qJAAA,CAAA,yBAAsB,AAAD,EAAE;gBACzB,MAAM;gBACN,OAAO;gBACP;gBACA,QAAQ,IAAI,MAAM;gBAClB;gBACA;YACF;YACA,IAAI,CAAC,QAAQ,sBAAsB,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,cAAc,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ;YACrJ,QAAQ,IAAI,IAAI;YAChB,IAAI,iBAAiB,iBAAiB;YACtC,SAAS,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,kBAAkB,IAAI;YACrC,aAAa;gBACX;gBACA,GAAG,OAAO,GAAG;gBACb;gBACA,QAAQ,OAAO,MAAM;YACvB;YACA,IAAI,KAAK,GAAG,CAAC,gBAAgB,KAAK,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,eAAe;gBAC3E,IAAI,QAAQ,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,KAAK,GAAG,CAAC,OAAO;gBACzF,KAAK;gBACL,UAAU;YACZ;QACF,OAAO;YACL,IAAI,CAAC,iBAAiB,mBAAmB,GAAG;gBAAC,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;gBAAG,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;aAAE;YAC1F,IAAI;YACJ,IAAI,CAAA,GAAA,qJAAA,CAAA,yBAAsB,AAAD,EAAE;gBACzB,MAAM;gBACN,OAAO;gBACP;gBACA,QAAQ,IAAI,MAAM;gBAClB;gBACA;YACF;YACA,QAAQ,qBAAqB;YAC7B,SAAS,IAAI,IAAI;YACjB,aAAa;gBACX,GAAG,OAAO,IAAI;gBACd;gBACA,OAAO,OAAO,KAAK;gBACnB;YACF;YACA,IAAI,KAAK,GAAG,CAAC,gBAAgB,KAAK,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,eAAe;gBAC1E,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,KAAK,GAAG,CAAC,MAAM;gBACxF,SAAS;YACX;QACF;QACA,IAAI,mBAAmB,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;YACjE;YACA;YACA;YACA;YACA,OAAO,cAAc,QAAQ,KAAK,CAAC,EAAE;YACrC,SAAS;YACT;YACA,iBAAiB;gBACf,GAAG,IAAI,QAAQ;gBACf,GAAG,IAAI,SAAS;YAClB;QACF,GAAG,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK;QAC9C,OAAO;IACT;AACF;AACO,MAAM,YAAY,qMAAA,CAAA,gBAAa;IACpC,SAAS;QACP,mGAAmG;QACnG,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,gCAA6B,EAAE;YACrE,MAAM;YAGN,MAAM;YACN,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;QAC7B,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,YAAS,EAAE,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,mBAAgB,EAAE;YACvG,eAAe,gCAAgC,IAAI,CAAC,KAAK;QAC3D,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mKAAA,CAAA,0BAAuB,EAAE;YAC5D,IAAI;YACJ,MAAM,IAAI,CAAC,KAAK;QAClB,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS,IAAI,CAAC,KAAK;IAC1D;AACF;AACA,gBAAgB,KAAK,eAAe;AACpC,gBAAgB,KAAK,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/getEquidistantTicks.js"], "sourcesContent": ["import { isVisible } from '../util/TickUtils';\nimport { getEveryNthWithCondition } from '../util/getEveryNthWithCondition';\nexport function getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap) {\n  // If the ticks are readonly, then the slice might not be necessary\n  var result = (ticks || []).slice();\n  var {\n    start: initialStart,\n    end\n  } = boundaries;\n  var index = 0;\n  // Premature optimisation idea 1: Estimate a lower bound, and start from there.\n  // For now, start from every tick\n  var stepsize = 1;\n  var start = initialStart;\n  var _loop = function _loop() {\n      // Given stepsize, evaluate whether every stepsize-th tick can be shown.\n      // If it can not, then increase the stepsize by 1, and try again.\n\n      var entry = ticks === null || ticks === void 0 ? void 0 : ticks[index];\n\n      // Break condition - If we have evaluated all the ticks, then we are done.\n      if (entry === undefined) {\n        return {\n          v: getEveryNthWithCondition(ticks, stepsize)\n        };\n      }\n\n      // Check if the element collides with the next element\n      var i = index;\n      var size;\n      var getSize = () => {\n        if (size === undefined) {\n          size = getTickSize(entry, i);\n        }\n        return size;\n      };\n      var tickCoord = entry.coordinate;\n      // We will always show the first tick.\n      var isShow = index === 0 || isVisible(sign, tickCoord, getSize, start, end);\n      if (!isShow) {\n        // Start all over with a larger stepsize\n        index = 0;\n        start = initialStart;\n        stepsize += 1;\n      }\n      if (isShow) {\n        // If it can be shown, update the start\n        start = tickCoord + sign * (getSize() / 2 + minTickGap);\n        index += stepsize;\n      }\n    },\n    _ret;\n  while (stepsize <= result.length) {\n    _ret = _loop();\n    if (_ret) return _ret.v;\n  }\n  return [];\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,oBAAoB,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU;IAClF,mEAAmE;IACnE,IAAI,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK;IAChC,IAAI,EACF,OAAO,YAAY,EACnB,GAAG,EACJ,GAAG;IACJ,IAAI,QAAQ;IACZ,+EAA+E;IAC/E,iCAAiC;IACjC,IAAI,WAAW;IACf,IAAI,QAAQ;IACZ,IAAI,QAAQ,SAAS;QACjB,wEAAwE;QACxE,iEAAiE;QAEjE,IAAI,QAAQ,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM;QAEtE,0EAA0E;QAC1E,IAAI,UAAU,WAAW;YACvB,OAAO;gBACL,GAAG,CAAA,GAAA,mKAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO;YACrC;QACF;QAEA,sDAAsD;QACtD,IAAI,IAAI;QACR,IAAI;QACJ,IAAI,UAAU;YACZ,IAAI,SAAS,WAAW;gBACtB,OAAO,YAAY,OAAO;YAC5B;YACA,OAAO;QACT;QACA,IAAI,YAAY,MAAM,UAAU;QAChC,sCAAsC;QACtC,IAAI,SAAS,UAAU,KAAK,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,WAAW,SAAS,OAAO;QACvE,IAAI,CAAC,QAAQ;YACX,wCAAwC;YACxC,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,IAAI,QAAQ;YACV,uCAAuC;YACvC,QAAQ,YAAY,OAAO,CAAC,YAAY,IAAI,UAAU;YACtD,SAAS;QACX;IACF,GACA;IACF,MAAO,YAAY,OAAO,MAAM,CAAE;QAChC,OAAO;QACP,IAAI,MAAM,OAAO,KAAK,CAAC;IACzB;IACA,OAAO,EAAE;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/getTicks.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { mathSign, isNumber } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { isVisible, getTickBoundaries, getNumberIntervalTicks, getAngledTickWidth } from '../util/TickUtils';\nimport { getEquidistantTicks } from './getEquidistantTicks';\nfunction getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start\n  } = boundaries;\n  var {\n    end\n  } = boundaries;\n  var _loop = function _loop(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === len - 1) {\n      var gap = sign * (entry.coordinate + sign * getSize() / 2 - end);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap > 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      end = entry.tickCoord - sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = len - 1; i >= 0; i--) {\n    _loop(i);\n  }\n  return result;\n}\nfunction getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, preserveEnd) {\n  // This method is mutating the array so clone is indeed necessary here\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start,\n    end\n  } = boundaries;\n  if (preserveEnd) {\n    // Try to guarantee the tail to be displayed\n    var tail = ticks[len - 1];\n    var tailSize = getTickSize(tail, len - 1);\n    var tailGap = sign * (tail.coordinate + sign * tailSize / 2 - end);\n    result[len - 1] = tail = _objectSpread(_objectSpread({}, tail), {}, {\n      tickCoord: tailGap > 0 ? tail.coordinate - tailGap * sign : tail.coordinate\n    });\n    var isTailShow = isVisible(sign, tail.tickCoord, () => tailSize, start, end);\n    if (isTailShow) {\n      end = tail.tickCoord - sign * (tailSize / 2 + minTickGap);\n      result[len - 1] = _objectSpread(_objectSpread({}, tail), {}, {\n        isShow: true\n      });\n    }\n  }\n  var count = preserveEnd ? len - 1 : len;\n  var _loop2 = function _loop2(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === 0) {\n      var gap = sign * (entry.coordinate - sign * getSize() / 2 - start);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap < 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      start = entry.tickCoord + sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = 0; i < count; i++) {\n    _loop2(i);\n  }\n  return result;\n}\nexport function getTicks(props, fontSize, letterSpacing) {\n  var {\n    tick,\n    ticks,\n    viewBox,\n    minTickGap,\n    orientation,\n    interval,\n    tickFormatter,\n    unit,\n    angle\n  } = props;\n  if (!ticks || !ticks.length || !tick) {\n    return [];\n  }\n  if (isNumber(interval) || Global.isSsr) {\n    var _getNumberIntervalTic;\n    return (_getNumberIntervalTic = getNumberIntervalTicks(ticks, isNumber(interval) ? interval : 0)) !== null && _getNumberIntervalTic !== void 0 ? _getNumberIntervalTic : [];\n  }\n  var candidates = [];\n  var sizeKey = orientation === 'top' || orientation === 'bottom' ? 'width' : 'height';\n  var unitSize = unit && sizeKey === 'width' ? getStringSize(unit, {\n    fontSize,\n    letterSpacing\n  }) : {\n    width: 0,\n    height: 0\n  };\n  var getTickSize = (content, index) => {\n    var value = typeof tickFormatter === 'function' ? tickFormatter(content.value, index) : content.value;\n    // Recharts only supports angles when sizeKey === 'width'\n    return sizeKey === 'width' ? getAngledTickWidth(getStringSize(value, {\n      fontSize,\n      letterSpacing\n    }), unitSize, angle) : getStringSize(value, {\n      fontSize,\n      letterSpacing\n    })[sizeKey];\n  };\n  var sign = ticks.length >= 2 ? mathSign(ticks[1].coordinate - ticks[0].coordinate) : 1;\n  var boundaries = getTickBoundaries(viewBox, sign, sizeKey);\n  if (interval === 'equidistantPreserveStart') {\n    return getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  if (interval === 'preserveStart' || interval === 'preserveStartEnd') {\n    candidates = getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, interval === 'preserveStartEnd');\n  } else {\n    candidates = getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  return candidates.filter(entry => entry.isShow);\n}"], "names": [], "mappings": ";;;AAKA;AACA;AACA;AACA;AACA;AATA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;AAMvT,SAAS,YAAY,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU;IACnE,IAAI,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK;IAChC,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,EACF,KAAK,EACN,GAAG;IACJ,IAAI,EACF,GAAG,EACJ,GAAG;IACJ,IAAI,QAAQ,SAAS,MAAM,CAAC;QAC1B,IAAI,QAAQ,MAAM,CAAC,EAAE;QACrB,IAAI;QACJ,IAAI,UAAU;YACZ,IAAI,SAAS,WAAW;gBACtB,OAAO,YAAY,OAAO;YAC5B;YACA,OAAO;QACT;QACA,IAAI,MAAM,MAAM,GAAG;YACjB,IAAI,MAAM,OAAO,CAAC,MAAM,UAAU,GAAG,OAAO,YAAY,IAAI,GAAG;YAC/D,MAAM,CAAC,EAAE,GAAG,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC9D,WAAW,MAAM,IAAI,MAAM,UAAU,GAAG,MAAM,OAAO,MAAM,UAAU;YACvE;QACF,OAAO;YACL,MAAM,CAAC,EAAE,GAAG,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC9D,WAAW,MAAM,UAAU;YAC7B;QACF;QACA,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,MAAM,SAAS,EAAE,SAAS,OAAO;QAC9D,IAAI,QAAQ;YACV,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,IAAI,UAAU;YAC1D,MAAM,CAAC,EAAE,GAAG,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACtD,QAAQ;YACV;QACF;IACF;IACA,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,IAAK;QACjC,MAAM;IACR;IACA,OAAO;AACT;AACA,SAAS,cAAc,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW;IAClF,sEAAsE;IACtE,IAAI,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK;IAChC,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,EACF,KAAK,EACL,GAAG,EACJ,GAAG;IACJ,IAAI,aAAa;QACf,4CAA4C;QAC5C,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE;QACzB,IAAI,WAAW,YAAY,MAAM,MAAM;QACvC,IAAI,UAAU,OAAO,CAAC,KAAK,UAAU,GAAG,OAAO,WAAW,IAAI,GAAG;QACjE,MAAM,CAAC,MAAM,EAAE,GAAG,OAAO,cAAc,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG;YAClE,WAAW,UAAU,IAAI,KAAK,UAAU,GAAG,UAAU,OAAO,KAAK,UAAU;QAC7E;QACA,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,SAAS,EAAE,IAAM,UAAU,OAAO;QACxE,IAAI,YAAY;YACd,MAAM,KAAK,SAAS,GAAG,OAAO,CAAC,WAAW,IAAI,UAAU;YACxD,MAAM,CAAC,MAAM,EAAE,GAAG,cAAc,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG;gBAC3D,QAAQ;YACV;QACF;IACF;IACA,IAAI,QAAQ,cAAc,MAAM,IAAI;IACpC,IAAI,SAAS,SAAS,OAAO,CAAC;QAC5B,IAAI,QAAQ,MAAM,CAAC,EAAE;QACrB,IAAI;QACJ,IAAI,UAAU;YACZ,IAAI,SAAS,WAAW;gBACtB,OAAO,YAAY,OAAO;YAC5B;YACA,OAAO;QACT;QACA,IAAI,MAAM,GAAG;YACX,IAAI,MAAM,OAAO,CAAC,MAAM,UAAU,GAAG,OAAO,YAAY,IAAI,KAAK;YACjE,MAAM,CAAC,EAAE,GAAG,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC9D,WAAW,MAAM,IAAI,MAAM,UAAU,GAAG,MAAM,OAAO,MAAM,UAAU;YACvE;QACF,OAAO;YACL,MAAM,CAAC,EAAE,GAAG,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC9D,WAAW,MAAM,UAAU;YAC7B;QACF;QACA,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,MAAM,SAAS,EAAE,SAAS,OAAO;QAC9D,IAAI,QAAQ;YACV,QAAQ,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,IAAI,UAAU;YAC5D,MAAM,CAAC,EAAE,GAAG,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACtD,QAAQ;YACV;QACF;IACF;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AACO,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa;IACrD,IAAI,EACF,IAAI,EACJ,KAAK,EACL,OAAO,EACP,UAAU,EACV,WAAW,EACX,QAAQ,EACR,aAAa,EACb,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM;QACpC,OAAO,EAAE;IACX;IACA,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,iJAAA,CAAA,SAAM,CAAC,KAAK,EAAE;QACtC,IAAI;QACJ,OAAO,CAAC,wBAAwB,CAAA,GAAA,oJAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,WAAW,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,EAAE;IAC7K;IACA,IAAI,aAAa,EAAE;IACnB,IAAI,UAAU,gBAAgB,SAAS,gBAAgB,WAAW,UAAU;IAC5E,IAAI,WAAW,QAAQ,YAAY,UAAU,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;QAC/D;QACA;IACF,KAAK;QACH,OAAO;QACP,QAAQ;IACV;IACA,IAAI,cAAc,CAAC,SAAS;QAC1B,IAAI,QAAQ,OAAO,kBAAkB,aAAa,cAAc,QAAQ,KAAK,EAAE,SAAS,QAAQ,KAAK;QACrG,yDAAyD;QACzD,OAAO,YAAY,UAAU,CAAA,GAAA,oJAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACnE;YACA;QACF,IAAI,UAAU,SAAS,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YAC1C;YACA;QACF,EAAE,CAAC,QAAQ;IACb;IACA,IAAI,OAAO,MAAM,MAAM,IAAI,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,IAAI;IACrF,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,MAAM;IAClD,IAAI,aAAa,4BAA4B;QAC3C,OAAO,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,YAAY,aAAa,OAAO;IACnE;IACA,IAAI,aAAa,mBAAmB,aAAa,oBAAoB;QACnE,aAAa,cAAc,MAAM,YAAY,aAAa,OAAO,YAAY,aAAa;IAC5F,OAAO;QACL,aAAa,YAAY,MAAM,YAAY,aAAa,OAAO;IACjE;IACA,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/CartesianAxis.js"], "sourcesContent": ["var _excluded = [\"viewBox\"],\n  _excluded2 = [\"viewBox\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Cartesian Axis\n */\nimport * as React from 'react';\nimport { Component } from 'react';\nimport get from 'es-toolkit/compat/get';\nimport { clsx } from 'clsx';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { isNumber } from '../util/DataUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTicks } from './getTicks';\n\n/** The orientation of the axis in correspondence to the chart */\n\n/** A unit to be appended to a value */\n\n/** The formatter function of tick */\n\n/*\n * `viewBox` and `scale` are SVG attributes.\n * Recharts however - unfortunately - has its own attributes named `viewBox` and `scale`\n * that are completely different data shape and different purpose.\n */\n\nexport class CartesianAxis extends Component {\n  constructor(props) {\n    super(props);\n    this.tickRefs = /*#__PURE__*/React.createRef();\n    this.tickRefs.current = [];\n    this.state = {\n      fontSize: '',\n      letterSpacing: ''\n    };\n  }\n  shouldComponentUpdate(_ref, nextState) {\n    var {\n        viewBox\n      } = _ref,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n    // props.viewBox is sometimes generated every time -\n    // check that specially as object equality is likely to fail\n    var _this$props = this.props,\n      {\n        viewBox: viewBoxOld\n      } = _this$props,\n      restPropsOld = _objectWithoutProperties(_this$props, _excluded2);\n    return !shallowEqual(viewBox, viewBoxOld) || !shallowEqual(restProps, restPropsOld) || !shallowEqual(nextState, this.state);\n  }\n\n  /**\n   * Calculate the coordinates of endpoints in ticks\n   * @param  data The data of a simple tick\n   * @return (x1, y1): The coordinate of endpoint close to tick text\n   *  (x2, y2): The coordinate of endpoint close to axis\n   */\n  getTickLineCoord(data) {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      tickSize,\n      mirror,\n      tickMargin\n    } = this.props;\n    var x1, x2, y1, y2, tx, ty;\n    var sign = mirror ? -1 : 1;\n    var finalTickSize = data.tickSize || tickSize;\n    var tickCoord = isNumber(data.tickCoord) ? data.tickCoord : data.coordinate;\n    switch (orientation) {\n      case 'top':\n        x1 = x2 = data.coordinate;\n        y2 = y + +!mirror * height;\n        y1 = y2 - sign * finalTickSize;\n        ty = y1 - sign * tickMargin;\n        tx = tickCoord;\n        break;\n      case 'left':\n        y1 = y2 = data.coordinate;\n        x2 = x + +!mirror * width;\n        x1 = x2 - sign * finalTickSize;\n        tx = x1 - sign * tickMargin;\n        ty = tickCoord;\n        break;\n      case 'right':\n        y1 = y2 = data.coordinate;\n        x2 = x + +mirror * width;\n        x1 = x2 + sign * finalTickSize;\n        tx = x1 + sign * tickMargin;\n        ty = tickCoord;\n        break;\n      default:\n        x1 = x2 = data.coordinate;\n        y2 = y + +mirror * height;\n        y1 = y2 + sign * finalTickSize;\n        ty = y1 + sign * tickMargin;\n        tx = tickCoord;\n        break;\n    }\n    return {\n      line: {\n        x1,\n        y1,\n        x2,\n        y2\n      },\n      tick: {\n        x: tx,\n        y: ty\n      }\n    };\n  }\n  getTickTextAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    var textAnchor;\n    switch (orientation) {\n      case 'left':\n        textAnchor = mirror ? 'start' : 'end';\n        break;\n      case 'right':\n        textAnchor = mirror ? 'end' : 'start';\n        break;\n      default:\n        textAnchor = 'middle';\n        break;\n    }\n    return textAnchor;\n  }\n  getTickVerticalAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    switch (orientation) {\n      case 'left':\n      case 'right':\n        return 'middle';\n      case 'top':\n        return mirror ? 'start' : 'end';\n      default:\n        return mirror ? 'end' : 'start';\n    }\n  }\n  renderAxisLine() {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      mirror,\n      axisLine\n    } = this.props;\n    var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), filterProps(axisLine, false)), {}, {\n      fill: 'none'\n    });\n    if (orientation === 'top' || orientation === 'bottom') {\n      var needHeight = +(orientation === 'top' && !mirror || orientation === 'bottom' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x,\n        y1: y + needHeight * height,\n        x2: x + width,\n        y2: y + needHeight * height\n      });\n    } else {\n      var needWidth = +(orientation === 'left' && !mirror || orientation === 'right' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x + needWidth * width,\n        y1: y,\n        x2: x + needWidth * width,\n        y2: y + height\n      });\n    }\n    return /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: clsx('recharts-cartesian-axis-line', get(axisLine, 'className'))\n    }));\n  }\n  static renderTickItem(option, props, value) {\n    var tickItem;\n    var combinedClassName = clsx(props.className, 'recharts-cartesian-axis-tick-value');\n    if (/*#__PURE__*/React.isValidElement(option)) {\n      tickItem = /*#__PURE__*/React.cloneElement(option, _objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else if (typeof option === 'function') {\n      tickItem = option(_objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else {\n      var className = 'recharts-cartesian-axis-tick-value';\n      if (typeof option !== 'boolean') {\n        className = clsx(className, option.className);\n      }\n      tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        className: className\n      }), value);\n    }\n    return tickItem;\n  }\n\n  /**\n   * render the ticks\n   * @param {string} fontSize Fontsize to consider for tick spacing\n   * @param {string} letterSpacing Letter spacing to consider for tick spacing\n   * @param {Array} ticks The ticks to actually render (overrides what was passed in props)\n   * @return {ReactElement | null} renderedTicks\n   */\n  renderTicks(fontSize, letterSpacing) {\n    var ticks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    var {\n      tickLine,\n      stroke,\n      tick,\n      tickFormatter,\n      unit\n    } = this.props;\n    // @ts-expect-error some properties are optional in props but required in getTicks\n    var finalTicks = getTicks(_objectSpread(_objectSpread({}, this.props), {}, {\n      ticks\n    }), fontSize, letterSpacing);\n    var textAnchor = this.getTickTextAnchor();\n    var verticalAnchor = this.getTickVerticalAnchor();\n    var axisProps = filterProps(this.props, false);\n    var customTickProps = filterProps(tick, false);\n    var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n      fill: 'none'\n    }, filterProps(tickLine, false));\n    var items = finalTicks.map((entry, i) => {\n      var {\n        line: lineCoord,\n        tick: tickCoord\n      } = this.getTickLineCoord(entry);\n      var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        textAnchor,\n        verticalAnchor\n      }, axisProps), {}, {\n        stroke: 'none',\n        fill: stroke\n      }, customTickProps), tickCoord), {}, {\n        index: i,\n        payload: entry,\n        visibleTicksCount: finalTicks.length,\n        tickFormatter\n      });\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-cartesian-axis-tick\",\n        key: \"tick-\".concat(entry.value, \"-\").concat(entry.coordinate, \"-\").concat(entry.tickCoord)\n      }, adaptEventsOfChild(this.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({}, tickLineProps, lineCoord, {\n        className: clsx('recharts-cartesian-axis-tick-line', get(tickLine, 'className'))\n      })), tick && CartesianAxis.renderTickItem(tick, tickProps, \"\".concat(typeof tickFormatter === 'function' ? tickFormatter(entry.value, i) : entry.value).concat(unit || '')));\n    });\n    return items.length > 0 ? /*#__PURE__*/React.createElement(\"g\", {\n      className: \"recharts-cartesian-axis-ticks\"\n    }, items) : null;\n  }\n  render() {\n    var {\n      axisLine,\n      width,\n      height,\n      className,\n      hide\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var {\n      ticks\n    } = this.props;\n\n    /*\n     * This is different condition from what validateWidthHeight is doing;\n     * the CartesianAxis does allow width or height to be undefined.\n     */\n    if (width != null && width <= 0 || height != null && height <= 0) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: clsx('recharts-cartesian-axis', className),\n      ref: _ref2 => {\n        if (_ref2) {\n          var tickNodes = _ref2.getElementsByClassName('recharts-cartesian-axis-tick-value');\n          this.tickRefs.current = Array.from(tickNodes);\n          var tick = tickNodes[0];\n          if (tick) {\n            var calculatedFontSize = window.getComputedStyle(tick).fontSize;\n            var calculatedLetterSpacing = window.getComputedStyle(tick).letterSpacing;\n            if (calculatedFontSize !== this.state.fontSize || calculatedLetterSpacing !== this.state.letterSpacing) {\n              this.setState({\n                fontSize: window.getComputedStyle(tick).fontSize,\n                letterSpacing: window.getComputedStyle(tick).letterSpacing\n              });\n            }\n          }\n        }\n      }\n    }, axisLine && this.renderAxisLine(), this.renderTicks(this.state.fontSize, this.state.letterSpacing, ticks), Label.renderCallByParent(this.props));\n  }\n}\n_defineProperty(CartesianAxis, \"displayName\", 'CartesianAxis');\n_defineProperty(CartesianAxis, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  viewBox: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  // The orientation of axis\n  orientation: 'bottom',\n  // The ticks\n  ticks: [],\n  stroke: '#666',\n  tickLine: true,\n  axisLine: true,\n  tick: true,\n  mirror: false,\n  minTickGap: 5,\n  // The width or height of tick\n  tickSize: 6,\n  tickMargin: 2,\n  interval: 'preserveEnd'\n});"], "names": [], "mappings": ";;;AAUA;;CAEC,GACD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxBA,IAAI,YAAY;IAAC;CAAU,EACzB,aAAa;IAAC;CAAU;AAC1B,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACtM,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;;;;;;;AA6BhT,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IAC1C,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,QAAQ,GAAG,WAAW,GAAE,qMAAA,CAAA,YAAe;QAC5C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE;QAC1B,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,eAAe;QACjB;IACF;IACA,sBAAsB,IAAI,EAAE,SAAS,EAAE;QACrC,IAAI,EACA,OAAO,EACR,GAAG,MACJ,YAAY,yBAAyB,MAAM;QAC7C,oDAAoD;QACpD,4DAA4D;QAC5D,IAAI,cAAc,IAAI,CAAC,KAAK,EAC1B,EACE,SAAS,UAAU,EACpB,GAAG,aACJ,eAAe,yBAAyB,aAAa;QACvD,OAAO,CAAC,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE,SAAS,eAAe,CAAC,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE,WAAW,iBAAiB,CAAC,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE,WAAW,IAAI,CAAC,KAAK;IAC5H;IAEA;;;;;GAKC,GACD,iBAAiB,IAAI,EAAE;QACrB,IAAI,EACF,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,UAAU,EACX,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QACxB,IAAI,OAAO,SAAS,CAAC,IAAI;QACzB,IAAI,gBAAgB,KAAK,QAAQ,IAAI;QACrC,IAAI,YAAY,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,IAAI,KAAK,SAAS,GAAG,KAAK,UAAU;QAC3E,OAAQ;YACN,KAAK;gBACH,KAAK,KAAK,KAAK,UAAU;gBACzB,KAAK,IAAI,CAAC,CAAC,SAAS;gBACpB,KAAK,KAAK,OAAO;gBACjB,KAAK,KAAK,OAAO;gBACjB,KAAK;gBACL;YACF,KAAK;gBACH,KAAK,KAAK,KAAK,UAAU;gBACzB,KAAK,IAAI,CAAC,CAAC,SAAS;gBACpB,KAAK,KAAK,OAAO;gBACjB,KAAK,KAAK,OAAO;gBACjB,KAAK;gBACL;YACF,KAAK;gBACH,KAAK,KAAK,KAAK,UAAU;gBACzB,KAAK,IAAI,CAAC,SAAS;gBACnB,KAAK,KAAK,OAAO;gBACjB,KAAK,KAAK,OAAO;gBACjB,KAAK;gBACL;YACF;gBACE,KAAK,KAAK,KAAK,UAAU;gBACzB,KAAK,IAAI,CAAC,SAAS;gBACnB,KAAK,KAAK,OAAO;gBACjB,KAAK,KAAK,OAAO;gBACjB,KAAK;gBACL;QACJ;QACA,OAAO;YACL,MAAM;gBACJ;gBACA;gBACA;gBACA;YACF;YACA,MAAM;gBACJ,GAAG;gBACH,GAAG;YACL;QACF;IACF;IACA,oBAAoB;QAClB,IAAI,EACF,WAAW,EACX,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;QACd,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,aAAa,SAAS,UAAU;gBAChC;YACF,KAAK;gBACH,aAAa,SAAS,QAAQ;gBAC9B;YACF;gBACE,aAAa;gBACb;QACJ;QACA,OAAO;IACT;IACA,wBAAwB;QACtB,IAAI,EACF,WAAW,EACX,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;QACd,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,SAAS,UAAU;YAC5B;gBACE,OAAO,SAAS,QAAQ;QAC5B;IACF;IACA,iBAAiB;QACf,IAAI,EACF,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,WAAW,EACX,MAAM,EACN,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,QAAQ,cAAc,cAAc,cAAc,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,UAAU,SAAS,CAAC,GAAG;YAC5H,MAAM;QACR;QACA,IAAI,gBAAgB,SAAS,gBAAgB,UAAU;YACrD,IAAI,aAAa,CAAC,CAAC,gBAAgB,SAAS,CAAC,UAAU,gBAAgB,YAAY,MAAM;YACzF,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAClD,IAAI;gBACJ,IAAI,IAAI,aAAa;gBACrB,IAAI,IAAI;gBACR,IAAI,IAAI,aAAa;YACvB;QACF,OAAO;YACL,IAAI,YAAY,CAAC,CAAC,gBAAgB,UAAU,CAAC,UAAU,gBAAgB,WAAW,MAAM;YACxF,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAClD,IAAI,IAAI,YAAY;gBACpB,IAAI;gBACJ,IAAI,IAAI,YAAY;gBACpB,IAAI,IAAI;YACV;QACF;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,SAAS,CAAC,GAAG,OAAO;YAClE,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,gCAAgC,CAAA,GAAA,8IAAA,CAAA,UAAG,AAAD,EAAE,UAAU;QAChE;IACF;IACA,OAAO,eAAe,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;QAC1C,IAAI;QACJ,IAAI,oBAAoB,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,MAAM,SAAS,EAAE;QAC9C,IAAI,WAAW,GAAE,qMAAA,CAAA,iBAAoB,CAAC,SAAS;YAC7C,WAAW,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC7F,WAAW;YACb;QACF,OAAO,IAAI,OAAO,WAAW,YAAY;YACvC,WAAW,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBAC5D,WAAW;YACb;QACF,OAAO;YACL,IAAI,YAAY;YAChB,IAAI,OAAO,WAAW,WAAW;gBAC/B,YAAY,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,SAAS;YAC9C;YACA,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,oJAAA,CAAA,OAAI,EAAE,SAAS,CAAC,GAAG,OAAO;gBACpE,WAAW;YACb,IAAI;QACN;QACA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,YAAY,QAAQ,EAAE,aAAa,EAAE;QACnC,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,EAAE;QAClF,IAAI,EACF,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,aAAa,EACb,IAAI,EACL,GAAG,IAAI,CAAC,KAAK;QACd,kFAAkF;QAClF,IAAI,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,cAAc,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG;YACzE;QACF,IAAI,UAAU;QACd,IAAI,aAAa,IAAI,CAAC,iBAAiB;QACvC,IAAI,iBAAiB,IAAI,CAAC,qBAAqB;QAC/C,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE;QACxC,IAAI,kBAAkB,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QACxC,IAAI,gBAAgB,cAAc,cAAc,CAAC,GAAG,YAAY,CAAC,GAAG;YAClE,MAAM;QACR,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,UAAU;QACzB,IAAI,QAAQ,WAAW,GAAG,CAAC,CAAC,OAAO;YACjC,IAAI,EACF,MAAM,SAAS,EACf,MAAM,SAAS,EAChB,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC1B,IAAI,YAAY,cAAc,cAAc,cAAc,cAAc;gBACtE;gBACA;YACF,GAAG,YAAY,CAAC,GAAG;gBACjB,QAAQ;gBACR,MAAM;YACR,GAAG,kBAAkB,YAAY,CAAC,GAAG;gBACnC,OAAO;gBACP,SAAS;gBACT,mBAAmB,WAAW,MAAM;gBACpC;YACF;YACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;gBACtD,WAAW;gBACX,KAAK,QAAQ,MAAM,CAAC,MAAM,KAAK,EAAE,KAAK,MAAM,CAAC,MAAM,UAAU,EAAE,KAAK,MAAM,CAAC,MAAM,SAAS;YAC5F,GAAG,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,KAAK,YAAY,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,SAAS,CAAC,GAAG,eAAe,WAAW;gBACxI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,qCAAqC,CAAA,GAAA,8IAAA,CAAA,UAAG,AAAD,EAAE,UAAU;YACrE,KAAK,QAAQ,cAAc,cAAc,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,kBAAkB,aAAa,cAAc,MAAM,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,MAAM,CAAC,QAAQ;QACzK;QACA,OAAO,MAAM,MAAM,GAAG,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;YAC9D,WAAW;QACb,GAAG,SAAS;IACd;IACA,SAAS;QACP,IAAI,EACF,QAAQ,EACR,KAAK,EACL,MAAM,EACN,SAAS,EACT,IAAI,EACL,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,MAAM;YACR,OAAO;QACT;QACA,IAAI,EACF,KAAK,EACN,GAAG,IAAI,CAAC,KAAK;QAEd;;;KAGC,GACD,IAAI,SAAS,QAAQ,SAAS,KAAK,UAAU,QAAQ,UAAU,GAAG;YAChE,OAAO;QACT;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YAC7C,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,2BAA2B;YAC3C,KAAK,CAAA;gBACH,IAAI,OAAO;oBACT,IAAI,YAAY,MAAM,sBAAsB,CAAC;oBAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC;oBACnC,IAAI,OAAO,SAAS,CAAC,EAAE;oBACvB,IAAI,MAAM;wBACR,IAAI,qBAAqB,OAAO,gBAAgB,CAAC,MAAM,QAAQ;wBAC/D,IAAI,0BAA0B,OAAO,gBAAgB,CAAC,MAAM,aAAa;wBACzE,IAAI,uBAAuB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,4BAA4B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;4BACtG,IAAI,CAAC,QAAQ,CAAC;gCACZ,UAAU,OAAO,gBAAgB,CAAC,MAAM,QAAQ;gCAChD,eAAe,OAAO,gBAAgB,CAAC,MAAM,aAAa;4BAC5D;wBACF;oBACF;gBACF;YACF;QACF,GAAG,YAAY,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,qJAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK;IACnJ;AACF;AACA,gBAAgB,eAAe,eAAe;AAC9C,gBAAgB,eAAe,gBAAgB;IAC7C,GAAG;IACH,GAAG;IACH,OAAO;IACP,QAAQ;IACR,SAAS;QACP,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;IACV;IACA,0BAA0B;IAC1B,aAAa;IACb,YAAY;IACZ,OAAO,EAAE;IACT,QAAQ;IACR,UAAU;IACV,UAAU;IACV,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,8BAA8B;IAC9B,UAAU;IACV,YAAY;IACZ,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/XAxis.js"], "sourcesContent": ["var _excluded = [\"children\"],\n  _excluded2 = [\"dangerouslySetInnerHTML\", \"ticks\"];\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview X Axis\n */\nimport * as React from 'react';\nimport { Component, useEffect, useMemo } from 'react';\nimport { clsx } from 'clsx';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { addXAxis, removeXAxis } from '../state/cartesianAxisSlice';\nimport { implicitXAxis, selectAxisScale, selectTicksOfAxis, selectXAxisPosition, selectXAxisSettings, selectXAxisSize } from '../state/selectors/axisSelectors';\nimport { selectAxisViewBox } from '../state/selectors/selectChartOffsetInternal';\nimport { useIsPanorama } from '../context/PanoramaContext';\nfunction SetXAxisSettings(props) {\n  var dispatch = useAppDispatch();\n  var settings = useMemo(() => {\n    var {\n        children\n      } = props,\n      rest = _objectWithoutProperties(props, _excluded);\n    return rest;\n  }, [props]);\n  var synchronizedSettings = useAppSelector(state => selectXAxisSettings(state, settings.id));\n  var settingsAreSynchronized = settings === synchronizedSettings;\n  useEffect(() => {\n    dispatch(addXAxis(settings));\n    return () => {\n      dispatch(removeXAxis(settings));\n    };\n  }, [settings, dispatch]);\n  if (settingsAreSynchronized) {\n    return props.children;\n  }\n  return null;\n}\nvar XAxisImpl = props => {\n  var {\n    xAxisId,\n    className\n  } = props;\n  var viewBox = useAppSelector(selectAxisViewBox);\n  var isPanorama = useIsPanorama();\n  var axisType = 'xAxis';\n  var scale = useAppSelector(state => selectAxisScale(state, axisType, xAxisId, isPanorama));\n  var cartesianTickItems = useAppSelector(state => selectTicksOfAxis(state, axisType, xAxisId, isPanorama));\n  var axisSize = useAppSelector(state => selectXAxisSize(state, xAxisId));\n  var position = useAppSelector(state => selectXAxisPosition(state, xAxisId));\n  if (axisSize == null || position == null) {\n    return null;\n  }\n  var {\n      dangerouslySetInnerHTML,\n      ticks\n    } = props,\n    allOtherProps = _objectWithoutProperties(props, _excluded2);\n  return /*#__PURE__*/React.createElement(CartesianAxis, _extends({}, allOtherProps, {\n    scale: scale,\n    x: position.x,\n    y: position.y,\n    width: axisSize.width,\n    height: axisSize.height,\n    className: clsx(\"recharts-\".concat(axisType, \" \").concat(axisType), className),\n    viewBox: viewBox,\n    ticks: cartesianTickItems\n  }));\n};\nvar XAxisSettingsDispatcher = props => {\n  var _props$interval, _props$includeHidden, _props$angle, _props$minTickGap, _props$tick;\n  return /*#__PURE__*/React.createElement(SetXAxisSettings, {\n    interval: (_props$interval = props.interval) !== null && _props$interval !== void 0 ? _props$interval : 'preserveEnd',\n    id: props.xAxisId,\n    scale: props.scale,\n    type: props.type,\n    padding: props.padding,\n    allowDataOverflow: props.allowDataOverflow,\n    domain: props.domain,\n    dataKey: props.dataKey,\n    allowDuplicatedCategory: props.allowDuplicatedCategory,\n    allowDecimals: props.allowDecimals,\n    tickCount: props.tickCount,\n    includeHidden: (_props$includeHidden = props.includeHidden) !== null && _props$includeHidden !== void 0 ? _props$includeHidden : false,\n    reversed: props.reversed,\n    ticks: props.ticks,\n    height: props.height,\n    orientation: props.orientation,\n    mirror: props.mirror,\n    hide: props.hide,\n    unit: props.unit,\n    name: props.name,\n    angle: (_props$angle = props.angle) !== null && _props$angle !== void 0 ? _props$angle : 0,\n    minTickGap: (_props$minTickGap = props.minTickGap) !== null && _props$minTickGap !== void 0 ? _props$minTickGap : 5,\n    tick: (_props$tick = props.tick) !== null && _props$tick !== void 0 ? _props$tick : true,\n    tickFormatter: props.tickFormatter\n  }, /*#__PURE__*/React.createElement(XAxisImpl, props));\n};\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class XAxis extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(XAxisSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(XAxis, \"displayName\", 'XAxis');\n_defineProperty(XAxis, \"defaultProps\", {\n  allowDataOverflow: implicitXAxis.allowDataOverflow,\n  allowDecimals: implicitXAxis.allowDecimals,\n  allowDuplicatedCategory: implicitXAxis.allowDuplicatedCategory,\n  height: implicitXAxis.height,\n  hide: false,\n  mirror: implicitXAxis.mirror,\n  orientation: implicitXAxis.orientation,\n  padding: implicitXAxis.padding,\n  reversed: implicitXAxis.reversed,\n  scale: implicitXAxis.scale,\n  tickCount: implicitXAxis.tickCount,\n  type: implicitXAxis.type,\n  xAxisId: 0\n});"], "names": [], "mappings": ";;;AAQA;;CAEC,GACD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,IAAI,YAAY;IAAC;CAAW,EAC1B,aAAa;IAAC;IAA2B;CAAQ;AACnD,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;;;;;;;;;;AAatM,SAAS,iBAAiB,KAAK;IAC7B,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrB,IAAI,EACA,QAAQ,EACT,GAAG,OACJ,OAAO,yBAAyB,OAAO;QACzC,OAAO;IACT,GAAG;QAAC;KAAM;IACV,IAAI,uBAAuB,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,EAAE;IACzF,IAAI,0BAA0B,aAAa;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE;QAClB,OAAO;YACL,SAAS,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACvB;IACF,GAAG;QAAC;QAAU;KAAS;IACvB,IAAI,yBAAyB;QAC3B,OAAO,MAAM,QAAQ;IACvB;IACA,OAAO;AACT;AACA,IAAI,YAAY,CAAA;IACd,IAAI,EACF,OAAO,EACP,SAAS,EACV,GAAG;IACJ,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,kLAAA,CAAA,oBAAiB;IAC9C,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,WAAW;IACf,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU,SAAS;IAC9E,IAAI,qBAAqB,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,UAAU,SAAS;IAC7F,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;IAC9D,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAClE,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;IACT;IACA,IAAI,EACA,uBAAuB,EACvB,KAAK,EACN,GAAG,OACJ,gBAAgB,yBAAyB,OAAO;IAClD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,gBAAa,EAAE,SAAS,CAAC,GAAG,eAAe;QACjF,OAAO;QACP,GAAG,SAAS,CAAC;QACb,GAAG,SAAS,CAAC;QACb,OAAO,SAAS,KAAK;QACrB,QAAQ,SAAS,MAAM;QACvB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,WAAW;QACpE,SAAS;QACT,OAAO;IACT;AACF;AACA,IAAI,0BAA0B,CAAA;IAC5B,IAAI,iBAAiB,sBAAsB,cAAc,mBAAmB;IAC5E,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,kBAAkB;QACxD,UAAU,CAAC,kBAAkB,MAAM,QAAQ,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB;QACxG,IAAI,MAAM,OAAO;QACjB,OAAO,MAAM,KAAK;QAClB,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,mBAAmB,MAAM,iBAAiB;QAC1C,QAAQ,MAAM,MAAM;QACpB,SAAS,MAAM,OAAO;QACtB,yBAAyB,MAAM,uBAAuB;QACtD,eAAe,MAAM,aAAa;QAClC,WAAW,MAAM,SAAS;QAC1B,eAAe,CAAC,uBAAuB,MAAM,aAAa,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB;QACjI,UAAU,MAAM,QAAQ;QACxB,OAAO,MAAM,KAAK;QAClB,QAAQ,MAAM,MAAM;QACpB,aAAa,MAAM,WAAW;QAC9B,QAAQ,MAAM,MAAM;QACpB,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,OAAO,CAAC,eAAe,MAAM,KAAK,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;QACzF,YAAY,CAAC,oBAAoB,MAAM,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB;QAClH,MAAM,CAAC,cAAc,MAAM,IAAI,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc;QACpF,eAAe,MAAM,aAAa;IACpC,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,WAAW;AACjD;AAGO,MAAM,cAAc,qMAAA,CAAA,YAAS;IAClC,SAAS;QACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,yBAAyB,IAAI,CAAC,KAAK;IAC7E;AACF;AACA,gBAAgB,OAAO,eAAe;AACtC,gBAAgB,OAAO,gBAAgB;IACrC,mBAAmB,sKAAA,CAAA,gBAAa,CAAC,iBAAiB;IAClD,eAAe,sKAAA,CAAA,gBAAa,CAAC,aAAa;IAC1C,yBAAyB,sKAAA,CAAA,gBAAa,CAAC,uBAAuB;IAC9D,QAAQ,sKAAA,CAAA,gBAAa,CAAC,MAAM;IAC5B,MAAM;IACN,QAAQ,sKAAA,CAAA,gBAAa,CAAC,MAAM;IAC5B,aAAa,sKAAA,CAAA,gBAAa,CAAC,WAAW;IACtC,SAAS,sKAAA,CAAA,gBAAa,CAAC,OAAO;IAC9B,UAAU,sKAAA,CAAA,gBAAa,CAAC,QAAQ;IAChC,OAAO,sKAAA,CAAA,gBAAa,CAAC,KAAK;IAC1B,WAAW,sKAAA,CAAA,gBAAa,CAAC,SAAS;IAClC,MAAM,sKAAA,CAAA,gBAAa,CAAC,IAAI;IACxB,SAAS;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/YAxis.js"], "sourcesContent": ["var _excluded = [\"dangerouslySetInnerHTML\", \"ticks\"];\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { Component, useEffect, useRef, useLayoutEffect, isValidElement } from 'react';\nimport { clsx } from 'clsx';\nimport { CartesianAxis } from './CartesianAxis';\nimport { addYAxis, removeYAxis, updateYAxisWidth } from '../state/cartesianAxisSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { implicitYAxis, selectAxisScale, selectTicksOfAxis, selectYAxisPosition, selectYAxisSize } from '../state/selectors/axisSelectors';\nimport { selectAxisViewBox } from '../state/selectors/selectChartOffsetInternal';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { getCalculatedYAxisWidth } from '../util/YAxisUtils';\nimport { isLabelContentAFunction } from '../component/Label';\nfunction SetYAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addYAxis(settings));\n    return () => {\n      dispatch(removeYAxis(settings));\n    };\n  }, [settings, dispatch]);\n  return null;\n}\nvar YAxisImpl = props => {\n  var _cartesianAxisRef$cur;\n  var {\n    yAxisId,\n    className,\n    width,\n    label\n  } = props;\n  var cartesianAxisRef = useRef(null);\n  var labelRef = useRef(null);\n  var viewBox = useAppSelector(selectAxisViewBox);\n  var isPanorama = useIsPanorama();\n  var dispatch = useAppDispatch();\n  var axisType = 'yAxis';\n  var scale = useAppSelector(state => selectAxisScale(state, axisType, yAxisId, isPanorama));\n  var axisSize = useAppSelector(state => selectYAxisSize(state, yAxisId));\n  var position = useAppSelector(state => selectYAxisPosition(state, yAxisId));\n  var cartesianTickItems = useAppSelector(state => selectTicksOfAxis(state, axisType, yAxisId, isPanorama));\n  useLayoutEffect(() => {\n    var _axisComponent$tickRe;\n    // No dynamic width calculation is done when width !== 'auto'\n    // or when a function/react element is used for label\n    if (width !== 'auto' || !axisSize || isLabelContentAFunction(label) || /*#__PURE__*/isValidElement(label)) return;\n    var axisComponent = cartesianAxisRef.current;\n    var tickNodes = axisComponent === null || axisComponent === void 0 || (_axisComponent$tickRe = axisComponent.tickRefs) === null || _axisComponent$tickRe === void 0 ? void 0 : _axisComponent$tickRe.current;\n    var {\n      tickSize,\n      tickMargin\n    } = axisComponent.props;\n\n    // get calculated width based on the label width, ticks etc\n    var updatedYAxisWidth = getCalculatedYAxisWidth({\n      ticks: tickNodes,\n      label: labelRef.current,\n      labelGapWithTick: 5,\n      tickSize,\n      tickMargin\n    });\n\n    // if the width has changed, dispatch an action to update the width\n    if (Math.round(axisSize.width) !== Math.round(updatedYAxisWidth)) dispatch(updateYAxisWidth({\n      id: yAxisId,\n      width: updatedYAxisWidth\n    }));\n  }, [cartesianAxisRef, cartesianAxisRef === null || cartesianAxisRef === void 0 || (_cartesianAxisRef$cur = cartesianAxisRef.current) === null || _cartesianAxisRef$cur === void 0 || (_cartesianAxisRef$cur = _cartesianAxisRef$cur.tickRefs) === null || _cartesianAxisRef$cur === void 0 ? void 0 : _cartesianAxisRef$cur.current, // required to do re-calculation when using brush\n  axisSize === null || axisSize === void 0 ? void 0 : axisSize.width, axisSize, dispatch, label, yAxisId, width]);\n  if (axisSize == null || position == null) {\n    return null;\n  }\n  var {\n      dangerouslySetInnerHTML,\n      ticks\n    } = props,\n    allOtherProps = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(CartesianAxis, _extends({}, allOtherProps, {\n    ref: cartesianAxisRef,\n    labelRef: labelRef,\n    scale: scale,\n    x: position.x,\n    y: position.y,\n    width: axisSize.width,\n    height: axisSize.height,\n    className: clsx(\"recharts-\".concat(axisType, \" \").concat(axisType), className),\n    viewBox: viewBox,\n    ticks: cartesianTickItems\n  }));\n};\nvar YAxisSettingsDispatcher = props => {\n  var _props$interval, _props$includeHidden, _props$angle, _props$minTickGap, _props$tick;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetYAxisSettings, {\n    interval: (_props$interval = props.interval) !== null && _props$interval !== void 0 ? _props$interval : 'preserveEnd',\n    id: props.yAxisId,\n    scale: props.scale,\n    type: props.type,\n    domain: props.domain,\n    allowDataOverflow: props.allowDataOverflow,\n    dataKey: props.dataKey,\n    allowDuplicatedCategory: props.allowDuplicatedCategory,\n    allowDecimals: props.allowDecimals,\n    tickCount: props.tickCount,\n    padding: props.padding,\n    includeHidden: (_props$includeHidden = props.includeHidden) !== null && _props$includeHidden !== void 0 ? _props$includeHidden : false,\n    reversed: props.reversed,\n    ticks: props.ticks,\n    width: props.width,\n    orientation: props.orientation,\n    mirror: props.mirror,\n    hide: props.hide,\n    unit: props.unit,\n    name: props.name,\n    angle: (_props$angle = props.angle) !== null && _props$angle !== void 0 ? _props$angle : 0,\n    minTickGap: (_props$minTickGap = props.minTickGap) !== null && _props$minTickGap !== void 0 ? _props$minTickGap : 5,\n    tick: (_props$tick = props.tick) !== null && _props$tick !== void 0 ? _props$tick : true,\n    tickFormatter: props.tickFormatter\n  }), /*#__PURE__*/React.createElement(YAxisImpl, props));\n};\nexport var YAxisDefaultProps = {\n  allowDataOverflow: implicitYAxis.allowDataOverflow,\n  allowDecimals: implicitYAxis.allowDecimals,\n  allowDuplicatedCategory: implicitYAxis.allowDuplicatedCategory,\n  hide: false,\n  mirror: implicitYAxis.mirror,\n  orientation: implicitYAxis.orientation,\n  padding: implicitYAxis.padding,\n  reversed: implicitYAxis.reversed,\n  scale: implicitYAxis.scale,\n  tickCount: implicitYAxis.tickCount,\n  type: implicitYAxis.type,\n  width: implicitYAxis.width,\n  yAxisId: 0\n};\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class YAxis extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(YAxisSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(YAxis, \"displayName\", 'YAxis');\n_defineProperty(YAxis, \"defaultProps\", YAxisDefaultProps);"], "names": [], "mappings": ";;;;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,IAAI,YAAY;IAAC;IAA2B;CAAQ;AACpD,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;;;;;;;;;;;;AAYtM,SAAS,iBAAiB,QAAQ;IAChC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE;QAClB,OAAO;YACL,SAAS,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACvB;IACF,GAAG;QAAC;QAAU;KAAS;IACvB,OAAO;AACT;AACA,IAAI,YAAY,CAAA;IACd,IAAI;IACJ,IAAI,EACF,OAAO,EACP,SAAS,EACT,KAAK,EACL,KAAK,EACN,GAAG;IACJ,IAAI,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,IAAI,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,kLAAA,CAAA,oBAAiB;IAC9C,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,WAAW;IACf,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU,SAAS;IAC9E,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;IAC9D,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;IAClE,IAAI,qBAAqB,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,UAAU,SAAS;IAC7F,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,IAAI;QACJ,6DAA6D;QAC7D,qDAAqD;QACrD,IAAI,UAAU,UAAU,CAAC,YAAY,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,UAAU,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QAC3G,IAAI,gBAAgB,iBAAiB,OAAO;QAC5C,IAAI,YAAY,kBAAkB,QAAQ,kBAAkB,KAAK,KAAK,CAAC,wBAAwB,cAAc,QAAQ,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,OAAO;QAC5M,IAAI,EACF,QAAQ,EACR,UAAU,EACX,GAAG,cAAc,KAAK;QAEvB,2DAA2D;QAC3D,IAAI,oBAAoB,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,OAAO;YACP,OAAO,SAAS,OAAO;YACvB,kBAAkB;YAClB;YACA;QACF;QAEA,mEAAmE;QACnE,IAAI,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,KAAK,CAAC,oBAAoB,SAAS,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE;YAC1F,IAAI;YACJ,OAAO;QACT;IACF,GAAG;QAAC;QAAkB,qBAAqB,QAAQ,qBAAqB,KAAK,KAAK,CAAC,wBAAwB,iBAAiB,OAAO,MAAM,QAAQ,0BAA0B,KAAK,KAAK,CAAC,wBAAwB,sBAAsB,QAAQ,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,OAAO;QACnU,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK;QAAE;QAAU;QAAU;QAAO;QAAS;KAAM;IAC9G,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;IACT;IACA,IAAI,EACA,uBAAuB,EACvB,KAAK,EACN,GAAG,OACJ,gBAAgB,yBAAyB,OAAO;IAClD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,gBAAa,EAAE,SAAS,CAAC,GAAG,eAAe;QACjF,KAAK;QACL,UAAU;QACV,OAAO;QACP,GAAG,SAAS,CAAC;QACb,GAAG,SAAS,CAAC;QACb,OAAO,SAAS,KAAK;QACrB,QAAQ,SAAS,MAAM;QACvB,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,WAAW;QACpE,SAAS;QACT,OAAO;IACT;AACF;AACA,IAAI,0BAA0B,CAAA;IAC5B,IAAI,iBAAiB,sBAAsB,cAAc,mBAAmB;IAC5E,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,kBAAkB;QAC/G,UAAU,CAAC,kBAAkB,MAAM,QAAQ,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB;QACxG,IAAI,MAAM,OAAO;QACjB,OAAO,MAAM,KAAK;QAClB,MAAM,MAAM,IAAI;QAChB,QAAQ,MAAM,MAAM;QACpB,mBAAmB,MAAM,iBAAiB;QAC1C,SAAS,MAAM,OAAO;QACtB,yBAAyB,MAAM,uBAAuB;QACtD,eAAe,MAAM,aAAa;QAClC,WAAW,MAAM,SAAS;QAC1B,SAAS,MAAM,OAAO;QACtB,eAAe,CAAC,uBAAuB,MAAM,aAAa,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB;QACjI,UAAU,MAAM,QAAQ;QACxB,OAAO,MAAM,KAAK;QAClB,OAAO,MAAM,KAAK;QAClB,aAAa,MAAM,WAAW;QAC9B,QAAQ,MAAM,MAAM;QACpB,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,OAAO,CAAC,eAAe,MAAM,KAAK,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;QACzF,YAAY,CAAC,oBAAoB,MAAM,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB;QAClH,MAAM,CAAC,cAAc,MAAM,IAAI,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc;QACpF,eAAe,MAAM,aAAa;IACpC,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,WAAW;AAClD;AACO,IAAI,oBAAoB;IAC7B,mBAAmB,sKAAA,CAAA,gBAAa,CAAC,iBAAiB;IAClD,eAAe,sKAAA,CAAA,gBAAa,CAAC,aAAa;IAC1C,yBAAyB,sKAAA,CAAA,gBAAa,CAAC,uBAAuB;IAC9D,MAAM;IACN,QAAQ,sKAAA,CAAA,gBAAa,CAAC,MAAM;IAC5B,aAAa,sKAAA,CAAA,gBAAa,CAAC,WAAW;IACtC,SAAS,sKAAA,CAAA,gBAAa,CAAC,OAAO;IAC9B,UAAU,sKAAA,CAAA,gBAAa,CAAC,QAAQ;IAChC,OAAO,sKAAA,CAAA,gBAAa,CAAC,KAAK;IAC1B,WAAW,sKAAA,CAAA,gBAAa,CAAC,SAAS;IAClC,MAAM,sKAAA,CAAA,gBAAa,CAAC,IAAI;IACxB,OAAO,sKAAA,CAAA,gBAAa,CAAC,KAAK;IAC1B,SAAS;AACX;AAGO,MAAM,cAAc,qMAAA,CAAA,YAAS;IAClC,SAAS;QACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,yBAAyB,IAAI,CAAC,KAAK;IAC7E;AACF;AACA,gBAAgB,OAAO,eAAe;AACtC,gBAAgB,OAAO,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1862, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/CartesianGrid.js"], "sourcesContent": ["var _excluded = [\"x1\", \"y1\", \"x2\", \"y2\", \"key\"],\n  _excluded2 = [\"offset\"],\n  _excluded3 = [\"xAxisId\", \"yAxisId\"],\n  _excluded4 = [\"xAxisId\", \"yAxisId\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Cartesian Grid\n */\nimport * as React from 'react';\nimport { warn } from '../util/LogUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { getCoordinatesOfGrid, getTicksOfAxis } from '../util/ChartUtils';\nimport { getTicks } from './getTicks';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useChartHeight, useChartWidth, useOffsetInternal } from '../context/chartLayoutContext';\nimport { selectAxisPropsNeededForCartesianGridTicksGenerator } from '../state/selectors/axisSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\n\n/**\n * The <CartesianGrid horizontal\n */\n\nvar Background = props => {\n  var {\n    fill\n  } = props;\n  if (!fill || fill === 'none') {\n    return null;\n  }\n  var {\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    ry\n  } = props;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    ry: ry,\n    width: width,\n    height: height,\n    stroke: \"none\",\n    fill: fill,\n    fillOpacity: fillOpacity,\n    className: \"recharts-cartesian-grid-bg\"\n  });\n};\nfunction renderLineItem(option, props) {\n  var lineItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript does not see the props type when cloning an element\n    lineItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    lineItem = option(props);\n  } else {\n    var {\n        x1,\n        y1,\n        x2,\n        y2,\n        key\n      } = props,\n      others = _objectWithoutProperties(props, _excluded);\n    var _filterProps = filterProps(others, false),\n      {\n        offset: __\n      } = _filterProps,\n      restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n    lineItem = /*#__PURE__*/React.createElement(\"line\", _extends({}, restOfFilteredProps, {\n      x1: x1,\n      y1: y1,\n      x2: x2,\n      y2: y2,\n      fill: \"none\",\n      key: key\n    }));\n  }\n  return lineItem;\n}\nfunction HorizontalGridLines(props) {\n  var {\n    x,\n    width,\n    horizontal = true,\n    horizontalPoints\n  } = props;\n  if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded3);\n  var items = horizontalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: x,\n      y1: entry,\n      x2: x + width,\n      y2: entry,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(horizontal, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-horizontal\"\n  }, items);\n}\nfunction VerticalGridLines(props) {\n  var {\n    y,\n    height,\n    vertical = true,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalPoints || !verticalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded4);\n  var items = verticalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: entry,\n      y1: y,\n      x2: entry,\n      y2: y + height,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(vertical, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-vertical\"\n  }, items);\n}\nfunction HorizontalStripes(props) {\n  var {\n    horizontalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    horizontalPoints,\n    horizontal = true\n  } = props;\n  if (!horizontal || !horizontalFill || !horizontalFill.length) {\n    return null;\n  }\n\n  // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n  var roundedSortedHorizontalPoints = horizontalPoints.map(e => Math.round(e + y - y)).sort((a, b) => a - b);\n  // Why is this condition `!==` instead of `<=` ?\n  if (y !== roundedSortedHorizontalPoints[0]) {\n    roundedSortedHorizontalPoints.unshift(0);\n  }\n  var items = roundedSortedHorizontalPoints.map((entry, i) => {\n    // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n    var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n    var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n    if (lineHeight <= 0) {\n      return null;\n    }\n    var colorIndex = i % horizontalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      y: entry,\n      x: x,\n      height: lineHeight,\n      width: width,\n      stroke: \"none\",\n      fill: horizontalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-horizontal\"\n  }, items);\n}\nfunction VerticalStripes(props) {\n  var {\n    vertical = true,\n    verticalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalFill || !verticalFill.length) {\n    return null;\n  }\n  var roundedSortedVerticalPoints = verticalPoints.map(e => Math.round(e + x - x)).sort((a, b) => a - b);\n  if (x !== roundedSortedVerticalPoints[0]) {\n    roundedSortedVerticalPoints.unshift(0);\n  }\n  var items = roundedSortedVerticalPoints.map((entry, i) => {\n    var lastStripe = !roundedSortedVerticalPoints[i + 1];\n    var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n    if (lineWidth <= 0) {\n      return null;\n    }\n    var colorIndex = i % verticalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      x: entry,\n      y: y,\n      width: lineWidth,\n      height: height,\n      stroke: \"none\",\n      fill: verticalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-vertical\"\n  }, items);\n}\nvar defaultVerticalCoordinatesGenerator = (_ref, syncWithTicks) => {\n  var {\n    xAxis,\n    width,\n    height,\n    offset\n  } = _ref;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n    ticks: getTicksOfAxis(xAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = (_ref2, syncWithTicks) => {\n  var {\n    yAxis,\n    width,\n    height,\n    offset\n  } = _ref2;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n    ticks: getTicksOfAxis(yAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n  horizontal: true,\n  vertical: true,\n  // The ordinates of horizontal grid lines\n  horizontalPoints: [],\n  // The abscissas of vertical grid lines\n  verticalPoints: [],\n  stroke: '#ccc',\n  fill: 'none',\n  // The fill of colors of grid lines\n  verticalFill: [],\n  horizontalFill: [],\n  xAxisId: 0,\n  yAxisId: 0\n};\nexport function CartesianGrid(props) {\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var offset = useOffsetInternal();\n  var propsIncludingDefaults = _objectSpread(_objectSpread({}, resolveDefaultProps(props, defaultProps)), {}, {\n    x: isNumber(props.x) ? props.x : offset.left,\n    y: isNumber(props.y) ? props.y : offset.top,\n    width: isNumber(props.width) ? props.width : offset.width,\n    height: isNumber(props.height) ? props.height : offset.height\n  });\n  var {\n    xAxisId,\n    yAxisId,\n    x,\n    y,\n    width,\n    height,\n    syncWithTicks,\n    horizontalValues,\n    verticalValues\n  } = propsIncludingDefaults;\n  var isPanorama = useIsPanorama();\n  var xAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'xAxis', xAxisId, isPanorama));\n  var yAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'yAxis', yAxisId, isPanorama));\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0 || !isNumber(x) || x !== +x || !isNumber(y) || y !== +y) {\n    return null;\n  }\n\n  /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */\n  var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n  var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n  var {\n    horizontalPoints,\n    verticalPoints\n  } = propsIncludingDefaults;\n\n  // No horizontal points are specified\n  if ((!horizontalPoints || !horizontalPoints.length) && typeof horizontalCoordinatesGenerator === 'function') {\n    var isHorizontalValues = horizontalValues && horizontalValues.length;\n    var generatorResult = horizontalCoordinatesGenerator({\n      yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n        ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isHorizontalValues ? true : syncWithTicks);\n    warn(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof generatorResult, \"]\"));\n    if (Array.isArray(generatorResult)) {\n      horizontalPoints = generatorResult;\n    }\n  }\n\n  // No vertical points are specified\n  if ((!verticalPoints || !verticalPoints.length) && typeof verticalCoordinatesGenerator === 'function') {\n    var isVerticalValues = verticalValues && verticalValues.length;\n    var _generatorResult = verticalCoordinatesGenerator({\n      xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n        ticks: isVerticalValues ? verticalValues : xAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isVerticalValues ? true : syncWithTicks);\n    warn(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof _generatorResult, \"]\"));\n    if (Array.isArray(_generatorResult)) {\n      verticalPoints = _generatorResult;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid\"\n  }, /*#__PURE__*/React.createElement(Background, {\n    fill: propsIncludingDefaults.fill,\n    fillOpacity: propsIncludingDefaults.fillOpacity,\n    x: propsIncludingDefaults.x,\n    y: propsIncludingDefaults.y,\n    width: propsIncludingDefaults.width,\n    height: propsIncludingDefaults.height,\n    ry: propsIncludingDefaults.ry\n  }), /*#__PURE__*/React.createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n    horizontalPoints: horizontalPoints\n  })), /*#__PURE__*/React.createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n    verticalPoints: verticalPoints\n  })), /*#__PURE__*/React.createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    horizontalPoints: horizontalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    verticalPoints: verticalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })));\n}\nCartesianGrid.displayName = 'CartesianGrid';"], "names": [], "mappings": ";;;AAYA;;CAEC,GACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA,IAAI,YAAY;IAAC;IAAM;IAAM;IAAM;IAAM;CAAM,EAC7C,aAAa;IAAC;CAAS,EACvB,aAAa;IAAC;IAAW;CAAU,EACnC,aAAa;IAAC;IAAW;CAAU;AACrC,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;;;;;;;;;;;;;AAiBtM;;CAEC,GAED,IAAI,aAAa,CAAA;IACf,IAAI,EACF,IAAI,EACL,GAAG;IACJ,IAAI,CAAC,QAAQ,SAAS,QAAQ;QAC5B,OAAO;IACT;IACA,IAAI,EACF,WAAW,EACX,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,EAAE,EACH,GAAG;IACJ,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,GAAG;QACH,GAAG;QACH,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,aAAa;QACb,WAAW;IACb;AACF;AACA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI;IACJ,IAAI,WAAW,GAAE,qMAAA,CAAA,iBAAoB,CAAC,SAAS;QAC7C,kFAAkF;QAClF,WAAW,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,QAAQ;IACrD,OAAO,IAAI,OAAO,WAAW,YAAY;QACvC,WAAW,OAAO;IACpB,OAAO;QACL,IAAI,EACA,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,EACJ,GAAG,OACJ,SAAS,yBAAyB,OAAO;QAC3C,IAAI,eAAe,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,QACrC,EACE,QAAQ,EAAE,EACX,GAAG,cACJ,sBAAsB,yBAAyB,cAAc;QAC/D,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,SAAS,CAAC,GAAG,qBAAqB;YACpF,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,KAAK;QACP;IACF;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK;IAChC,IAAI,EACF,CAAC,EACD,KAAK,EACL,aAAa,IAAI,EACjB,gBAAgB,EACjB,GAAG;IACJ,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,iBAAiB,MAAM,EAAE;QAChE,OAAO;IACT;IACA,IAAI,EACA,OAAO,EACP,OAAO,EACR,GAAG,OACJ,qBAAqB,yBAAyB,OAAO;IACvD,IAAI,QAAQ,iBAAiB,GAAG,CAAC,CAAC,OAAO;QACvC,IAAI,gBAAgB,cAAc,cAAc,CAAC,GAAG,qBAAqB,CAAC,GAAG;YAC3E,IAAI;YACJ,IAAI;YACJ,IAAI,IAAI;YACR,IAAI;YACJ,KAAK,QAAQ,MAAM,CAAC;YACpB,OAAO;QACT;QACA,OAAO,eAAe,YAAY;IACpC;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;QAC3C,WAAW;IACb,GAAG;AACL;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,EACF,CAAC,EACD,MAAM,EACN,WAAW,IAAI,EACf,cAAc,EACf,GAAG;IACJ,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,eAAe,MAAM,EAAE;QAC1D,OAAO;IACT;IACA,IAAI,EACA,OAAO,EACP,OAAO,EACR,GAAG,OACJ,qBAAqB,yBAAyB,OAAO;IACvD,IAAI,QAAQ,eAAe,GAAG,CAAC,CAAC,OAAO;QACrC,IAAI,gBAAgB,cAAc,cAAc,CAAC,GAAG,qBAAqB,CAAC,GAAG;YAC3E,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI,IAAI;YACR,KAAK,QAAQ,MAAM,CAAC;YACpB,OAAO;QACT;QACA,OAAO,eAAe,UAAU;IAClC;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;QAC3C,WAAW;IACb,GAAG;AACL;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,EACF,cAAc,EACd,WAAW,EACX,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,gBAAgB,EAChB,aAAa,IAAI,EAClB,GAAG;IACJ,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,eAAe,MAAM,EAAE;QAC5D,OAAO;IACT;IAEA,8HAA8H;IAC9H,IAAI,gCAAgC,iBAAiB,GAAG,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACxG,gDAAgD;IAChD,IAAI,MAAM,6BAA6B,CAAC,EAAE,EAAE;QAC1C,8BAA8B,OAAO,CAAC;IACxC;IACA,IAAI,QAAQ,8BAA8B,GAAG,CAAC,CAAC,OAAO;QACpD,0FAA0F;QAC1F,IAAI,aAAa,CAAC,6BAA6B,CAAC,IAAI,EAAE;QACtD,IAAI,aAAa,aAAa,IAAI,SAAS,QAAQ,6BAA6B,CAAC,IAAI,EAAE,GAAG;QAC1F,IAAI,cAAc,GAAG;YACnB,OAAO;QACT;QACA,IAAI,aAAa,IAAI,eAAe,MAAM;QAC1C,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,KAAK,SAAS,MAAM,CAAC,GAAG,+CAA+C;;YAEvE,GAAG;YACH,GAAG;YACH,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM,cAAc,CAAC,WAAW;YAChC,aAAa;YACb,WAAW;QACb;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;QAC3C,WAAW;IACb,GAAG;AACL;AACA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,EACF,WAAW,IAAI,EACf,YAAY,EACZ,WAAW,EACX,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,cAAc,EACf,GAAG;IACJ,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,MAAM,EAAE;QACtD,OAAO;IACT;IACA,IAAI,8BAA8B,eAAe,GAAG,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACpG,IAAI,MAAM,2BAA2B,CAAC,EAAE,EAAE;QACxC,4BAA4B,OAAO,CAAC;IACtC;IACA,IAAI,QAAQ,4BAA4B,GAAG,CAAC,CAAC,OAAO;QAClD,IAAI,aAAa,CAAC,2BAA2B,CAAC,IAAI,EAAE;QACpD,IAAI,YAAY,aAAa,IAAI,QAAQ,QAAQ,2BAA2B,CAAC,IAAI,EAAE,GAAG;QACtF,IAAI,aAAa,GAAG;YAClB,OAAO;QACT;QACA,IAAI,aAAa,IAAI,aAAa,MAAM;QACxC,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,KAAK,SAAS,MAAM,CAAC,GAAG,+CAA+C;;YAEvE,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,MAAM,YAAY,CAAC,WAAW;YAC9B,aAAa;YACb,WAAW;QACb;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;QAC3C,WAAW;IACb,GAAG;AACL;AACA,IAAI,sCAAsC,CAAC,MAAM;IAC/C,IAAI,EACF,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACP,GAAG;IACJ,OAAO,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,cAAc,cAAc,CAAC,GAAG,6JAAA,CAAA,gBAAa,CAAC,YAAY,GAAG,QAAQ,CAAC,GAAG;QAC1H,OAAO,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;QAC7B,SAAS;YACP,GAAG;YACH,GAAG;YACH;YACA;QACF;IACF,KAAK,OAAO,IAAI,EAAE,OAAO,IAAI,GAAG,OAAO,KAAK,EAAE;AAChD;AACA,IAAI,wCAAwC,CAAC,OAAO;IAClD,IAAI,EACF,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACP,GAAG;IACJ,OAAO,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,cAAc,cAAc,CAAC,GAAG,6JAAA,CAAA,gBAAa,CAAC,YAAY,GAAG,QAAQ,CAAC,GAAG;QAC1H,OAAO,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;QAC7B,SAAS;YACP,GAAG;YACH,GAAG;YACH;YACA;QACF;IACF,KAAK,OAAO,GAAG,EAAE,OAAO,GAAG,GAAG,OAAO,MAAM,EAAE;AAC/C;AACA,IAAI,eAAe;IACjB,YAAY;IACZ,UAAU;IACV,yCAAyC;IACzC,kBAAkB,EAAE;IACpB,uCAAuC;IACvC,gBAAgB,EAAE;IAClB,QAAQ;IACR,MAAM;IACN,mCAAmC;IACnC,cAAc,EAAE;IAChB,gBAAgB,EAAE;IAClB,SAAS;IACT,SAAS;AACX;AACO,SAAS,cAAc,KAAK;IACjC,IAAI,aAAa,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC/B,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD;IAC7B,IAAI,yBAAyB,cAAc,cAAc,CAAC,GAAG,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,gBAAgB,CAAC,GAAG;QAC1G,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI;QAC5C,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,OAAO,GAAG;QAC3C,OAAO,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,OAAO,KAAK;QACzD,QAAQ,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,OAAO,MAAM;IAC/D;IACA,IAAI,EACF,OAAO,EACP,OAAO,EACP,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,aAAa,EACb,gBAAgB,EAChB,cAAc,EACf,GAAG;IACJ,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sDAAmD,AAAD,EAAE,OAAO,SAAS,SAAS;IACjH,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,sDAAmD,AAAD,EAAE,OAAO,SAAS,SAAS;IACjH,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,SAAS,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,UAAU,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM,CAAC,GAAG;QAC9H,OAAO;IACT;IAEA;;;;;;GAMC,GACD,IAAI,+BAA+B,uBAAuB,4BAA4B,IAAI;IAC1F,IAAI,iCAAiC,uBAAuB,8BAA8B,IAAI;IAC9F,IAAI,EACF,gBAAgB,EAChB,cAAc,EACf,GAAG;IAEJ,qCAAqC;IACrC,IAAI,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,MAAM,KAAK,OAAO,mCAAmC,YAAY;QAC3G,IAAI,qBAAqB,oBAAoB,iBAAiB,MAAM;QACpE,IAAI,kBAAkB,+BAA+B;YACnD,OAAO,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACzD,OAAO,qBAAqB,mBAAmB,MAAM,KAAK;YAC5D,KAAK;YACL,OAAO;YACP,QAAQ;YACR;QACF,GAAG,qBAAqB,OAAO;QAC/B,CAAA,GAAA,mJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,CAAC,kBAAkB,+EAA+E,MAAM,CAAC,OAAO,iBAAiB;QACnJ,IAAI,MAAM,OAAO,CAAC,kBAAkB;YAClC,mBAAmB;QACrB;IACF;IAEA,mCAAmC;IACnC,IAAI,CAAC,CAAC,kBAAkB,CAAC,eAAe,MAAM,KAAK,OAAO,iCAAiC,YAAY;QACrG,IAAI,mBAAmB,kBAAkB,eAAe,MAAM;QAC9D,IAAI,mBAAmB,6BAA6B;YAClD,OAAO,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACzD,OAAO,mBAAmB,iBAAiB,MAAM,KAAK;YACxD,KAAK;YACL,OAAO;YACP,QAAQ;YACR;QACF,GAAG,mBAAmB,OAAO;QAC7B,CAAA,GAAA,mJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,CAAC,mBAAmB,6EAA6E,MAAM,CAAC,OAAO,kBAAkB;QACnJ,IAAI,MAAM,OAAO,CAAC,mBAAmB;YACnC,iBAAiB;QACnB;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,KAAK;QAC3C,WAAW;IACb,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;QAC9C,MAAM,uBAAuB,IAAI;QACjC,aAAa,uBAAuB,WAAW;QAC/C,GAAG,uBAAuB,CAAC;QAC3B,GAAG,uBAAuB,CAAC;QAC3B,OAAO,uBAAuB,KAAK;QACnC,QAAQ,uBAAuB,MAAM;QACrC,IAAI,uBAAuB,EAAE;IAC/B,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mBAAmB,SAAS,CAAC,GAAG,wBAAwB;QAC3F,kBAAkB;IACpB,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iBAAiB,SAAS,CAAC,GAAG,wBAAwB;QAC1F,gBAAgB;IAClB,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qBAAqB,SAAS,CAAC,GAAG,wBAAwB;QAC9F,QAAQ;QACR,kBAAkB;QAClB,OAAO;QACP,OAAO;IACT,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mBAAmB,SAAS,CAAC,GAAG,wBAAwB;QAC5F,QAAQ;QACR,gBAAgB;QAChB,OAAO;QACP,OAAO;IACT;AACF;AACA,cAAc,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/Line.js"], "sourcesContent": ["var _excluded = [\"type\", \"layout\", \"connectNulls\", \"needClip\"],\n  _excluded2 = [\"activeDot\", \"animateNewValues\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"hide\", \"isAnimationActive\", \"label\", \"legendType\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { Component, PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectLinePoints } from '../state/selectors/lineSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nimport { usePlotArea } from '../hooks';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: stroke,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.stroke,\n      unit\n    }\n  };\n}\nvar generateSimpleStrokeDasharray = (totalLength, length) => {\n  return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n};\nfunction repeat(lines, count) {\n  var linesUnit = lines.length % 2 !== 0 ? [...lines, 0] : lines;\n  var result = [];\n  for (var i = 0; i < count; ++i) {\n    result = [...result, ...linesUnit];\n  }\n  return result;\n}\nvar getStrokeDasharray = (length, totalLength, lines) => {\n  var lineLength = lines.reduce((pre, next) => pre + next);\n\n  // if lineLength is 0 return the default when no strokeDasharray is provided\n  if (!lineLength) {\n    return generateSimpleStrokeDasharray(totalLength, length);\n  }\n  var count = Math.floor(length / lineLength);\n  var remainLength = length % lineLength;\n  var restLength = totalLength - length;\n  var remainLines = [];\n  for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n    if (sum + lines[i] > remainLength) {\n      remainLines = [...lines.slice(0, i), remainLength - sum];\n      break;\n    }\n  }\n  var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n  return [...repeat(lines, count), ...remainLines, ...emptyLines].map(line => \"\".concat(line, \"px\")).join(', ');\n};\nfunction renderDotItem(option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n}\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    dot,\n    dataKey,\n    needClip\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var lineProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, lineProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-line-dots\",\n    key: \"dots\"\n  }, dotsProps), dots);\n}\nfunction StaticCurve(_ref2) {\n  var {\n    clipPathId,\n    pathRef,\n    points,\n    strokeDasharray,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      type,\n      layout,\n      connectNulls,\n      needClip\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var curveProps = _objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n    fill: 'none',\n    className: 'recharts-line-curve',\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n    points,\n    type,\n    layout,\n    connectNulls,\n    strokeDasharray: strokeDasharray !== null && strokeDasharray !== void 0 ? strokeDasharray : props.strokeDasharray\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n    pathRef: pathRef\n  })), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    clipPathId: clipPathId,\n    props: props\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction getTotalLength(mainCurve) {\n  try {\n    return mainCurve && mainCurve.getTotalLength && mainCurve.getTotalLength() || 0;\n  } catch (_unused) {\n    return 0;\n  }\n}\nfunction CurveWithAnimation(_ref3) {\n  var {\n    clipPathId,\n    props,\n    pathRef,\n    previousPointsRef,\n    longestAnimatedLengthRef\n  } = _ref3;\n  var {\n    points,\n    strokeDasharray,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    animateNewValues,\n    width,\n    height,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-line-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var totalLength = getTotalLength(pathRef.current);\n  /*\n   * Here we want to detect if the length animation has been interrupted.\n   * For that we keep a reference to the furthest length that has been animated.\n   *\n   * And then, to keep things smooth, we add to it the current length that is being animated right now.\n   *\n   * If we did Math.max then it makes the length animation \"pause\" but we want to keep it smooth\n   * so in case we have some \"leftover\" length from the previous animation we add it to the current length.\n   *\n   * This is not perfect because the animation changes speed due to easing. The default easing is 'ease' which is not linear\n   * and makes it stand out. But it's good enough I suppose.\n   * If we want to fix it then we need to keep track of multiple animations and their easing and timings.\n   *\n   * If you want to see this in action, try to change the dataKey of the line chart while the initial animation is running.\n   * The Line begins with zero length and slowly grows to the full length. While this growth is in progress,\n   * change the dataKey and the Line will continue growing from where it has grown so far.\n   */\n  var startingPoint = longestAnimatedLengthRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref4 => {\n    var {\n      t\n    } = _ref4;\n    var interpolator = interpolateNumber(startingPoint, totalLength + startingPoint);\n    var curLength = Math.min(interpolator(t), totalLength);\n    var currentStrokeDasharray;\n    if (strokeDasharray) {\n      var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(num => parseFloat(num));\n      currentStrokeDasharray = getStrokeDasharray(curLength, totalLength, lines);\n    } else {\n      currentStrokeDasharray = generateSimpleStrokeDasharray(totalLength, curLength);\n    }\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepData = t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          var interpolatorX = interpolateNumber(prev.x, entry.x);\n          var interpolatorY = interpolateNumber(prev.y, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolatorX(t),\n            y: interpolatorY(t)\n          });\n        }\n\n        // magic number of faking previous x and y location\n        if (animateNewValues) {\n          var _interpolatorX = interpolateNumber(width * 2, entry.x);\n          var _interpolatorY = interpolateNumber(height / 2, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: _interpolatorX(t),\n            y: _interpolatorY(t)\n          });\n        }\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: entry.x,\n          y: entry.y\n        });\n      });\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n      return /*#__PURE__*/React.createElement(StaticCurve, {\n        props: props,\n        points: stepData,\n        clipPathId: clipPathId,\n        pathRef: pathRef,\n        showLabels: !isAnimating,\n        strokeDasharray: currentStrokeDasharray\n      });\n    }\n\n    /*\n     * Here it is important to wait a little bit with updating the previousPointsRef\n     * before the animation has a time to initialize.\n     * If we set the previous pointsRef immediately, we set it before the Legend height it calculated\n     * and before pathRef is set.\n     * If that happens, the Line will re-render again after Legend had reported its height\n     * which will start a new animation with the previous points as the starting point\n     * which gives the effect of the Line animating slightly upwards (where the animation distance equals the Legend height).\n     * Waiting for t > 0 is indirect but good enough to ensure that the Legend height is calculated and animation works properly.\n     *\n     * Total length similarly is calculated from the pathRef. We should not update the previousPointsRef\n     * before the pathRef is set, otherwise we will have a wrong total length.\n     */\n    if (t > 0 && totalLength > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      /*\n       * totalLength is set from a ref and is not updated in the first tick of the animation.\n       * It defaults to zero which is exactly what we want here because we want to grow from zero,\n       * however the same happens when the data change.\n       *\n       * In that case we want to remember the previous length and continue from there, and only animate the shape.\n       *\n       * Therefore the totalLength > 0 check.\n       *\n       * The Animate is about to fire handleAnimationStart which will update the state\n       * and cause a re-render and read a new proper totalLength which will be used in the next tick\n       * and update the longestAnimatedLengthRef.\n       */\n      // eslint-disable-next-line no-param-reassign\n      longestAnimatedLengthRef.current = curLength;\n    }\n    return /*#__PURE__*/React.createElement(StaticCurve, {\n      props: props,\n      points: points,\n      clipPathId: clipPathId,\n      pathRef: pathRef,\n      showLabels: !isAnimating,\n      strokeDasharray: currentStrokeDasharray\n    });\n  });\n}\nfunction RenderCurve(_ref5) {\n  var {\n    clipPathId,\n    props\n  } = _ref5;\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var longestAnimatedLengthRef = useRef(0);\n  var pathRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && prevPoints !== points) {\n    return /*#__PURE__*/React.createElement(CurveWithAnimation, {\n      props: props,\n      clipPathId: clipPathId,\n      previousPointsRef: previousPointsRef,\n      longestAnimatedLengthRef: longestAnimatedLengthRef,\n      pathRef: pathRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticCurve, {\n    props: props,\n    points: points,\n    clipPathId: clipPathId,\n    pathRef: pathRef,\n    showLabels: true\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value: dataPoint.value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n  };\n};\nclass LineWithState extends Component {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-line-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      xAxisId,\n      yAxisId,\n      top,\n      left,\n      width,\n      height,\n      id,\n      needClip,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-line', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderCurve, {\n      props: this.props,\n      clipPathId: clipPathId\n    }), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, /*#__PURE__*/React.createElement(SetErrorBarContext, {\n      xAxisId: xAxisId,\n      yAxisId: yAxisId,\n      data: points,\n      dataPointFormatter: errorBarDataPointFormatter,\n      errorBarOffset: 0\n    }, this.props.children))), /*#__PURE__*/React.createElement(ActivePoints, {\n      activeDot: this.props.activeDot,\n      points: points,\n      mainColor: this.props.stroke,\n      itemDataKey: this.props.dataKey\n    }));\n  }\n}\nvar defaultLineProps = {\n  activeDot: true,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: true,\n  fill: '#fff',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  label: false,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction LineImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultLineProps),\n    {\n      activeDot,\n      animateNewValues,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      hide,\n      isAnimationActive,\n      label,\n      legendType,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var {\n    height,\n    width,\n    x: left,\n    y: top\n  } = usePlotArea();\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var lineSettings = useMemo(() => ({\n    dataKey: props.dataKey,\n    data: props.data\n  }), [props.dataKey, props.data]);\n  var points = useAppSelector(state => selectLinePoints(state, xAxisId, yAxisId, isPanorama, lineSettings));\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Cannot render Line in an unsupported layout\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LineWithState, _extends({}, everythingElse, {\n    connectNulls: connectNulls,\n    dot: dot,\n    activeDot: activeDot,\n    animateNewValues: animateNewValues,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    hide: hide,\n    label: label,\n    legendType: legendType,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    points: points,\n    layout: layout,\n    height: height,\n    width: width,\n    left: left,\n    top: top,\n    needClip: needClip\n  }));\n}\nexport function computeLinePoints(_ref6) {\n  var {\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  } = _ref6;\n  return displayedData.map((entry, index) => {\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isNullish(value) ? null : yAxis.scale(value),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNullish(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n}\nexport class Line extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"line\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey\n      // line doesn't stack\n      ,\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(LineImpl, this.props));\n  }\n}\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", defaultLineProps);"], "names": [], "mappings": ";;;;AAUA,gDAAgD;AAChD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnCA,IAAI,YAAY;IAAC;IAAQ;IAAU;IAAgB;CAAW,EAC5D,aAAa;IAAC;IAAa;IAAoB;IAAkB;IAAqB;IAAmB;IAAgB;IAAO;IAAQ;IAAqB;IAAS;IAAc;IAAW;CAAU;AAC3M,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACtM,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BnR;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED,IAAI,mCAAmC,CAAA;IACrC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,MAAM,EACN,UAAU,EACV,IAAI,EACL,GAAG;IACJ,OAAO;QAAC;YACN,UAAU;YACV;YACA,MAAM;YACN,OAAO;YACP,OAAO,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAChC,SAAS;QACX;KAAE;AACJ;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,MAAM,EACN,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACL,GAAG;IACJ,OAAO;QACL,mBAAmB;QACnB,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;YACA,SAAS;YACT,MAAM,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC/B;YACA,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,MAAM;YACnB;QACF;IACF;AACF;AACA,IAAI,gCAAgC,CAAC,aAAa;IAChD,OAAO,GAAG,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,cAAc,QAAQ;AAC/D;AACA,SAAS,OAAO,KAAK,EAAE,KAAK;IAC1B,IAAI,YAAY,MAAM,MAAM,GAAG,MAAM,IAAI;WAAI;QAAO;KAAE,GAAG;IACzD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;QAC9B,SAAS;eAAI;eAAW;SAAU;IACpC;IACA,OAAO;AACT;AACA,IAAI,qBAAqB,CAAC,QAAQ,aAAa;IAC7C,IAAI,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM;IAEnD,4EAA4E;IAC5E,IAAI,CAAC,YAAY;QACf,OAAO,8BAA8B,aAAa;IACpD;IACA,IAAI,QAAQ,KAAK,KAAK,CAAC,SAAS;IAChC,IAAI,eAAe,SAAS;IAC5B,IAAI,aAAa,cAAc;IAC/B,IAAI,cAAc,EAAE;IACpB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,MAAM,MAAM,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,EAAE,EAAG;QAC/D,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG,cAAc;YACjC,cAAc;mBAAI,MAAM,KAAK,CAAC,GAAG;gBAAI,eAAe;aAAI;YACxD;QACF;IACF;IACA,IAAI,aAAa,YAAY,MAAM,GAAG,MAAM,IAAI;QAAC;QAAG;KAAW,GAAG;QAAC;KAAW;IAC9E,OAAO;WAAI,OAAO,OAAO;WAAW;WAAgB;KAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,GAAG,MAAM,CAAC,MAAM,OAAO,IAAI,CAAC;AAC1G;AACA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI;IACJ,IAAI,WAAW,GAAE,qMAAA,CAAA,iBAAoB,CAAC,SAAS;QAC7C,UAAU,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,QAAQ;IACpD,OAAO,IAAI,OAAO,WAAW,YAAY;QACvC,UAAU,OAAO;IACnB,OAAO;QACL,IAAI,YAAY,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO,WAAW,YAAY,OAAO,SAAS,GAAG;QAC3F,UAAU,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,+IAAA,CAAA,MAAG,EAAE,SAAS,CAAC,GAAG,OAAO;YAClE,WAAW;QACb;IACF;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,MAAM,EAAE,GAAG;IACnC,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,KAAK;QACP,OAAO;IACT;IACA,OAAO,OAAO,MAAM,KAAK;AAC3B;AACA,SAAS,KAAK,IAAI;IAChB,IAAI,EACF,UAAU,EACV,MAAM,EACN,KAAK,EACN,GAAG;IACJ,IAAI,EACF,GAAG,EACH,OAAO,EACP,QAAQ,EACT,GAAG;IACJ,IAAI,CAAC,iBAAiB,QAAQ,MAAM;QAClC,OAAO;IACT;IACA,IAAI,UAAU,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD,EAAE;IACxB,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACnC,IAAI,iBAAiB,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,KAAK;IACtC,IAAI,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO;QAC5B,IAAI,WAAW,cAAc,cAAc,cAAc;YACvD,KAAK,OAAO,MAAM,CAAC;YACnB,GAAG;QACL,GAAG,YAAY,iBAAiB,CAAC,GAAG;YAClC,OAAO;YACP,IAAI,MAAM,CAAC;YACX,IAAI,MAAM,CAAC;YACX;YACA,OAAO,MAAM,KAAK;YAClB,SAAS,MAAM,OAAO;YACtB;QACF;QACA,OAAO,cAAc,KAAK;IAC5B;IACA,IAAI,YAAY;QACd,UAAU,WAAW,iBAAiB,MAAM,CAAC,UAAU,KAAK,SAAS,MAAM,CAAC,YAAY,OAAO;IACjG;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;QACtD,WAAW;QACX,KAAK;IACP,GAAG,YAAY;AACjB;AACA,SAAS,YAAY,KAAK;IACxB,IAAI,EACF,UAAU,EACV,OAAO,EACP,MAAM,EACN,eAAe,EACf,KAAK,EACL,UAAU,EACX,GAAG;IACJ,IAAI,EACA,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,QAAQ,EACT,GAAG,OACJ,SAAS,yBAAyB,OAAO;IAC3C,IAAI,aAAa,cAAc,cAAc,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,QAAQ,CAAC,GAAG;QAC/E,MAAM;QACN,WAAW;QACX,UAAU,WAAW,iBAAiB,MAAM,CAAC,YAAY,OAAO;QAChE;QACA;QACA;QACA;QACA,iBAAiB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,MAAM,eAAe;IACnH;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,IAAI,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,QAAK,EAAE,SAAS,CAAC,GAAG,YAAY;QACpM,SAAS;IACX,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,MAAM;QAC1C,QAAQ;QACR,YAAY;QACZ,OAAO;IACT,IAAI,cAAc,yJAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,OAAO;AACxD;AACA,SAAS,eAAe,SAAS;IAC/B,IAAI;QACF,OAAO,aAAa,UAAU,cAAc,IAAI,UAAU,cAAc,MAAM;IAChF,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF;AACA,SAAS,mBAAmB,KAAK;IAC/B,IAAI,EACF,UAAU,EACV,KAAK,EACL,OAAO,EACP,iBAAiB,EACjB,wBAAwB,EACzB,GAAG;IACJ,IAAI,EACF,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,KAAK,EACL,MAAM,EACN,cAAc,EACd,gBAAgB,EACjB,GAAG;IACJ,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,cAAc,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IACxC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,OAAO,mBAAmB,YAAY;YACxC;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAe;IACnB,IAAI,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,OAAO,qBAAqB,YAAY;YAC1C;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAiB;IACrB,IAAI,cAAc,eAAe,QAAQ,OAAO;IAChD;;;;;;;;;;;;;;;;GAgBC,GACD,IAAI,gBAAgB,yBAAyB,OAAO;IACpD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAO,EAAE;QAC/C,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,MAAM;YACJ,GAAG;QACL;QACA,IAAI;YACF,GAAG;QACL;QACA,gBAAgB;QAChB,kBAAkB;QAClB,KAAK;IACP,GAAG,CAAA;QACD,IAAI,EACF,CAAC,EACF,GAAG;QACJ,IAAI,eAAe,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe,cAAc;QAClE,IAAI,YAAY,KAAK,GAAG,CAAC,aAAa,IAAI;QAC1C,IAAI;QACJ,IAAI,iBAAiB;YACnB,IAAI,QAAQ,GAAG,MAAM,CAAC,iBAAiB,KAAK,CAAC,aAAa,GAAG,CAAC,CAAA,MAAO,WAAW;YAChF,yBAAyB,mBAAmB,WAAW,aAAa;QACtE,OAAO;YACL,yBAAyB,8BAA8B,aAAa;QACtE;QACA,IAAI,YAAY;YACd,IAAI,uBAAuB,WAAW,MAAM,GAAG,OAAO,MAAM;YAC5D,IAAI,WAAW,MAAM,IAAI,SAAS,OAAO,GAAG,CAAC,CAAC,OAAO;gBACnD,IAAI,iBAAiB,KAAK,KAAK,CAAC,QAAQ;gBACxC,IAAI,UAAU,CAAC,eAAe,EAAE;oBAC9B,IAAI,OAAO,UAAU,CAAC,eAAe;oBACrC,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC;oBACrD,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC;oBACrD,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;wBACjD,GAAG,cAAc;wBACjB,GAAG,cAAc;oBACnB;gBACF;gBAEA,mDAAmD;gBACnD,IAAI,kBAAkB;oBACpB,IAAI,iBAAiB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,GAAG,MAAM,CAAC;oBACzD,IAAI,iBAAiB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,GAAG,MAAM,CAAC;oBAC1D,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;wBACjD,GAAG,eAAe;wBAClB,GAAG,eAAe;oBACpB;gBACF;gBACA,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;oBACjD,GAAG,MAAM,CAAC;oBACV,GAAG,MAAM,CAAC;gBACZ;YACF;YACA,6CAA6C;YAC7C,kBAAkB,OAAO,GAAG;YAC5B,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa;gBACnD,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,SAAS;gBACT,YAAY,CAAC;gBACb,iBAAiB;YACnB;QACF;QAEA;;;;;;;;;;;;KAYC,GACD,IAAI,IAAI,KAAK,cAAc,GAAG;YAC5B,6CAA6C;YAC7C,kBAAkB,OAAO,GAAG;YAC5B;;;;;;;;;;;;OAYC,GACD,6CAA6C;YAC7C,yBAAyB,OAAO,GAAG;QACrC;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa;YACnD,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,YAAY,CAAC;YACb,iBAAiB;QACnB;IACF;AACF;AACA,SAAS,YAAY,KAAK;IACxB,IAAI,EACF,UAAU,EACV,KAAK,EACN,GAAG;IACJ,IAAI,EACF,MAAM,EACN,iBAAiB,EAClB,GAAG;IACJ,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,2BAA2B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtC,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACrB,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,qBAAqB,UAAU,OAAO,MAAM,IAAI,eAAe,QAAQ;QACzE,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,oBAAoB;YAC1D,OAAO;YACP,YAAY;YACZ,mBAAmB;YACnB,0BAA0B;YAC1B,SAAS;QACX;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa;QACnD,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,YAAY;IACd;AACF;AACA,IAAI,6BAA6B,CAAC,WAAW;IAC3C,OAAO;QACL,GAAG,UAAU,CAAC;QACd,GAAG,UAAU,CAAC;QACd,OAAO,UAAU,KAAK;QACtB,uEAAuE;QACvE,UAAU,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,OAAO,EAAE;IACjD;AACF;AACA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;IACvC;IACA,SAAS;QACP,IAAI;QACJ,IAAI,EACF,IAAI,EACJ,GAAG,EACH,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACN,EAAE,EACF,QAAQ,EACR,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,MAAM;YACR,OAAO;QACT;QACA,IAAI,aAAa,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB;QACvC,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,IAAI,CAAC,EAAE,GAAG;QAC3C,IAAI,EACF,IAAI,CAAC,EACL,cAAc,CAAC,EAChB,GAAG,CAAC,eAAe,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;YAChG,GAAG;YACH,aAAa;QACf;QACA,IAAI,UAAU,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD,EAAE;QACxB,IAAI,UAAU,IAAI,IAAI;QACtB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YACpG,WAAW;QACb,GAAG,YAAY,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qKAAA,CAAA,wBAAqB,EAAE;YACpH,YAAY;YACZ,SAAS;YACT,SAAS;QACX,IAAI,CAAC,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;YAC3D,IAAI,iBAAiB,MAAM,CAAC;QAC9B,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC1C,GAAG,OAAO,UAAU;YACpB,GAAG,MAAM,UAAU;YACnB,OAAO,QAAQ;YACf,QAAQ,SAAS;QACnB,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa;YAClD,OAAO,IAAI,CAAC,KAAK;YACjB,YAAY;QACd,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,wJAAA,CAAA,gCAA6B,EAAE;YAClE,WAAW,WAAW,eAAe,MAAM;QAC7C,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,qBAAkB,EAAE;YACtD,SAAS;YACT,SAAS;YACT,MAAM;YACN,oBAAoB;YACpB,gBAAgB;QAClB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,eAAY,EAAE;YACxE,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,QAAQ;YACR,WAAW,IAAI,CAAC,KAAK,CAAC,MAAM;YAC5B,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO;QACjC;IACF;AACF;AACA,IAAI,mBAAmB;IACrB,WAAW;IACX,kBAAkB;IAClB,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;IACjB,cAAc;IACd,KAAK;IACL,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,iJAAA,CAAA,SAAM,CAAC,KAAK;IAChC,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,SAAS;IACT,SAAS;AACX;AACA,SAAS,SAAS,KAAK;IACrB,IAAI,uBAAuB,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,mBACpD,EACE,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,YAAY,EACZ,GAAG,EACH,IAAI,EACJ,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,OAAO,EACP,OAAO,EACR,GAAG,sBACJ,iBAAiB,yBAAyB,sBAAsB;IAClE,IAAI,EACF,QAAQ,EACT,GAAG,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,SAAS;IAC1B,IAAI,EACF,MAAM,EACN,KAAK,EACL,GAAG,IAAI,EACP,GAAG,GAAG,EACP,GAAG,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD;IACd,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC1B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAChC,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC,GAAG;QAAC,MAAM,OAAO;QAAE,MAAM,IAAI;KAAC;IAC/B,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,SAAS,SAAS,YAAY;IAC3F,IAAI,WAAW,gBAAgB,WAAW,YAAY;QACpD,8CAA8C;QAC9C,OAAO;IACT;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe,SAAS,CAAC,GAAG,gBAAgB;QAClF,cAAc;QACd,KAAK;QACL,WAAW;QACX,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,mBAAmB;QACnB,MAAM;QACN,OAAO;QACP,YAAY;QACZ,SAAS;QACT,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,KAAK;QACL,UAAU;IACZ;AACF;AACO,SAAS,kBAAkB,KAAK;IACrC,IAAI,EACF,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,EACV,OAAO,EACP,QAAQ,EACR,aAAa,EACd,GAAG;IACJ,OAAO,cAAc,GAAG,CAAC,CAAC,OAAO;QAC/B,uEAAuE;QACvE,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QACrC,IAAI,WAAW,cAAc;YAC3B,OAAO;gBACL,GAAG,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;oBACzB,MAAM;oBACN,OAAO;oBACP;oBACA;oBACA;gBACF;gBACA,GAAG,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,OAAO,MAAM,KAAK,CAAC;gBACzC;gBACA,SAAS;YACX;QACF;QACA,OAAO;YACL,GAAG,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,OAAO,MAAM,KAAK,CAAC;YACzC,GAAG,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;gBACzB,MAAM;gBACN,OAAO;gBACP;gBACA;gBACA;YACF;YACA;YACA,SAAS;QACX;IACF;AACF;AACO,MAAM,aAAa,qMAAA,CAAA,gBAAa;IACrC,SAAS;QACP,mGAAmG;QACnG,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,gCAA6B,EAAE;YACrE,MAAM;YACN,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAG3B,SAAS;YACT,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS;QACX,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,mBAAgB,EAAE;YACpD,eAAe,iCAAiC,IAAI,CAAC,KAAK;QAC5D,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mKAAA,CAAA,0BAAuB,EAAE;YAC5D,IAAI;YACJ,MAAM,IAAI,CAAC,KAAK;QAClB,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,UAAU,IAAI,CAAC,KAAK;IAC3D;AACF;AACA,gBAAgB,MAAM,eAAe;AACrC,gBAAgB,MAAM,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2903, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/Area.js"], "sourcesContent": ["var _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\"],\n  _excluded2 = [\"activeDot\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"fill\", \"fillOpacity\", \"hide\", \"isAnimationActive\", \"legendType\", \"stroke\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolate, isNan, isNullish, isNumber, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectArea } from '../state/selectors/areaSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { useChartName } from '../state/selectors/selectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAppSelector } from '../state/hooks';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isWellBehavedNumber } from '../util/isWellBehavedNumber';\nimport { Animate } from '../animation/Animate';\nimport { usePlotArea } from '../hooks';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit\n    }\n  };\n}\nvar renderDotItem = (option, props) => {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n};\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    needClip,\n    dot,\n    dataKey\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var areaProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, areaProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : undefined\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-area-dots\"\n  }, dotsProps), dots);\n}\nfunction StaticArea(_ref2) {\n  var {\n    points,\n    baseLine,\n    needClip,\n    clipPathId,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      layout,\n      type,\n      stroke,\n      connectNulls,\n      isRange\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Layer, {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : undefined\n  }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n    points: points,\n    connectNulls: connectNulls,\n    type: type,\n    baseLine: baseLine,\n    layout: layout,\n    stroke: \"none\",\n    className: \"recharts-area-area\"\n  })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: points\n  })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: baseLine\n  }))), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    props: props,\n    clipPathId: clipPathId\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction VerticalRect(_ref3) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref3;\n  var startY = points[0].y;\n  var endY = points[points.length - 1].y;\n  if (!isWellBehavedNumber(startY) || !isWellBehavedNumber(endY)) {\n    return null;\n  }\n  var height = alpha * Math.abs(startY - endY);\n  var maxX = Math.max(...points.map(entry => entry.x || 0));\n  if (isNumber(baseLine)) {\n    maxX = Math.max(baseLine, maxX);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxX = Math.max(...baseLine.map(entry => entry.x || 0), maxX);\n  }\n  if (isNumber(maxX)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: 0,\n      y: startY < endY ? startY : startY - height,\n      width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n      height: Math.floor(height)\n    });\n  }\n  return null;\n}\nfunction HorizontalRect(_ref4) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref4;\n  var startX = points[0].x;\n  var endX = points[points.length - 1].x;\n  if (!isWellBehavedNumber(startX) || !isWellBehavedNumber(endX)) {\n    return null;\n  }\n  var width = alpha * Math.abs(startX - endX);\n  var maxY = Math.max(...points.map(entry => entry.y || 0));\n  if (isNumber(baseLine)) {\n    maxY = Math.max(baseLine, maxY);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxY = Math.max(...baseLine.map(entry => entry.y || 0), maxY);\n  }\n  if (isNumber(maxY)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: startX < endX ? startX : startX - width,\n      y: 0,\n      width: width,\n      height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n    });\n  }\n  return null;\n}\nfunction ClipRect(_ref5) {\n  var {\n    alpha,\n    layout,\n    points,\n    baseLine,\n    strokeWidth\n  } = _ref5;\n  if (layout === 'vertical') {\n    return /*#__PURE__*/React.createElement(VerticalRect, {\n      alpha: alpha,\n      points: points,\n      baseLine: baseLine,\n      strokeWidth: strokeWidth\n    });\n  }\n  return /*#__PURE__*/React.createElement(HorizontalRect, {\n    alpha: alpha,\n    points: points,\n    baseLine: baseLine,\n    strokeWidth: strokeWidth\n  });\n}\nfunction AreaWithAnimation(_ref6) {\n  var {\n    needClip,\n    clipPathId,\n    props,\n    previousPointsRef,\n    previousBaselineRef\n  } = _ref6;\n  var {\n    points,\n    baseLine,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-area-');\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref7 => {\n    var {\n      t\n    } = _ref7;\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepPoints =\n      /*\n       * Here it is important that at the very end of the animation, on the last frame,\n       * we render the original points without any interpolation.\n       * This is needed because the code above is checking for reference equality to decide if the animation should run\n       * and if we create a new array instance (even if the numbers were the same)\n       * then we would break animations.\n       */\n      t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolate(prev.x, entry.x, t),\n            y: interpolate(prev.y, entry.y, t)\n          });\n        }\n        return entry;\n      });\n      var stepBaseLine;\n      if (isNumber(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, baseLine, t);\n      } else if (isNullish(baseLine) || isNan(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, 0, t);\n      } else {\n        stepBaseLine = baseLine.map((entry, index) => {\n          var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n          if (Array.isArray(prevBaseLine) && prevBaseLine[prevPointIndex]) {\n            var prev = prevBaseLine[prevPointIndex];\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolate(prev.x, entry.x, t),\n              y: interpolate(prev.y, entry.y, t)\n            });\n          }\n          return entry;\n        });\n      }\n      if (t > 0) {\n        /*\n         * We need to keep the refs in the parent component because we need to remember the last shape of the animation\n         * even if AreaWithAnimation is unmounted as that happens when changing props.\n         *\n         * And we need to update the refs here because here is where the interpolation is computed.\n         * Eslint doesn't like changing function arguments, but we need it so here is an eslint-disable.\n         */\n        // eslint-disable-next-line no-param-reassign\n        previousPointsRef.current = stepPoints;\n        // eslint-disable-next-line no-param-reassign\n        previousBaselineRef.current = stepBaseLine;\n      }\n      return /*#__PURE__*/React.createElement(StaticArea, {\n        points: stepPoints,\n        baseLine: stepBaseLine,\n        needClip: needClip,\n        clipPathId: clipPathId,\n        props: props,\n        showLabels: !isAnimating\n      });\n    }\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      // eslint-disable-next-line no-param-reassign\n      previousBaselineRef.current = baseLine;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"animationClipPath-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(ClipRect, {\n      alpha: t,\n      points: points,\n      baseLine: baseLine,\n      layout: props.layout,\n      strokeWidth: props.strokeWidth\n    }))), /*#__PURE__*/React.createElement(Layer, {\n      clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n    }, /*#__PURE__*/React.createElement(StaticArea, {\n      points: points,\n      baseLine: baseLine,\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      showLabels: true\n    })));\n  });\n}\n\n/*\n * This components decides if the area should be animated or not.\n * It also holds the state of the animation.\n */\nfunction RenderArea(_ref8) {\n  var {\n    needClip,\n    clipPathId,\n    props\n  } = _ref8;\n  var {\n    points,\n    baseLine,\n    isAnimationActive\n  } = props;\n\n  /*\n   * These two must be refs, not state!\n   * Because we want to store the most recent shape of the animation in case we have to interrupt the animation;\n   * that happens when user initiates another animation before the current one finishes.\n   *\n   * If this was a useState, then every step in the animation would trigger a re-render.\n   * So, useRef it is.\n   */\n  var previousPointsRef = useRef(null);\n  var previousBaselineRef = useRef();\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  if (isAnimationActive &&\n  /*\n   * Here it's important that we unmount of AreaWithAnimation in case points are undefined\n   * - this will make sure to interrupt the animation if it's running.\n   * We still get to keep the last shape of the animation in the refs above.\n   */\n  points && points.length && (prevPoints !== points || prevBaseLine !== baseLine)) {\n    return /*#__PURE__*/React.createElement(AreaWithAnimation, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      previousPointsRef: previousPointsRef,\n      previousBaselineRef: previousBaselineRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticArea, {\n    points: points,\n    baseLine: baseLine,\n    needClip: needClip,\n    clipPathId: clipPathId,\n    props: props,\n    showLabels: true\n  });\n}\nclass AreaWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-area-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      top,\n      left,\n      needClip,\n      xAxisId,\n      yAxisId,\n      width,\n      height,\n      id,\n      baseLine\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderArea, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: this.props\n    })), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }), this.props.isRange && Array.isArray(baseLine) && /*#__PURE__*/React.createElement(ActivePoints, {\n      points: baseLine,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nvar defaultAreaProps = {\n  activeDot: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: false,\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'line',\n  stroke: '#3182bd',\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction AreaImpl(props) {\n  var _useAppSelector;\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultAreaProps),\n    {\n      activeDot,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      fill,\n      fillOpacity,\n      hide,\n      isAnimationActive,\n      legendType,\n      stroke,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var isPanorama = useIsPanorama();\n  var areaSettings = useMemo(() => ({\n    baseValue: props.baseValue,\n    stackId: props.stackId,\n    connectNulls,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.baseValue, props.stackId, connectNulls, props.data, props.dataKey]);\n  var {\n    points,\n    isRange,\n    baseLine\n  } = (_useAppSelector = useAppSelector(state => selectArea(state, xAxisId, yAxisId, isPanorama, areaSettings))) !== null && _useAppSelector !== void 0 ? _useAppSelector : {};\n  var {\n    height,\n    width,\n    x: left,\n    y: top\n  } = usePlotArea();\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Can't render Area in an unsupported layout\n    return null;\n  }\n  if (chartName !== 'AreaChart' && chartName !== 'ComposedChart') {\n    // There is nothing stopping us from rendering Area in other charts, except for historical reasons. Do we want to allow that?\n    return null;\n  }\n\n  /*\n   * It is important to NOT have this condition here,\n   * because we need the Animate inside to receive an empty state\n   * so that it can properly reset its internal state and start a new animation.\n   */\n  // if (!points || !points.length) {\n  //   return null;\n  // }\n\n  return /*#__PURE__*/React.createElement(AreaWithState, _extends({}, everythingElse, {\n    activeDot: activeDot,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    baseLine: baseLine,\n    connectNulls: connectNulls,\n    dot: dot,\n    fill: fill,\n    fillOpacity: fillOpacity,\n    height: height,\n    hide: hide,\n    layout: layout,\n    isAnimationActive: isAnimationActive,\n    isRange: isRange,\n    legendType: legendType,\n    needClip: needClip,\n    points: points,\n    stroke: stroke,\n    width: width,\n    left: left,\n    top: top,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  }));\n}\nexport var getBaseValue = (layout, chartBaseValue, itemBaseValue, xAxis, yAxis) => {\n  // The baseValue can be defined both on the AreaChart, and on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue)) {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error d3scale .domain() returns unknown, Math.max expects number\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n};\nexport function computeArea(_ref9) {\n  var {\n    areaSettings: {\n      connectNulls,\n      baseValue: itemBaseValue,\n      dataKey\n    },\n    stackedData,\n    layout,\n    chartBaseValue,\n    xAxis,\n    yAxis,\n    displayedData,\n    dataStartIndex,\n    xAxisTicks,\n    yAxisTicks,\n    bandSize\n  } = _ref9;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = getBaseValue(layout, chartBaseValue, itemBaseValue, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map((entry, index) => {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && !connectNulls && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(entry => {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return {\n    points,\n    baseLine,\n    isRange\n  };\n}\nexport class Area extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"area\",\n      data: this.props.data,\n      dataKey: this.props.dataKey,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(AreaImpl, this.props));\n  }\n}\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", defaultAreaProps);"], "names": [], "mappings": ";;;;;AAUA,gDAAgD;AAChD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA,IAAI,YAAY;IAAC;IAAU;IAAQ;IAAU;IAAgB;CAAU,EACrE,aAAa;IAAC;IAAa;IAAkB;IAAqB;IAAmB;IAAgB;IAAO;IAAQ;IAAe;IAAQ;IAAqB;IAAc;IAAU;IAAW;CAAU;AAC/M,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACtM,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;AACvT,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BnR;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED,SAAS,mBAAmB,MAAM,EAAE,IAAI;IACtC,OAAO,UAAU,WAAW,SAAS,SAAS;AAChD;AACA,IAAI,mCAAmC,CAAA;IACrC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,UAAU,EACV,IAAI,EACL,GAAG;IACJ,OAAO;QAAC;YACN,UAAU;YACV;YACA,MAAM;YACN,OAAO,mBAAmB,QAAQ;YAClC,OAAO,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAChC,SAAS;QACX;KAAE;AACJ;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,MAAM,EACN,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACL,GAAG;IACJ,OAAO;QACL,mBAAmB;QACnB,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;YACA,SAAS;YACT,MAAM,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC/B;YACA,MAAM,MAAM,WAAW;YACvB,OAAO,mBAAmB,QAAQ;YAClC;QACF;IACF;AACF;AACA,IAAI,gBAAgB,CAAC,QAAQ;IAC3B,IAAI;IACJ,IAAI,WAAW,GAAE,qMAAA,CAAA,iBAAoB,CAAC,SAAS;QAC7C,UAAU,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,QAAQ;IACpD,OAAO,IAAI,OAAO,WAAW,YAAY;QACvC,UAAU,OAAO;IACnB,OAAO;QACL,IAAI,YAAY,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO,WAAW,YAAY,OAAO,SAAS,GAAG;QAC3F,UAAU,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,+IAAA,CAAA,MAAG,EAAE,SAAS,CAAC,GAAG,OAAO;YAClE,WAAW;QACb;IACF;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,MAAM,EAAE,GAAG;IACnC,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,KAAK;QACP,OAAO;IACT;IACA,OAAO,OAAO,MAAM,KAAK;AAC3B;AACA,SAAS,KAAK,IAAI;IAChB,IAAI,EACF,UAAU,EACV,MAAM,EACN,KAAK,EACN,GAAG;IACJ,IAAI,EACF,QAAQ,EACR,GAAG,EACH,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,iBAAiB,QAAQ,MAAM;QAClC,OAAO;IACT;IACA,IAAI,UAAU,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD,EAAE;IACxB,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACnC,IAAI,iBAAiB,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,KAAK;IACtC,IAAI,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO;QAC5B,IAAI,WAAW,cAAc,cAAc,cAAc;YACvD,KAAK,OAAO,MAAM,CAAC;YACnB,GAAG;QACL,GAAG,YAAY,iBAAiB,CAAC,GAAG;YAClC,OAAO;YACP,IAAI,MAAM,CAAC;YACX,IAAI,MAAM,CAAC;YACX;YACA,OAAO,MAAM,KAAK;YAClB,SAAS,MAAM,OAAO;YACtB;QACF;QACA,OAAO,cAAc,KAAK;IAC5B;IACA,IAAI,YAAY;QACd,UAAU,WAAW,iBAAiB,MAAM,CAAC,UAAU,KAAK,SAAS,MAAM,CAAC,YAAY,OAAO;IACjG;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;QACtD,WAAW;IACb,GAAG,YAAY;AACjB;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,EACF,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,EACL,UAAU,EACX,GAAG;IACJ,IAAI,EACA,MAAM,EACN,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,OAAO,EACR,GAAG,OACJ,SAAS,yBAAyB,OAAO;IAC3C,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,IAAI,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;QAC3K,UAAU,WAAW,iBAAiB,MAAM,CAAC,YAAY,OAAO;IAClE,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,QAAK,EAAE,SAAS,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO;QACjF,QAAQ;QACR,cAAc;QACd,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;IACb,KAAK,WAAW,UAAU,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,QAAK,EAAE,SAAS,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAQ;QACxG,WAAW;QACX,QAAQ;QACR,MAAM;QACN,cAAc;QACd,MAAM;QACN,QAAQ;IACV,KAAK,WAAW,UAAU,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,QAAK,EAAE,SAAS,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAQ;QACnH,WAAW;QACX,QAAQ;QACR,MAAM;QACN,cAAc;QACd,MAAM;QACN,QAAQ;IACV,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,MAAM;QAC3C,QAAQ;QACR,OAAO;QACP,YAAY;IACd,IAAI,cAAc,yJAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,OAAO;AACxD;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,EACF,KAAK,EACL,QAAQ,EACR,MAAM,EACN,WAAW,EACZ,GAAG;IACJ,IAAI,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;IACxB,IAAI,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;QAC9D,OAAO;IACT;IACA,IAAI,SAAS,QAAQ,KAAK,GAAG,CAAC,SAAS;IACvC,IAAI,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,CAAC,IAAI;IACtD,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QACtB,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,EAAE;QACjE,OAAO,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,QAAS,MAAM,CAAC,IAAI,IAAI;IAC1D;IACA,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAClB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,GAAG;YACH,GAAG,SAAS,OAAO,SAAS,SAAS;YACrC,OAAO,OAAO,CAAC,cAAc,SAAS,GAAG,MAAM,CAAC,cAAc,MAAM,CAAC;YACrE,QAAQ,KAAK,KAAK,CAAC;QACrB;IACF;IACA,OAAO;AACT;AACA,SAAS,eAAe,KAAK;IAC3B,IAAI,EACF,KAAK,EACL,QAAQ,EACR,MAAM,EACN,WAAW,EACZ,GAAG;IACJ,IAAI,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;IACxB,IAAI,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;QAC9D,OAAO;IACT;IACA,IAAI,QAAQ,QAAQ,KAAK,GAAG,CAAC,SAAS;IACtC,IAAI,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,CAAC,IAAI;IACtD,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QACtB,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,EAAE;QACjE,OAAO,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,QAAS,MAAM,CAAC,IAAI,IAAI;IAC1D;IACA,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAClB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,GAAG,SAAS,OAAO,SAAS,SAAS;YACrC,GAAG;YACH,OAAO;YACP,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,cAAc,SAAS,GAAG,MAAM,CAAC,cAAc,MAAM,CAAC;QACnF;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,KAAK;IACrB,IAAI,EACF,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACZ,GAAG;IACJ,IAAI,WAAW,YAAY;QACzB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,cAAc;YACpD,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;QACf;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,gBAAgB;QACtD,OAAO;QACP,QAAQ;QACR,UAAU;QACV,aAAa;IACf;AACF;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,EACF,QAAQ,EACR,UAAU,EACV,KAAK,EACL,iBAAiB,EACjB,mBAAmB,EACpB,GAAG;IACJ,IAAI,EACF,MAAM,EACN,QAAQ,EACR,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,cAAc,EACf,GAAG;IACJ,IAAI,cAAc,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IACxC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,OAAO,mBAAmB,YAAY;YACxC;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAe;IACnB,IAAI,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,OAAO,qBAAqB,YAAY;YAC1C;QACF;QACA,eAAe;IACjB,GAAG;QAAC;KAAiB;IACrB,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,eAAe,oBAAoB,OAAO;IAC9C,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAO,EAAE;QAC/C,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,MAAM;YACJ,GAAG;QACL;QACA,IAAI;YACF,GAAG;QACL;QACA,gBAAgB;QAChB,kBAAkB;QAClB,KAAK;IACP,GAAG,CAAA;QACD,IAAI,EACF,CAAC,EACF,GAAG;QACJ,IAAI,YAAY;YACd,IAAI,uBAAuB,WAAW,MAAM,GAAG,OAAO,MAAM;YAC5D,IAAI,aACJ;;;;;;OAMC,GACD,MAAM,IAAI,SAAS,OAAO,GAAG,CAAC,CAAC,OAAO;gBACpC,IAAI,iBAAiB,KAAK,KAAK,CAAC,QAAQ;gBACxC,IAAI,UAAU,CAAC,eAAe,EAAE;oBAC9B,IAAI,OAAO,UAAU,CAAC,eAAe;oBACrC,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;wBACjD,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE;wBAChC,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE;oBAClC;gBACF;gBACA,OAAO;YACT;YACA,IAAI;YACJ,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;gBACtB,eAAe,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,cAAc,UAAU;YACrD,OAAO,IAAI,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,WAAW;gBACjD,eAAe,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,cAAc,GAAG;YAC9C,OAAO;gBACL,eAAe,SAAS,GAAG,CAAC,CAAC,OAAO;oBAClC,IAAI,iBAAiB,KAAK,KAAK,CAAC,QAAQ;oBACxC,IAAI,MAAM,OAAO,CAAC,iBAAiB,YAAY,CAAC,eAAe,EAAE;wBAC/D,IAAI,OAAO,YAAY,CAAC,eAAe;wBACvC,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;4BACjD,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE;4BAChC,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE;wBAClC;oBACF;oBACA,OAAO;gBACT;YACF;YACA,IAAI,IAAI,GAAG;gBACT;;;;;;SAMC,GACD,6CAA6C;gBAC7C,kBAAkB,OAAO,GAAG;gBAC5B,6CAA6C;gBAC7C,oBAAoB,OAAO,GAAG;YAChC;YACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;gBAClD,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,YAAY,CAAC;YACf;QACF;QACA,IAAI,IAAI,GAAG;YACT,6CAA6C;YAC7C,kBAAkB,OAAO,GAAG;YAC5B,6CAA6C;YAC7C,oBAAoB,OAAO,GAAG;QAChC;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;YAC/I,IAAI,qBAAqB,MAAM,CAAC;QAClC,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,UAAU;YAC5C,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ,MAAM,MAAM;YACpB,aAAa,MAAM,WAAW;QAChC,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YAC5C,UAAU,0BAA0B,MAAM,CAAC,YAAY;QACzD,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;YAC9C,QAAQ;YACR,UAAU;YACV,UAAU;YACV,YAAY;YACZ,OAAO;YACP,YAAY;QACd;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,IAAI,EACF,QAAQ,EACR,UAAU,EACV,KAAK,EACN,GAAG;IACJ,IAAI,EACF,MAAM,EACN,QAAQ,EACR,iBAAiB,EAClB,GAAG;IAEJ;;;;;;;GAOC,GACD,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC/B,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,eAAe,oBAAoB,OAAO;IAC9C,IAAI,qBACJ;;;;GAIC,GACD,UAAU,OAAO,MAAM,IAAI,CAAC,eAAe,UAAU,iBAAiB,QAAQ,GAAG;QAC/E,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mBAAmB;YACzD,UAAU;YACV,YAAY;YACZ,OAAO;YACP,mBAAmB;YACnB,qBAAqB;QACvB;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;QAClD,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,YAAY;IACd;AACF;AACA,MAAM,sBAAsB,qMAAA,CAAA,gBAAa;IACvC,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;IACvC;IACA,SAAS;QACP,IAAI;QACJ,IAAI,EACF,IAAI,EACJ,GAAG,EACH,MAAM,EACN,SAAS,EACT,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,EAAE,EACF,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,MAAM;YACR,OAAO;QACT;QACA,IAAI,aAAa,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB;QACvC,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,IAAI,CAAC,EAAE,GAAG;QAC3C,IAAI,EACF,IAAI,CAAC,EACL,cAAc,CAAC,EAChB,GAAG,CAAC,eAAe,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;YAChG,GAAG;YACH,aAAa;QACf;QACA,IAAI,UAAU,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD,EAAE;QACxB,IAAI,UAAU,IAAI,IAAI;QACtB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;YACpG,WAAW;QACb,GAAG,YAAY,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qKAAA,CAAA,wBAAqB,EAAE;YACpH,YAAY;YACZ,SAAS;YACT,SAAS;QACX,IAAI,CAAC,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;YAC3D,IAAI,iBAAiB,MAAM,CAAC;QAC9B,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC1C,GAAG,OAAO,UAAU;YACpB,GAAG,MAAM,UAAU;YACnB,OAAO,QAAQ;YACf,QAAQ,SAAS;QACnB,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,YAAY;YACjD,UAAU;YACV,YAAY;YACZ,OAAO,IAAI,CAAC,KAAK;QACnB,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,eAAY,EAAE;YAClD,QAAQ;YACR,WAAW,mBAAmB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAChE,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO;YAC/B,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS;QACjC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,aAAa,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,eAAY,EAAE;YAClG,QAAQ;YACR,WAAW,mBAAmB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAChE,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO;YAC/B,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS;QACjC;IACF;AACF;AACA,IAAI,mBAAmB;IACrB,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;IACjB,cAAc;IACd,KAAK;IACL,MAAM;IACN,aAAa;IACb,MAAM;IACN,mBAAmB,CAAC,iJAAA,CAAA,SAAM,CAAC,KAAK;IAChC,YAAY;IACZ,QAAQ;IACR,SAAS;IACT,SAAS;AACX;AACA,SAAS,SAAS,KAAK;IACrB,IAAI;IACJ,IAAI,uBAAuB,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,mBACpD,EACE,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,YAAY,EACZ,GAAG,EACH,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,iBAAiB,EACjB,UAAU,EACV,MAAM,EACN,OAAO,EACP,OAAO,EACR,GAAG,sBACJ,iBAAiB,yBAAyB,sBAAsB;IAClE,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC1B,IAAI,YAAY,CAAA,GAAA,kKAAA,CAAA,eAAY,AAAD;IAC3B,IAAI,EACF,QAAQ,EACT,GAAG,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,SAAS;IAC1B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAChC,WAAW,MAAM,SAAS;YAC1B,SAAS,MAAM,OAAO;YACtB;YACA,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;QACxB,CAAC,GAAG;QAAC,MAAM,SAAS;QAAE,MAAM,OAAO;QAAE;QAAc,MAAM,IAAI;QAAE,MAAM,OAAO;KAAC;IAC7E,IAAI,EACF,MAAM,EACN,OAAO,EACP,QAAQ,EACT,GAAG,CAAC,kBAAkB,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,SAAS,YAAY,cAAc,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,CAAC;IAC3K,IAAI,EACF,MAAM,EACN,KAAK,EACL,GAAG,IAAI,EACP,GAAG,GAAG,EACP,GAAG,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD;IACd,IAAI,WAAW,gBAAgB,WAAW,YAAY;QACpD,6CAA6C;QAC7C,OAAO;IACT;IACA,IAAI,cAAc,eAAe,cAAc,iBAAiB;QAC9D,6HAA6H;QAC7H,OAAO;IACT;IAEA;;;;GAIC,GACD,mCAAmC;IACnC,iBAAiB;IACjB,IAAI;IAEJ,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe,SAAS,CAAC,GAAG,gBAAgB;QAClF,WAAW;QACX,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,UAAU;QACV,cAAc;QACd,KAAK;QACL,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,mBAAmB;QACnB,SAAS;QACT,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,KAAK;QACL,SAAS;QACT,SAAS;IACX;AACF;AACO,IAAI,eAAe,CAAC,QAAQ,gBAAgB,eAAe,OAAO;IACvE,uEAAuE;IACvE,2CAA2C;IAC3C,IAAI,YAAY,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB;IACrF,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QACvB,OAAO;IACT;IACA,IAAI,cAAc,WAAW,eAAe,QAAQ;IACpD,8EAA8E;IAC9E,IAAI,SAAS,YAAY,KAAK,CAAC,MAAM;IACrC,IAAI,YAAY,IAAI,KAAK,UAAU;QACjC,IAAI,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;QAC7C,IAAI,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;QAC7C,IAAI,cAAc,WAAW;YAC3B,OAAO;QACT;QACA,IAAI,cAAc,WAAW;YAC3B,OAAO;QACT;QACA,OAAO,YAAY,IAAI,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG;IAC9E;IACA,IAAI,cAAc,WAAW;QAC3B,OAAO,MAAM,CAAC,EAAE;IAClB;IACA,IAAI,cAAc,WAAW;QAC3B,OAAO,MAAM,CAAC,EAAE;IAClB;IACA,OAAO,MAAM,CAAC,EAAE;AAClB;AACO,SAAS,YAAY,KAAK;IAC/B,IAAI,EACF,cAAc,EACZ,YAAY,EACZ,WAAW,aAAa,EACxB,OAAO,EACR,EACD,WAAW,EACX,MAAM,EACN,cAAc,EACd,KAAK,EACL,KAAK,EACL,aAAa,EACb,cAAc,EACd,UAAU,EACV,UAAU,EACV,QAAQ,EACT,GAAG;IACJ,IAAI,WAAW,eAAe,YAAY,MAAM;IAChD,IAAI,YAAY,aAAa,QAAQ,gBAAgB,eAAe,OAAO;IAC3E,IAAI,qBAAqB,WAAW;IACpC,IAAI,UAAU;IACd,IAAI,SAAS,cAAc,GAAG,CAAC,CAAC,OAAO;QACrC,IAAI;QACJ,IAAI,UAAU;YACZ,QAAQ,WAAW,CAAC,iBAAiB,MAAM;QAC7C,OAAO;YACL,QAAQ,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YACjC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,QAAQ;oBAAC;oBAAW;iBAAM;YAC5B,OAAO;gBACL,UAAU;YACZ;QACF;QACA,IAAI,eAAe,KAAK,CAAC,EAAE,IAAI,QAAQ,YAAY,CAAC,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;QACzG,IAAI,oBAAoB;YACtB,OAAO;gBACL,gGAAgG;gBAChG,GAAG,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;oBACzB,MAAM;oBACN,OAAO;oBACP;oBACA;oBACA;gBACF;gBACA,GAAG,eAAe,OAAO,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;gBAC7C;gBACA,SAAS;YACX;QACF;QACA,OAAO;YACL,GAAG,eAAe,OAAO,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE;YAC7C,gGAAgG;YAChG,GAAG,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;gBACzB,MAAM;gBACN,OAAO;gBACP;gBACA;gBACA;YACF;YACA;YACA,SAAS;QACX;IACF;IACA,IAAI;IACJ,IAAI,YAAY,SAAS;QACvB,WAAW,OAAO,GAAG,CAAC,CAAA;YACpB,IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG;YACtD,IAAI,oBAAoB;gBACtB,OAAO;oBACL,GAAG,MAAM,CAAC;oBACV,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI,OAAO,MAAM,KAAK,CAAC,KAAK;gBACrD;YACF;YACA,OAAO;gBACL,GAAG,KAAK,OAAO,MAAM,KAAK,CAAC,KAAK;gBAChC,GAAG,MAAM,CAAC;YACZ;QACF;IACF,OAAO;QACL,WAAW,qBAAqB,MAAM,KAAK,CAAC,aAAa,MAAM,KAAK,CAAC;IACvE;IACA,OAAO;QACL;QACA;QACA;IACF;AACF;AACO,MAAM,aAAa,qMAAA,CAAA,gBAAa;IACrC,SAAS;QACP,mGAAmG;QACnG,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,gCAA6B,EAAE;YACrE,MAAM;YACN,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS;QACX,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,mBAAgB,EAAE;YACpD,eAAe,iCAAiC,IAAI,CAAC,KAAK;QAC5D,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mKAAA,CAAA,0BAAuB,EAAE;YAC5D,IAAI;YACJ,MAAM,IAAI,CAAC,KAAK;QAClB,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,UAAU,IAAI,CAAC,KAAK;IAC3D;AACF;AACA,gBAAgB,MAAM,eAAe;AACrC,gBAAgB,MAAM,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3660, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/ZAxis.js"], "sourcesContent": ["function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { addZAxis, removeZAxis } from '../state/cartesianAxisSlice';\nimport { useAppDispatch } from '../state/hooks';\nimport { implicitZAxis } from '../state/selectors/axisSelectors';\nfunction SetZAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addZAxis(settings));\n    return () => {\n      dispatch(removeZAxis(settings));\n    };\n  }, [settings, dispatch]);\n  return null;\n}\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ZAxis extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(SetZAxisSettings, {\n      domain: this.props.domain,\n      id: this.props.zAxisId,\n      dataKey: this.props.dataKey,\n      name: this.props.name,\n      unit: this.props.unit,\n      range: this.props.range,\n      scale: this.props.scale,\n      type: this.props.type,\n      allowDuplicatedCategory: implicitZAxis.allowDuplicatedCategory,\n      allowDataOverflow: implicitZAxis.allowDataOverflow,\n      reversed: implicitZAxis.reversed,\n      includeHidden: implicitZAxis.includeHidden\n    });\n  }\n}\n_defineProperty(ZAxis, \"displayName\", 'ZAxis');\n_defineProperty(ZAxis, \"defaultProps\", {\n  zAxisId: 0,\n  range: implicitZAxis.range,\n  scale: implicitZAxis.scale,\n  type: implicitZAxis.type\n});"], "names": [], "mappings": ";;;AAGA;AAEA;AACA;AACA;AAPA,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;AAMvT,SAAS,iBAAiB,QAAQ;IAChC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE;QAClB,OAAO;YACL,SAAS,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACvB;IACF,GAAG;QAAC;QAAU;KAAS;IACvB,OAAO;AACT;AAEO,MAAM,cAAc,qMAAA,CAAA,YAAS;IAClC,SAAS;QACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,kBAAkB;YACxD,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;YACtB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,yBAAyB,sKAAA,CAAA,gBAAa,CAAC,uBAAuB;YAC9D,mBAAmB,sKAAA,CAAA,gBAAa,CAAC,iBAAiB;YAClD,UAAU,sKAAA,CAAA,gBAAa,CAAC,QAAQ;YAChC,eAAe,sKAAA,CAAA,gBAAa,CAAC,aAAa;QAC5C;IACF;AACF;AACA,gBAAgB,OAAO,eAAe;AACtC,gBAAgB,OAAO,gBAAgB;IACrC,SAAS;IACT,OAAO,sKAAA,CAAA,gBAAa,CAAC,KAAK;IAC1B,OAAO,sKAAA,CAAA,gBAAa,CAAC,KAAK;IAC1B,MAAM,sKAAA,CAAA,gBAAa,CAAC,IAAI;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3736, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/cartesian/Scatter.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"],\n  _excluded2 = [\"animationBegin\", \"animationDuration\", \"animationEasing\", \"hide\", \"isAnimationActive\", \"legendType\", \"lineJointType\", \"lineType\", \"shape\", \"xAxisId\", \"yAxisId\", \"zAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { Component, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { Cell } from '../component/Cell';\nimport { getLinearRegression, interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { ScatterSymbol } from '../util/ScatterUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectScatterPoints } from '../state/selectors/scatterSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromScatterProps = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction ScatterLine(_ref) {\n  var {\n    points,\n    props\n  } = _ref;\n  var {\n    line,\n    lineType,\n    lineJointType\n  } = props;\n  if (!line) {\n    return null;\n  }\n  var scatterProps = filterProps(props, false);\n  var customLineProps = filterProps(line, false);\n  var linePoints, lineItem;\n  if (lineType === 'joint') {\n    linePoints = points.map(entry => ({\n      x: entry.cx,\n      y: entry.cy\n    }));\n  } else if (lineType === 'fitting') {\n    var {\n      xmin,\n      xmax,\n      a,\n      b\n    } = getLinearRegression(points);\n    var linearExp = x => a * x + b;\n    linePoints = [{\n      x: xmin,\n      y: linearExp(xmin)\n    }, {\n      x: xmax,\n      y: linearExp(xmax)\n    }];\n  }\n  var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n    fill: 'none',\n    stroke: scatterProps && scatterProps.fill\n  }, customLineProps), {}, {\n    points: linePoints\n  });\n  if (/*#__PURE__*/React.isValidElement(line)) {\n    lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n  } else if (typeof line === 'function') {\n    lineItem = line(lineProps);\n  } else {\n    lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n      type: lineJointType\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-scatter-line\",\n    key: \"recharts-scatter-line\"\n  }, lineItem);\n}\nfunction ScatterSymbols(props) {\n  var {\n    points,\n    showLabels,\n    allOtherScatterProps\n  } = props;\n  var {\n    shape,\n    activeShape,\n    dataKey\n  } = allOtherScatterProps;\n  var baseProps = filterProps(allOtherScatterProps, false);\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherScatterProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherScatterProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherScatterProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherScatterProps.dataKey);\n  if (points == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ScatterLine, {\n    points: points,\n    props: allOtherScatterProps\n  }), points.map((entry, i) => {\n    var isActive = activeShape && activeIndex === String(i);\n    var option = isActive ? activeShape : shape;\n    var symbolProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"symbol-\".concat(i)\n    }, baseProps), entry), {}, {\n      [DATA_ITEM_INDEX_ATTRIBUTE_NAME]: i,\n      [DATA_ITEM_DATAKEY_ATTRIBUTE_NAME]: String(dataKey)\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-scatter-symbol\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onClick: onClickFromContext(entry, i)\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"symbol-\".concat(entry === null || entry === void 0 ? void 0 : entry.cx, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.cy, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.size, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(ScatterSymbol, _extends({\n      option: option,\n      isActive: isActive\n    }, symbolProps)));\n  }), showLabels && LabelList.renderCallByParent(allOtherScatterProps, points));\n}\nfunction SymbolsWithAnimation(_ref2) {\n  var {\n    previousPointsRef,\n    props\n  } = _ref2;\n  var {\n    points,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-scatter-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    // Scatter doesn't have onAnimationEnd prop, and if we want to add it we do it here\n    // if (typeof onAnimationEnd === 'function') {\n    //   onAnimationEnd();\n    // }\n    setIsAnimating(false);\n  }, []);\n  var handleAnimationStart = useCallback(() => {\n    // Scatter doesn't have onAnimationStart prop, and if we want to add it we do it here\n    // if (typeof onAnimationStart === 'function') {\n    //   onAnimationStart();\n    // }\n    setIsAnimating(true);\n  }, []);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? points : points.map((entry, index) => {\n      var prev = prevPoints && prevPoints[index];\n      if (prev) {\n        var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n        var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n        var interpolatorSize = interpolateNumber(prev.size, entry.size);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          cx: interpolatorCx(t),\n          cy: interpolatorCy(t),\n          size: interpolatorSize(t)\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.size);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        size: interpolator(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(ScatterSymbols, {\n      points: stepData,\n      allOtherScatterProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSymbols(props) {\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && (!prevPoints || prevPoints !== points)) {\n    return /*#__PURE__*/React.createElement(SymbolsWithAnimation, {\n      props: props,\n      previousPointsRef: previousPointsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(ScatterSymbols, {\n    points: points,\n    allOtherScatterProps: props,\n    showLabels: true\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    points,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPayload),\n    positions: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPosition),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // why doesn't Scatter support unit?\n    }\n  };\n}\nexport function computeScatterPoints(_ref4) {\n  var {\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  } = _ref4;\n  var xAxisDataKey = isNullish(xAxis.dataKey) ? scatterSettings.dataKey : xAxis.dataKey;\n  var yAxisDataKey = isNullish(yAxis.dataKey) ? scatterSettings.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  return displayedData.map((entry, index) => {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !isNullish(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(xAxis.dataKey) ? scatterSettings.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: scatterSettings.tooltipType\n    }, {\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(yAxis.dataKey) ? scatterSettings.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: scatterSettings.tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        // @ts-expect-error name prop should not have dataKey in it\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: scatterSettings.tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry,\n      index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry,\n      index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx,\n      cy,\n      x: cx - radius,\n      y: cy - radius,\n      width: 2 * radius,\n      height: 2 * radius,\n      size,\n      node: {\n        x,\n        y,\n        z\n      },\n      tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey, direction) => {\n  return {\n    x: dataPoint.cx,\n    y: dataPoint.cy,\n    value: direction === 'x' ? +dataPoint.node.x : +dataPoint.node.y,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nfunction ScatterWithId(props) {\n  var idRef = useRef(uniqueId('recharts-scatter-'));\n  var {\n    hide,\n    points,\n    className,\n    needClip,\n    xAxisId,\n    yAxisId,\n    id,\n    children\n  } = props;\n  if (hide) {\n    return null;\n  }\n  var layerClass = clsx('recharts-scatter', className);\n  var clipPathId = isNullish(id) ? idRef.current : id;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: layerClass,\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n  }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n    clipPathId: clipPathId,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  })), /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: points,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: 0\n  }, children), /*#__PURE__*/React.createElement(Layer, {\n    key: \"recharts-scatter-symbols\"\n  }, /*#__PURE__*/React.createElement(RenderSymbols, props)));\n}\nvar defaultScatterProps = {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n};\nfunction ScatterImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultScatterProps),\n    {\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      hide,\n      isAnimationActive,\n      legendType,\n      lineJointType,\n      lineType,\n      shape,\n      xAxisId,\n      yAxisId,\n      zAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var scatterSettings = useMemo(() => ({\n    name: props.name,\n    tooltipType: props.tooltipType,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.data, props.dataKey, props.name, props.tooltipType]);\n  var isPanorama = useIsPanorama();\n  var points = useAppSelector(state => {\n    return selectScatterPoints(state, xAxisId, yAxisId, zAxisId, scatterSettings, cells, isPanorama);\n  });\n  if (needClip == null) {\n    return null;\n  }\n  /*\n   * Do not check if points is null here!\n   * It is important that the animation component receives `null` as points\n   * so that it can reset its internal state and start animating to new positions.\n   */\n  // if (points == null)\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      points\n    })\n  }), /*#__PURE__*/React.createElement(ScatterWithId, _extends({}, everythingElse, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    zAxisId: zAxisId,\n    lineType: lineType,\n    lineJointType: lineJointType,\n    legendType: legendType,\n    shape: shape,\n    hide: hide,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    points: points,\n    needClip: needClip\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class Scatter extends Component {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"scatter\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: this.props.zAxisId,\n      dataKey: this.props.dataKey\n      // scatter doesn't stack\n      ,\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromScatterProps(this.props)\n    }), /*#__PURE__*/React.createElement(ScatterImpl, this.props));\n  }\n}\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", defaultScatterProps);"], "names": [], "mappings": ";;;;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA,IAAI,YAAY;IAAC;IAAgB;IAAW;CAAe,EACzD,aAAa;IAAC;IAAkB;IAAqB;IAAmB;IAAQ;IAAqB;IAAc;IAAiB;IAAY;IAAS;IAAW;IAAW;CAAU;AAC3L,SAAS,yBAAyB,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,GAAG,GAAG,IAAI,8BAA8B,GAAG;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAG;IAAE,OAAO;AAAG;AACrU,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACtM,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;AACnR,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BvT;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED,IAAI,uCAAuC,CAAA;IACzC,IAAI,EACF,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,IAAI,EACL,GAAG;IACJ,OAAO;QAAC;YACN,UAAU;YACV;YACA,MAAM;YACN,OAAO;YACP,OAAO,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAChC,SAAS;QACX;KAAE;AACJ;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,EACF,MAAM,EACN,KAAK,EACN,GAAG;IACJ,IAAI,EACF,IAAI,EACJ,QAAQ,EACR,aAAa,EACd,GAAG;IACJ,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI,eAAe,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACtC,IAAI,kBAAkB,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;IACxC,IAAI,YAAY;IAChB,IAAI,aAAa,SAAS;QACxB,aAAa,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBAChC,GAAG,MAAM,EAAE;gBACX,GAAG,MAAM,EAAE;YACb,CAAC;IACH,OAAO,IAAI,aAAa,WAAW;QACjC,IAAI,EACF,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,CAAC,EACF,GAAG,CAAA,GAAA,oJAAA,CAAA,sBAAmB,AAAD,EAAE;QACxB,IAAI,YAAY,CAAA,IAAK,IAAI,IAAI;QAC7B,aAAa;YAAC;gBACZ,GAAG;gBACH,GAAG,UAAU;YACf;YAAG;gBACD,GAAG;gBACH,GAAG,UAAU;YACf;SAAE;IACJ;IACA,IAAI,YAAY,cAAc,cAAc,cAAc,CAAC,GAAG,eAAe,CAAC,GAAG;QAC/E,MAAM;QACN,QAAQ,gBAAgB,aAAa,IAAI;IAC3C,GAAG,kBAAkB,CAAC,GAAG;QACvB,QAAQ;IACV;IACA,IAAI,WAAW,GAAE,qMAAA,CAAA,iBAAoB,CAAC,OAAO;QAC3C,WAAW,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,MAAM;IACnD,OAAO,IAAI,OAAO,SAAS,YAAY;QACrC,WAAW,KAAK;IAClB,OAAO;QACL,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,QAAK,EAAE,SAAS,CAAC,GAAG,WAAW;YACzE,MAAM;QACR;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;QAC7C,WAAW;QACX,KAAK;IACP,GAAG;AACL;AACA,SAAS,eAAe,KAAK;IAC3B,IAAI,EACF,MAAM,EACN,UAAU,EACV,oBAAoB,EACrB,GAAG;IACJ,IAAI,EACF,KAAK,EACL,WAAW,EACX,OAAO,EACR,GAAG;IACJ,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,cAAW,AAAD,EAAE,sBAAsB;IAClD,IAAI,cAAc,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,2BAAwB;IACzD,IAAI,EACA,cAAc,qBAAqB,EACnC,SAAS,oBAAoB,EAC7B,cAAc,qBAAqB,EACpC,GAAG,sBACJ,sBAAsB,yBAAyB,sBAAsB;IACvE,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,uBAAuB,qBAAqB,OAAO;IAC3G,IAAI,0BAA0B,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE;IACxD,IAAI,qBAAqB,CAAA,GAAA,4JAAA,CAAA,4BAAyB,AAAD,EAAE,sBAAsB,qBAAqB,OAAO;IACrG,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa;QAC1G,QAAQ;QACR,OAAO;IACT,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO;QACrB,IAAI,WAAW,eAAe,gBAAgB,OAAO;QACrD,IAAI,SAAS,WAAW,cAAc;QACtC,IAAI,cAAc,cAAc,cAAc,cAAc;YAC1D,KAAK,UAAU,MAAM,CAAC;QACxB,GAAG,YAAY,QAAQ,CAAC,GAAG;YACzB,CAAC,oJAAA,CAAA,iCAA8B,CAAC,EAAE;YAClC,CAAC,oJAAA,CAAA,mCAAgC,CAAC,EAAE,OAAO;QAC7C;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,SAAS;YACtD,WAAW;QACb,GAAG,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,OAAO,IAAI;YACpD,qDAAqD;YACrD,cAAc,wBAAwB,OAAO;YAG7C,cAAc,wBAAwB,OAAO;YAG7C,SAAS,mBAAmB,OAAO;YAGnC,KAAK,UAAU,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE,KAAK,MAAM,CAAC;QACtO,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,gBAAa,EAAE,SAAS;YAC3D,QAAQ;YACR,UAAU;QACZ,GAAG;IACL,IAAI,cAAc,yJAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,sBAAsB;AACvE;AACA,SAAS,qBAAqB,KAAK;IACjC,IAAI,EACF,iBAAiB,EACjB,KAAK,EACN,GAAG;IACJ,IAAI,EACF,MAAM,EACN,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EAChB,GAAG;IACJ,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,cAAc,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IACxC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,mFAAmF;QACnF,8CAA8C;QAC9C,sBAAsB;QACtB,IAAI;QACJ,eAAe;IACjB,GAAG,EAAE;IACL,IAAI,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,qFAAqF;QACrF,gDAAgD;QAChD,wBAAwB;QACxB,IAAI;QACJ,eAAe;IACjB,GAAG,EAAE;IACL,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAO,EAAE;QAC/C,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,MAAM;YACJ,GAAG;QACL;QACA,IAAI;YACF,GAAG;QACL;QACA,gBAAgB;QAChB,kBAAkB;QAClB,KAAK;IACP,GAAG,CAAA;QACD,IAAI,EACF,CAAC,EACF,GAAG;QACJ,IAAI,WAAW,MAAM,IAAI,SAAS,OAAO,GAAG,CAAC,CAAC,OAAO;YACnD,IAAI,OAAO,cAAc,UAAU,CAAC,MAAM;YAC1C,IAAI,MAAM;gBACR,IAAI,iBAAiB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE;gBACxD,IAAI,iBAAiB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE;gBACxD,IAAI,mBAAmB,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;gBAC9D,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;oBACjD,IAAI,eAAe;oBACnB,IAAI,eAAe;oBACnB,MAAM,iBAAiB;gBACzB;YACF;YACA,IAAI,eAAe,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,MAAM,IAAI;YAClD,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACjD,MAAM,aAAa;YACrB;QACF;QACA,IAAI,IAAI,GAAG;YACT,6CAA6C;YAC7C,kBAAkB,OAAO,GAAG;QAC9B;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,gBAAgB;YACpG,QAAQ;YACR,sBAAsB;YACtB,YAAY,CAAC;QACf;IACF;AACF;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,EACF,MAAM,EACN,iBAAiB,EAClB,GAAG;IACJ,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,aAAa,kBAAkB,OAAO;IAC1C,IAAI,qBAAqB,UAAU,OAAO,MAAM,IAAI,CAAC,CAAC,cAAc,eAAe,MAAM,GAAG;QAC1F,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,sBAAsB;YAC5D,OAAO;YACP,mBAAmB;QACrB;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,gBAAgB;QACtD,QAAQ;QACR,sBAAsB;QACtB,YAAY;IACd;AACF;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,EACF,OAAO,EACP,MAAM,EACN,MAAM,EACN,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,WAAW,EACZ,GAAG;IACJ,OAAO;QACL,mBAAmB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,cAAc;QACnG,WAAW,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,eAAe;QAC5F,UAAU;YACR;YACA;YACA;YACA,SAAS;YACT;YACA,MAAM,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YAC/B;YACA,MAAM;YACN,OAAO;YACP,MAAM,GAAG,oCAAoC;QAC/C;IACF;AACF;AACO,SAAS,qBAAqB,KAAK;IACxC,IAAI,EACF,aAAa,EACb,KAAK,EACL,KAAK,EACL,KAAK,EACL,eAAe,EACf,UAAU,EACV,UAAU,EACV,KAAK,EACN,GAAG;IACJ,IAAI,eAAe,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO,IAAI,gBAAgB,OAAO,GAAG,MAAM,OAAO;IACrF,IAAI,eAAe,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO,IAAI,gBAAgB,OAAO,GAAG,MAAM,OAAO;IACrF,IAAI,eAAe,SAAS,MAAM,OAAO;IACzC,IAAI,gBAAgB,QAAQ,MAAM,KAAK,GAAG,qJAAA,CAAA,QAAK,CAAC,YAAY,CAAC,KAAK;IAClE,IAAI,WAAW,iBAAiB,aAAa,CAAC,EAAE;IAChD,IAAI,YAAY,MAAM,KAAK,CAAC,SAAS,GAAG,MAAM,KAAK,CAAC,SAAS,KAAK;IAClE,IAAI,YAAY,MAAM,KAAK,CAAC,SAAS,GAAG,MAAM,KAAK,CAAC,SAAS,KAAK;IAClE,OAAO,cAAc,GAAG,CAAC,CAAC,OAAO;QAC/B,IAAI,IAAI,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QACjC,IAAI,IAAI,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QACjC,IAAI,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,iBAAiB;QAC9E,IAAI,iBAAiB;YAAC;gBACpB,2DAA2D;gBAC3D,MAAM,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO,IAAI,gBAAgB,IAAI,GAAG,MAAM,IAAI,IAAI,MAAM,OAAO;gBACnF,MAAM,MAAM,IAAI,IAAI;gBACpB,uEAAuE;gBACvE,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,MAAM,gBAAgB,WAAW;YACnC;YAAG;gBACD,2DAA2D;gBAC3D,MAAM,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO,IAAI,gBAAgB,IAAI,GAAG,MAAM,IAAI,IAAI,MAAM,OAAO;gBACnF,MAAM,MAAM,IAAI,IAAI;gBACpB,uEAAuE;gBACvE,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,MAAM,gBAAgB,WAAW;YACnC;SAAE;QACF,IAAI,MAAM,KAAK;YACb,eAAe,IAAI,CAAC;gBAClB,2DAA2D;gBAC3D,MAAM,MAAM,IAAI,IAAI,MAAM,OAAO;gBACjC,MAAM,MAAM,IAAI,IAAI;gBACpB,uEAAuE;gBACvE,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,MAAM,gBAAgB,WAAW;YACnC;QACF;QACA,IAAI,KAAK,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;YAC/B,MAAM;YACN,OAAO;YACP,UAAU;YACV;YACA;YACA,SAAS;QACX;QACA,IAAI,KAAK,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE;YAC/B,MAAM;YACN,OAAO;YACP,UAAU;YACV;YACA;YACA,SAAS;QACX;QACA,IAAI,OAAO,MAAM,MAAM,MAAM,KAAK,CAAC,KAAK;QACxC,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,EAAE;QAClD,OAAO,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;YACjD;YACA;YACA,GAAG,KAAK;YACR,GAAG,KAAK;YACR,OAAO,IAAI;YACX,QAAQ,IAAI;YACZ;YACA,MAAM;gBACJ;gBACA;gBACA;YACF;YACA;YACA,iBAAiB;gBACf,GAAG;gBACH,GAAG;YACL;YACA,SAAS;QACX,GAAG,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK;IAChD;AACF;AACA,IAAI,6BAA6B,CAAC,WAAW,SAAS;IACpD,OAAO;QACL,GAAG,UAAU,EAAE;QACf,GAAG,UAAU,EAAE;QACf,OAAO,cAAc,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC;QAChE,uEAAuE;QACvE,UAAU,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;IACzC;AACF;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,IAAI,EACF,IAAI,EACJ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,EACP,EAAE,EACF,QAAQ,EACT,GAAG;IACJ,IAAI,MAAM;QACR,OAAO;IACT;IACA,IAAI,aAAa,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,oBAAoB;IAC1C,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,MAAM,OAAO,GAAG;IACjD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;QAC7C,WAAW;QACX,UAAU,WAAW,iBAAiB,MAAM,CAAC,YAAY,OAAO;IAClE,GAAG,YAAY,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qKAAA,CAAA,wBAAqB,EAAE;QACpH,YAAY;QACZ,SAAS;QACT,SAAS;IACX,KAAK,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,qBAAkB,EAAE;QACxD,SAAS;QACT,SAAS;QACT,MAAM;QACN,oBAAoB;QACpB,gBAAgB;IAClB,GAAG,WAAW,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,QAAK,EAAE;QACpD,KAAK;IACP,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe;AACrD;AACA,IAAI,sBAAsB;IACxB,SAAS;IACT,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,eAAe;IACf,MAAM,EAAE;IACR,OAAO;IACP,MAAM;IACN,mBAAmB,CAAC,iJAAA,CAAA,SAAM,CAAC,KAAK;IAChC,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;AACnB;AACA,SAAS,YAAY,KAAK;IACxB,IAAI,uBAAuB,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,sBACpD,EACE,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,IAAI,EACJ,iBAAiB,EACjB,UAAU,EACV,aAAa,EACb,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACR,GAAG,sBACJ,iBAAiB,yBAAyB,sBAAsB;IAClE,IAAI,EACF,QAAQ,EACT,GAAG,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,SAAS;IAC1B,IAAI,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ,EAAE,oJAAA,CAAA,OAAI,GAAG;QAAC,MAAM,QAAQ;KAAC;IAC/E,IAAI,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACnC,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;YAC9B,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;QACxB,CAAC,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,OAAO;QAAE,MAAM,IAAI;QAAE,MAAM,WAAW;KAAC;IAC9D,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QAC1B,OAAO,CAAA,GAAA,yKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS,SAAS,iBAAiB,OAAO;IACvF;IACA,IAAI,YAAY,MAAM;QACpB,OAAO;IACT;IACA;;;;GAIC,GACD,sBAAsB;IACtB,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,mKAAA,CAAA,0BAAuB,EAAE;QACtH,IAAI;QACJ,MAAM,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;YAChD;QACF;IACF,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,eAAe,SAAS,CAAC,GAAG,gBAAgB;QAC/E,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,eAAe;QACf,YAAY;QACZ,OAAO;QACP,MAAM;QACN,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,QAAQ;QACR,UAAU;IACZ;AACF;AAGO,MAAM,gBAAgB,qMAAA,CAAA,YAAS;IACpC,SAAS;QACP,mGAAmG;QACnG,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,gCAA6B,EAAE;YACrE,MAAM;YACN,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAG3B,SAAS;YACT,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS;QACX,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,mBAAgB,EAAE;YACpD,eAAe,qCAAqC,IAAI,CAAC,KAAK;QAChE,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,aAAa,IAAI,CAAC,KAAK;IAC9D;AACF;AACA,gBAAgB,SAAS,eAAe;AACxC,gBAAgB,SAAS,gBAAgB", "ignoreList": [0], "debugId": null}}]}