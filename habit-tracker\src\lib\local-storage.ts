// Local storage implementation for habit tracking
import { v4 as uuidv4 } from 'uuid';

export interface User {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  created_at: string;
}

export interface Profile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  timezone: string;
  created_at: string;
  updated_at: string;
  preferences: {
    theme: string;
    language: string;
    notifications: {
      push: boolean;
      email: boolean;
      sound: boolean;
      reminders: boolean;
      achievements: boolean;
      weeklyReport: boolean;
    };
    privacy: {
      analytics: boolean;
      crashReports: boolean;
      dataSharing: boolean;
    };
  };
}

export interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  description?: string;
  created_at: string;
}

export interface Habit {
  id: string;
  user_id: string;
  category_id?: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  frequency: 'daily' | 'weekly' | 'weekdays' | 'weekends' | 'custom';
  target_value: number;
  unit: string;
  reminder_time?: string;
  reminder_enabled: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  custom_frequency?: any;
  tags: string[];
  difficulty_level: number;
  estimated_duration?: number;
}

export interface HabitCompletion {
  id: string;
  habit_id: string;
  user_id: string;
  completed_at: string;
  value: number;
  notes?: string;
  mood_before?: number;
  mood_after?: number;
  location?: string;
  weather?: string;
  energy_level?: number;
  created_at: string;
}

export interface HabitStreak {
  id: string;
  habit_id: string;
  user_id: string;
  current_streak: number;
  longest_streak: number;
  last_completed_date?: string;
  streak_start_date?: string;
  updated_at: string;
}

export interface MoodEntry {
  id: string;
  user_id: string;
  mood: 'terrible' | 'poor' | 'okay' | 'good' | 'excellent';
  mood_value: number;
  notes?: string;
  energy_level?: number;
  stress_level?: number;
  sleep_quality?: number;
  weather?: string;
  location?: string;
  tags: string[];
  created_at: string;
  recorded_for_date: string;
}

// Storage keys
const STORAGE_KEYS = {
  USER: 'habitflow_user',
  PROFILE: 'habitflow_profile',
  HABITS: 'habitflow_habits',
  COMPLETIONS: 'habitflow_completions',
  STREAKS: 'habitflow_streaks',
  MOOD_ENTRIES: 'habitflow_mood_entries',
  CATEGORIES: 'habitflow_categories',
} as const;

// Default categories
const DEFAULT_CATEGORIES: Category[] = [
  { id: '1', name: 'Health', icon: 'heart', color: '#ef4444', description: 'Physical and mental health habits', created_at: new Date().toISOString() },
  { id: '2', name: 'Fitness', icon: 'dumbbell', color: '#f97316', description: 'Exercise and physical activity', created_at: new Date().toISOString() },
  { id: '3', name: 'Learning', icon: 'book', color: '#3b82f6', description: 'Education and skill development', created_at: new Date().toISOString() },
  { id: '4', name: 'Wellness', icon: 'star', color: '#8b5cf6', description: 'Mental wellness and self-care', created_at: new Date().toISOString() },
  { id: '5', name: 'Productivity', icon: 'zap', color: '#eab308', description: 'Work and productivity habits', created_at: new Date().toISOString() },
  { id: '6', name: 'Lifestyle', icon: 'coffee', color: '#10b981', description: 'Daily life and routine habits', created_at: new Date().toISOString() },
  { id: '7', name: 'Mindfulness', icon: 'moon', color: '#6366f1', description: 'Meditation and mindfulness', created_at: new Date().toISOString() },
  { id: '8', name: 'Social', icon: 'users', color: '#ec4899', description: 'Relationships and social activities', created_at: new Date().toISOString() },
];

// Utility functions
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
}

function setToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error);
  }
}

// Initialize default data
function initializeDefaultData(): void {
  // Initialize categories if they don't exist
  const categories = getFromStorage(STORAGE_KEYS.CATEGORIES, []);
  if (categories.length === 0) {
    setToStorage(STORAGE_KEYS.CATEGORIES, DEFAULT_CATEGORIES);
  }

  // Initialize default user if none exists
  const user = getFromStorage(STORAGE_KEYS.USER, null);
  if (!user) {
    const defaultUser: User = {
      id: uuidv4(),
      email: '<EMAIL>',
      full_name: 'HabitFlow User',
      created_at: new Date().toISOString(),
    };
    setToStorage(STORAGE_KEYS.USER, defaultUser);

    const defaultProfile: Profile = {
      id: defaultUser.id,
      full_name: defaultUser.full_name,
      timezone: 'UTC',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: {
          push: true,
          email: false,
          sound: true,
          reminders: true,
          achievements: true,
          weeklyReport: true,
        },
        privacy: {
          analytics: true,
          crashReports: true,
          dataSharing: false,
        },
      },
    };
    setToStorage(STORAGE_KEYS.PROFILE, defaultProfile);
  }
}

// Calculate streak for a habit
function calculateStreak(habitId: string, userId: string): HabitStreak {
  const completions = getFromStorage<HabitCompletion[]>(STORAGE_KEYS.COMPLETIONS, [])
    .filter(c => c.habit_id === habitId && c.user_id === userId)
    .sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime());

  if (completions.length === 0) {
    return {
      id: uuidv4(),
      habit_id: habitId,
      user_id: userId,
      current_streak: 0,
      longest_streak: 0,
      updated_at: new Date().toISOString(),
    };
  }

  // Calculate current streak
  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Group completions by date
  const completionsByDate = new Map<string, number>();
  completions.forEach(completion => {
    const date = completion.completed_at.split('T')[0];
    completionsByDate.set(date, (completionsByDate.get(date) || 0) + 1);
  });

  // Calculate streaks
  const sortedDates = Array.from(completionsByDate.keys()).sort().reverse();
  
  for (let i = 0; i < sortedDates.length; i++) {
    const currentDate = new Date(sortedDates[i]);
    const expectedDate = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
    
    if (currentDate.getTime() === expectedDate.getTime()) {
      currentStreak++;
      tempStreak++;
    } else {
      break;
    }
  }

  // Calculate longest streak
  tempStreak = 0;
  for (let i = 0; i < sortedDates.length; i++) {
    tempStreak++;
    if (i === sortedDates.length - 1 || 
        new Date(sortedDates[i]).getTime() - new Date(sortedDates[i + 1]).getTime() > 24 * 60 * 60 * 1000) {
      longestStreak = Math.max(longestStreak, tempStreak);
      tempStreak = 0;
    }
  }

  return {
    id: uuidv4(),
    habit_id: habitId,
    user_id: userId,
    current_streak: currentStreak,
    longest_streak: Math.max(longestStreak, currentStreak),
    last_completed_date: completions[0]?.completed_at.split('T')[0],
    streak_start_date: currentStreak > 0 ? sortedDates[currentStreak - 1] : undefined,
    updated_at: new Date().toISOString(),
  };
}

// Local storage API
export const localStorageAPI = {
  // Initialize
  initialize: initializeDefaultData,

  // User management
  getUser: (): User | null => getFromStorage(STORAGE_KEYS.USER, null),
  setUser: (user: User) => setToStorage(STORAGE_KEYS.USER, user),
  
  getProfile: (): Profile | null => getFromStorage(STORAGE_KEYS.PROFILE, null),
  updateProfile: (updates: Partial<Profile>) => {
    const profile = getFromStorage<Profile | null>(STORAGE_KEYS.PROFILE, null);
    if (profile) {
      const updatedProfile = { ...profile, ...updates, updated_at: new Date().toISOString() };
      setToStorage(STORAGE_KEYS.PROFILE, updatedProfile);
      return updatedProfile;
    }
    return null;
  },

  // Categories
  getCategories: (): Category[] => getFromStorage(STORAGE_KEYS.CATEGORIES, DEFAULT_CATEGORIES),

  // Habits
  getHabits: (userId: string): Habit[] => {
    return getFromStorage<Habit[]>(STORAGE_KEYS.HABITS, [])
      .filter(habit => habit.user_id === userId && habit.is_active);
  },

  createHabit: (habit: Omit<Habit, 'id' | 'created_at' | 'updated_at'>): Habit => {
    const habits = getFromStorage<Habit[]>(STORAGE_KEYS.HABITS, []);
    const newHabit: Habit = {
      ...habit,
      id: uuidv4(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    habits.push(newHabit);
    setToStorage(STORAGE_KEYS.HABITS, habits);
    return newHabit;
  },

  updateHabit: (habitId: string, updates: Partial<Habit>): Habit | null => {
    const habits = getFromStorage<Habit[]>(STORAGE_KEYS.HABITS, []);
    const index = habits.findIndex(h => h.id === habitId);
    if (index !== -1) {
      habits[index] = { ...habits[index], ...updates, updated_at: new Date().toISOString() };
      setToStorage(STORAGE_KEYS.HABITS, habits);
      return habits[index];
    }
    return null;
  },

  deleteHabit: (habitId: string): boolean => {
    const habits = getFromStorage<Habit[]>(STORAGE_KEYS.HABITS, []);
    const index = habits.findIndex(h => h.id === habitId);
    if (index !== -1) {
      habits[index].is_active = false;
      habits[index].updated_at = new Date().toISOString();
      setToStorage(STORAGE_KEYS.HABITS, habits);
      return true;
    }
    return false;
  },

  // Habit completions
  getHabitCompletions: (userId: string, startDate?: string, endDate?: string): HabitCompletion[] => {
    let completions = getFromStorage<HabitCompletion[]>(STORAGE_KEYS.COMPLETIONS, [])
      .filter(completion => completion.user_id === userId);

    if (startDate) {
      completions = completions.filter(c => c.completed_at >= startDate);
    }
    if (endDate) {
      completions = completions.filter(c => c.completed_at <= endDate);
    }

    return completions.sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime());
  },

  completeHabit: (completion: Omit<HabitCompletion, 'id' | 'created_at'>): HabitCompletion => {
    const completions = getFromStorage<HabitCompletion[]>(STORAGE_KEYS.COMPLETIONS, []);
    const newCompletion: HabitCompletion = {
      ...completion,
      id: uuidv4(),
      created_at: new Date().toISOString(),
    };
    completions.push(newCompletion);
    setToStorage(STORAGE_KEYS.COMPLETIONS, completions);

    // Update streak
    const streak = calculateStreak(completion.habit_id, completion.user_id);
    const streaks = getFromStorage<HabitStreak[]>(STORAGE_KEYS.STREAKS, []);
    const existingIndex = streaks.findIndex(s => s.habit_id === completion.habit_id && s.user_id === completion.user_id);
    
    if (existingIndex !== -1) {
      streaks[existingIndex] = streak;
    } else {
      streaks.push(streak);
    }
    setToStorage(STORAGE_KEYS.STREAKS, streaks);

    return newCompletion;
  },

  uncompleteHabit: (habitId: string, userId: string, date: string): boolean => {
    const completions = getFromStorage<HabitCompletion[]>(STORAGE_KEYS.COMPLETIONS, []);
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const filteredCompletions = completions.filter(c => 
      !(c.habit_id === habitId && 
        c.user_id === userId && 
        new Date(c.completed_at) >= startOfDay && 
        new Date(c.completed_at) <= endOfDay)
    );

    setToStorage(STORAGE_KEYS.COMPLETIONS, filteredCompletions);

    // Recalculate streak
    const streak = calculateStreak(habitId, userId);
    const streaks = getFromStorage<HabitStreak[]>(STORAGE_KEYS.STREAKS, []);
    const existingIndex = streaks.findIndex(s => s.habit_id === habitId && s.user_id === userId);
    
    if (existingIndex !== -1) {
      streaks[existingIndex] = streak;
    } else {
      streaks.push(streak);
    }
    setToStorage(STORAGE_KEYS.STREAKS, streaks);

    return true;
  },

  // Streaks
  getHabitStreaks: (userId: string): HabitStreak[] => {
    return getFromStorage<HabitStreak[]>(STORAGE_KEYS.STREAKS, [])
      .filter(streak => streak.user_id === userId);
  },

  // Mood entries
  getMoodEntries: (userId: string, startDate?: string, endDate?: string): MoodEntry[] => {
    let entries = getFromStorage<MoodEntry[]>(STORAGE_KEYS.MOOD_ENTRIES, [])
      .filter(entry => entry.user_id === userId);

    if (startDate) {
      entries = entries.filter(e => e.recorded_for_date >= startDate);
    }
    if (endDate) {
      entries = entries.filter(e => e.recorded_for_date <= endDate);
    }

    return entries.sort((a, b) => new Date(b.recorded_for_date).getTime() - new Date(a.recorded_for_date).getTime());
  },

  createMoodEntry: (entry: Omit<MoodEntry, 'id' | 'created_at'>): MoodEntry => {
    const entries = getFromStorage<MoodEntry[]>(STORAGE_KEYS.MOOD_ENTRIES, []);
    const newEntry: MoodEntry = {
      ...entry,
      id: uuidv4(),
      created_at: new Date().toISOString(),
    };
    entries.push(newEntry);
    setToStorage(STORAGE_KEYS.MOOD_ENTRIES, entries);
    return newEntry;
  },

  updateMoodEntry: (entryId: string, updates: Partial<MoodEntry>): MoodEntry | null => {
    const entries = getFromStorage<MoodEntry[]>(STORAGE_KEYS.MOOD_ENTRIES, []);
    const index = entries.findIndex(e => e.id === entryId);
    if (index !== -1) {
      entries[index] = { ...entries[index], ...updates };
      setToStorage(STORAGE_KEYS.MOOD_ENTRIES, entries);
      return entries[index];
    }
    return null;
  },

  // Clear all data
  clearAllData: () => {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    initializeDefaultData();
  },
};
