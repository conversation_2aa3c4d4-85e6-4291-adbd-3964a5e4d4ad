'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { db, subscriptions, isSupabaseConfigured } from '@/lib/supabase';
import { Database } from '@/lib/database.types';
import toast from 'react-hot-toast';

type Habit = Database['public']['Tables']['habits']['Row'] & {
  category?: Database['public']['Tables']['habit_categories']['Row'];
  streak?: Database['public']['Tables']['habit_streaks']['Row'];
};

type HabitCompletion = Database['public']['Tables']['habit_completions']['Row'] & {
  habit?: Habit;
};

type CreateHabitData = Database['public']['Tables']['habits']['Insert'];
type UpdateHabitData = Database['public']['Tables']['habits']['Update'];

// Demo data for when Supabase is not configured
const demoCategories = [
  { id: '1', name: 'Health', icon: 'heart', color: '#ef4444', description: 'Physical and mental health habits', created_at: new Date().toISOString() },
  { id: '2', name: 'Fitness', icon: 'dumbbell', color: '#f97316', description: 'Exercise and physical activity', created_at: new Date().toISOString() },
  { id: '3', name: 'Learning', icon: 'book', color: '#3b82f6', description: 'Education and skill development', created_at: new Date().toISOString() },
  { id: '4', name: 'Wellness', icon: 'star', color: '#8b5cf6', description: 'Mental wellness and self-care', created_at: new Date().toISOString() },
];

const demoHabits = [
  {
    id: '1',
    user_id: 'demo-user-123',
    category_id: '1',
    name: 'Drink Water',
    description: 'Drink 8 glasses of water throughout the day',
    icon: 'droplets',
    color: '#3b82f6',
    frequency: 'daily' as const,
    target_value: 8,
    unit: 'glasses',
    reminder_time: null,
    reminder_enabled: false,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    custom_frequency: null,
    tags: [],
    difficulty_level: 2,
    estimated_duration: null,
    category: demoCategories[0],
    streak: { id: '1', habit_id: '1', user_id: 'demo-user-123', current_streak: 7, longest_streak: 12, last_completed_date: new Date().toISOString().split('T')[0], streak_start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], updated_at: new Date().toISOString() }
  },
  {
    id: '2',
    user_id: 'demo-user-123',
    category_id: '2',
    name: 'Exercise',
    description: '30 minutes of physical activity',
    icon: 'dumbbell',
    color: '#f97316',
    frequency: 'daily' as const,
    target_value: 1,
    unit: 'session',
    reminder_time: '07:00',
    reminder_enabled: true,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    custom_frequency: null,
    tags: ['morning', 'fitness'],
    difficulty_level: 3,
    estimated_duration: 30,
    category: demoCategories[1],
    streak: { id: '2', habit_id: '2', user_id: 'demo-user-123', current_streak: 3, longest_streak: 15, last_completed_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], streak_start_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], updated_at: new Date().toISOString() }
  },
  {
    id: '3',
    user_id: 'demo-user-123',
    category_id: '3',
    name: 'Read',
    description: 'Read for 20 minutes',
    icon: 'book',
    color: '#3b82f6',
    frequency: 'daily' as const,
    target_value: 20,
    unit: 'minutes',
    reminder_time: '21:00',
    reminder_enabled: true,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    custom_frequency: null,
    tags: ['evening', 'learning'],
    difficulty_level: 2,
    estimated_duration: 20,
    category: demoCategories[2],
    streak: { id: '3', habit_id: '3', user_id: 'demo-user-123', current_streak: 5, longest_streak: 21, last_completed_date: new Date().toISOString().split('T')[0], streak_start_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], updated_at: new Date().toISOString() }
  }
];

export function useHabits() {
  const { user } = useAuth();
  const [habits, setHabits] = useState<Habit[]>([]);
  const [completions, setCompletions] = useState<HabitCompletion[]>([]);
  const [categories, setCategories] = useState<Database['public']['Tables']['habit_categories']['Row'][]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch habits
  const fetchHabits = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);

      if (!isSupabaseConfigured) {
        // Use demo data
        setHabits(demoHabits);
        setLoading(false);
        return;
      }

      const { data, error } = await db.getHabits(user.id);

      if (error) throw error;

      setHabits(data || []);
    } catch (err: any) {
      setError(err.message);
      toast.error('Failed to load habits');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Fetch completions for today
  const fetchTodayCompletions = useCallback(async () => {
    if (!user) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const { data, error } = await db.getHabitCompletions(user.id, today, tomorrow);
      
      if (error) throw error;
      
      setCompletions(data || []);
    } catch (err: any) {
      setError(err.message);
    }
  }, [user]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      if (!isSupabaseConfigured) {
        setCategories(demoCategories);
        return;
      }

      const { data, error } = await db.getCategories();

      if (error) throw error;

      setCategories(data || []);
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  // Create habit
  const createHabit = async (habitData: CreateHabitData) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const { data, error } = await db.createHabit({
        ...habitData,
        user_id: user.id,
      });

      if (error) throw error;

      toast.success('Habit created successfully!');
      await fetchHabits();
      
      return { data, error: null };
    } catch (err: any) {
      toast.error('Failed to create habit');
      return { data: null, error: err.message };
    }
  };

  // Update habit
  const updateHabit = async (habitId: string, updates: UpdateHabitData) => {
    try {
      const { error } = await db.updateHabit(habitId, updates);

      if (error) throw error;

      toast.success('Habit updated successfully!');
      await fetchHabits();
      
      return { error: null };
    } catch (err: any) {
      toast.error('Failed to update habit');
      return { error: err.message };
    }
  };

  // Delete habit
  const deleteHabit = async (habitId: string) => {
    try {
      const { error } = await db.deleteHabit(habitId);

      if (error) throw error;

      toast.success('Habit deleted successfully!');
      await fetchHabits();
      
      return { error: null };
    } catch (err: any) {
      toast.error('Failed to delete habit');
      return { error: err.message };
    }
  };

  // Complete habit
  const completeHabit = async (habitId: string, value = 1, notes?: string) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const { data, error } = await db.completeHabit({
        habit_id: habitId,
        user_id: user.id,
        value,
        notes,
        completed_at: new Date().toISOString(),
      });

      if (error) throw error;

      toast.success('Habit completed! 🎉');
      await fetchTodayCompletions();
      await fetchHabits(); // Refresh to update streaks
      
      return { data, error: null };
    } catch (err: any) {
      toast.error('Failed to complete habit');
      return { data: null, error: err.message };
    }
  };

  // Uncomplete habit
  const uncompleteHabit = async (habitId: string) => {
    if (!user) return { error: 'No user logged in' };

    try {
      const today = new Date().toISOString().split('T')[0];
      const { error } = await db.uncompleteHabit(habitId, user.id, today);

      if (error) throw error;

      toast.success('Habit unmarked');
      await fetchTodayCompletions();
      await fetchHabits(); // Refresh to update streaks
      
      return { error: null };
    } catch (err: any) {
      toast.error('Failed to uncomplete habit');
      return { error: err.message };
    }
  };

  // Check if habit is completed today
  const isHabitCompletedToday = (habitId: string) => {
    return completions.some(completion => completion.habit_id === habitId);
  };

  // Get habit completion for today
  const getHabitCompletionToday = (habitId: string) => {
    return completions.find(completion => completion.habit_id === habitId);
  };

  // Get habits with completion status
  const habitsWithStatus = habits.map(habit => ({
    ...habit,
    completedToday: isHabitCompletedToday(habit.id),
    todayCompletion: getHabitCompletionToday(habit.id),
  }));

  // Statistics
  const stats = {
    totalHabits: habits.length,
    activeHabits: habits.filter(h => h.is_active).length,
    completedToday: completions.length,
    completionRate: habits.length > 0 ? Math.round((completions.length / habits.length) * 100) : 0,
    longestStreak: Math.max(...habits.map(h => h.streak?.longest_streak || 0), 0),
    currentStreaks: habits.reduce((sum, h) => sum + (h.streak?.current_streak || 0), 0),
  };

  // Initialize data and subscriptions
  useEffect(() => {
    if (user) {
      fetchHabits();
      fetchTodayCompletions();
      fetchCategories();

      // Set up real-time subscriptions
      const habitsSubscription = subscriptions.subscribeToHabits(user.id, (payload) => {
        console.log('Habits updated:', payload);
        fetchHabits();
      });

      const completionsSubscription = subscriptions.subscribeToCompletions(user.id, (payload) => {
        console.log('Completions updated:', payload);
        fetchTodayCompletions();
      });

      return () => {
        habitsSubscription.unsubscribe();
        completionsSubscription.unsubscribe();
      };
    }
  }, [user, fetchHabits, fetchTodayCompletions, fetchCategories]);

  return {
    habits: habitsWithStatus,
    completions,
    categories,
    loading,
    error,
    stats,
    actions: {
      createHabit,
      updateHabit,
      deleteHabit,
      completeHabit,
      uncompleteHabit,
      refreshHabits: fetchHabits,
      refreshCompletions: fetchTodayCompletions,
    },
    utils: {
      isHabitCompletedToday,
      getHabitCompletionToday,
    },
  };
}

// Hook for habit analytics
export function useHabitAnalytics() {
  const { user } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  const fetchAnalytics = useCallback(async (metricType: string, timePeriod: string) => {
    if (!user) return;

    try {
      const { data, error } = await db.getAnalyticsData(user.id, metricType, timePeriod);
      
      if (error) throw error;
      
      setAnalyticsData(prev => ({
        ...prev,
        [`${metricType}_${timePeriod}`]: data,
      }));
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
    }
  }, [user]);

  const getStreakTrends = async (days = 30) => {
    if (!user) return [];

    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
      
      const { data, error } = await db.getHabitCompletions(
        user.id,
        startDate.toISOString(),
        endDate.toISOString()
      );

      if (error) throw error;

      // Process data to create trend analysis
      const trendData = data?.reduce((acc: any, completion) => {
        const date = completion.completed_at.split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {}) || {};

      return Object.entries(trendData).map(([date, count]) => ({
        date,
        completions: count,
      }));
    } catch (err: any) {
      console.error('Failed to fetch streak trends:', err);
      return [];
    }
  };

  useEffect(() => {
    if (user) {
      setLoading(true);
      Promise.all([
        fetchAnalytics('completions', 'daily'),
        fetchAnalytics('streaks', 'weekly'),
        fetchAnalytics('mood_correlation', 'monthly'),
      ]).finally(() => setLoading(false));
    }
  }, [user, fetchAnalytics]);

  return {
    analyticsData,
    loading,
    getStreakTrends,
    fetchAnalytics,
  };
}
