{"version": 3, "sources": [], "sections": [{"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from './database.types';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// Check if we have real Supabase credentials\nexport const isSupabaseConfigured = Boolean(supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'https://demo.supabase.co' &&\n  supabaseAnonKey !== 'demo_key');\n\nexport const supabase = isSupabaseConfigured && supabaseUrl && supabaseAnonKey ?\n  createClient<Database>(supabaseUrl, supabaseAnonKey, {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true,\n    },\n  }) : null;\n\n// Auth helpers\nexport const auth = {\n  signUp: async (email: string, password: string, metadata?: any) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { data: null, error: { message: 'Demo mode - authentication not available' } };\n    }\n    return await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata,\n      },\n    });\n  },\n\n  signIn: async (email: string, password: string) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { data: null, error: { message: 'Demo mode - authentication not available' } };\n    }\n    return await supabase.auth.signInWithPassword({\n      email,\n      password,\n    });\n  },\n\n  signInWithGoogle: async () => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { data: null, error: { message: 'Demo mode - authentication not available' } };\n    }\n    return await supabase.auth.signInWithOAuth({\n      provider: 'google',\n      options: {\n        redirectTo: `${window.location.origin}/auth/callback`,\n      },\n    });\n  },\n\n  signOut: async () => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { error: null };\n    }\n    return await supabase.auth.signOut();\n  },\n\n  getCurrentUser: async () => {\n    if (!isSupabaseConfigured || !supabase) {\n      return null;\n    }\n    const { data: { user } } = await supabase.auth.getUser();\n    return user;\n  },\n\n  getCurrentSession: async () => {\n    if (!isSupabaseConfigured || !supabase) {\n      return null;\n    }\n    const { data: { session } } = await supabase.auth.getSession();\n    return session;\n  },\n};\n\n// Database helpers\nexport const db = {\n  // Profiles\n  getProfile: async (userId: string) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { data: null, error: { message: 'Demo mode - database not available' } };\n    }\n    return await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single();\n  },\n\n  updateProfile: async (userId: string, updates: any) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { error: { message: 'Demo mode - database not available' } };\n    }\n    return await supabase\n      .from('profiles')\n      .update(updates)\n      .eq('id', userId);\n  },\n\n  // Habits\n  getHabits: async (userId: string) => {\n    return await supabase\n      .from('habits')\n      .select(`\n        *,\n        category:habit_categories(*),\n        streak:habit_streaks(*)\n      `)\n      .eq('user_id', userId)\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n  },\n\n  createHabit: async (habit: any) => {\n    return await supabase\n      .from('habits')\n      .insert(habit)\n      .select()\n      .single();\n  },\n\n  updateHabit: async (habitId: string, updates: any) => {\n    return await supabase\n      .from('habits')\n      .update(updates)\n      .eq('id', habitId);\n  },\n\n  deleteHabit: async (habitId: string) => {\n    return await supabase\n      .from('habits')\n      .update({ is_active: false })\n      .eq('id', habitId);\n  },\n\n  // Habit Completions\n  getHabitCompletions: async (userId: string, startDate?: string, endDate?: string) => {\n    let query = supabase\n      .from('habit_completions')\n      .select(`\n        *,\n        habit:habits(*)\n      `)\n      .eq('user_id', userId)\n      .order('completed_at', { ascending: false });\n\n    if (startDate) {\n      query = query.gte('completed_at', startDate);\n    }\n    if (endDate) {\n      query = query.lte('completed_at', endDate);\n    }\n\n    return await query;\n  },\n\n  completeHabit: async (completion: any) => {\n    return await supabase\n      .from('habit_completions')\n      .insert(completion)\n      .select()\n      .single();\n  },\n\n  uncompleteHabit: async (habitId: string, userId: string, date: string) => {\n    return await supabase\n      .from('habit_completions')\n      .delete()\n      .eq('habit_id', habitId)\n      .eq('user_id', userId)\n      .gte('completed_at', date)\n      .lt('completed_at', new Date(new Date(date).getTime() + 24 * 60 * 60 * 1000).toISOString());\n  },\n\n  // Mood Entries\n  getMoodEntries: async (userId: string, startDate?: string, endDate?: string) => {\n    let query = supabase\n      .from('mood_entries')\n      .select('*')\n      .eq('user_id', userId)\n      .order('recorded_for_date', { ascending: false });\n\n    if (startDate) {\n      query = query.gte('recorded_for_date', startDate);\n    }\n    if (endDate) {\n      query = query.lte('recorded_for_date', endDate);\n    }\n\n    return await query;\n  },\n\n  createMoodEntry: async (moodEntry: any) => {\n    return await supabase\n      .from('mood_entries')\n      .insert(moodEntry)\n      .select()\n      .single();\n  },\n\n  updateMoodEntry: async (entryId: string, updates: any) => {\n    return await supabase\n      .from('mood_entries')\n      .update(updates)\n      .eq('id', entryId);\n  },\n\n  // Categories\n  getCategories: async () => {\n    return await supabase\n      .from('habit_categories')\n      .select('*')\n      .order('name');\n  },\n\n  // Achievements\n  getAchievements: async () => {\n    return await supabase\n      .from('achievements')\n      .select('*')\n      .order('points');\n  },\n\n  getUserAchievements: async (userId: string) => {\n    return await supabase\n      .from('user_achievements')\n      .select(`\n        *,\n        achievement:achievements(*)\n      `)\n      .eq('user_id', userId)\n      .order('unlocked_at', { ascending: false });\n  },\n\n  unlockAchievement: async (userId: string, achievementId: string) => {\n    return await supabase\n      .from('user_achievements')\n      .insert({\n        user_id: userId,\n        achievement_id: achievementId,\n      })\n      .select()\n      .single();\n  },\n\n  // Streaks\n  getHabitStreaks: async (userId: string) => {\n    return await supabase\n      .from('habit_streaks')\n      .select(`\n        *,\n        habit:habits(*)\n      `)\n      .eq('user_id', userId)\n      .order('current_streak', { ascending: false });\n  },\n\n  // Analytics\n  getAnalyticsData: async (userId: string, metricType: string, timePeriod: string) => {\n    return await supabase\n      .from('analytics_cache')\n      .select('*')\n      .eq('user_id', userId)\n      .eq('metric_type', metricType)\n      .eq('time_period', timePeriod)\n      .order('date_key', { ascending: false });\n  },\n\n  // Habit Stacks\n  getHabitStacks: async (userId: string) => {\n    return await supabase\n      .from('habit_stacks')\n      .select(`\n        *,\n        items:habit_stack_items(\n          *,\n          habit:habits(*)\n        )\n      `)\n      .eq('user_id', userId)\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n  },\n\n  createHabitStack: async (stack: any) => {\n    return await supabase\n      .from('habit_stacks')\n      .insert(stack)\n      .select()\n      .single();\n  },\n\n  // Notifications\n  getNotifications: async (userId: string, unreadOnly = false) => {\n    let query = supabase\n      .from('notifications')\n      .select('*')\n      .eq('user_id', userId)\n      .order('created_at', { ascending: false });\n\n    if (unreadOnly) {\n      query = query.is('read_at', null);\n    }\n\n    return await query;\n  },\n\n  markNotificationAsRead: async (notificationId: string) => {\n    return await supabase\n      .from('notifications')\n      .update({ read_at: new Date().toISOString() })\n      .eq('id', notificationId);\n  },\n};\n\n// Real-time subscriptions\nexport const subscriptions = {\n  subscribeToHabits: (userId: string, callback: (payload: any) => void) => {\n    if (!isSupabaseConfigured || !supabase) {\n      // Return a mock subscription for demo mode\n      return {\n        unsubscribe: () => {},\n      };\n    }\n\n    return supabase\n      .channel('habits')\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'habits',\n          filter: `user_id=eq.${userId}`,\n        },\n        callback\n      )\n      .subscribe();\n  },\n\n  subscribeToCompletions: (userId: string, callback: (payload: any) => void) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return {\n        unsubscribe: () => {},\n      };\n    }\n\n    return supabase\n      .channel('completions')\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'habit_completions',\n          filter: `user_id=eq.${userId}`,\n        },\n        callback\n      )\n      .subscribe();\n  },\n\n  subscribeToMoodEntries: (userId: string, callback: (payload: any) => void) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return {\n        unsubscribe: () => {},\n      };\n    }\n\n    return supabase\n      .channel('mood_entries')\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'mood_entries',\n          filter: `user_id=eq.${userId}`,\n        },\n        callback\n      )\n      .subscribe();\n  },\n\n  subscribeToNotifications: (userId: string, callback: (payload: any) => void) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return {\n        unsubscribe: () => {},\n      };\n    }\n\n    return supabase\n      .channel('notifications')\n      .on(\n        'postgres_changes',\n        {\n          event: 'INSERT',\n          schema: 'public',\n          table: 'notifications',\n          filter: `user_id=eq.${userId}`,\n        },\n        callback\n      )\n      .subscribe();\n  },\n};\n\nexport default supabase;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,uBAAuB,QAAQ,eAAe,mBACzD,gBAAgB,8BAChB,oBAAoB;AAEf,MAAM,WAAW,wBAAwB,eAAe,kBAC7D,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa,iBAAiB;IACnD,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;AACF,KAAK;AAGA,MAAM,OAAO;IAClB,QAAQ,OAAO,OAAe,UAAkB;QAC9C,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAA2C;YAAE;QACtF;QACA,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAChC;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;IACF;IAEA,QAAQ,OAAO,OAAe;QAC5B,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAA2C;YAAE;QACtF;QACA,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC5C;YACA;QACF;IACF;IAEA,kBAAkB;QAChB,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAA2C;YAAE;QACtF;QACA,OAAO,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YACzC,UAAU;YACV,SAAS;gBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;YACvD;QACF;IACF;IAEA,SAAS;QACP,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,OAAO;YAAK;QACvB;QACA,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;IACpC;IAEA,gBAAgB;QACd,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;QACT;QACA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,mBAAmB;QACjB,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;QACT;QACA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAC5D,OAAO;IACT;AACF;AAGO,MAAM,KAAK;IAChB,WAAW;IACX,YAAY,OAAO;QACjB,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QAChF;QACA,OAAO,MAAM,SACV,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IACX;IAEA,eAAe,OAAO,QAAgB;QACpC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QACpE;QACA,OAAO,MAAM,SACV,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;IACd;IAEA,SAAS;IACT,WAAW,OAAO;QAChB,OAAO,MAAM,SACV,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;IAC5C;IAEA,aAAa,OAAO;QAClB,OAAO,MAAM,SACV,IAAI,CAAC,UACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;IACX;IAEA,aAAa,OAAO,SAAiB;QACnC,OAAO,MAAM,SACV,IAAI,CAAC,UACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;IACd;IAEA,aAAa,OAAO;QAClB,OAAO,MAAM,SACV,IAAI,CAAC,UACL,MAAM,CAAC;YAAE,WAAW;QAAM,GAC1B,EAAE,CAAC,MAAM;IACd;IAEA,oBAAoB;IACpB,qBAAqB,OAAO,QAAgB,WAAoB;QAC9D,IAAI,QAAQ,SACT,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,gBAAgB;YAAE,WAAW;QAAM;QAE5C,IAAI,WAAW;YACb,QAAQ,MAAM,GAAG,CAAC,gBAAgB;QACpC;QACA,IAAI,SAAS;YACX,QAAQ,MAAM,GAAG,CAAC,gBAAgB;QACpC;QAEA,OAAO,MAAM;IACf;IAEA,eAAe,OAAO;QACpB,OAAO,MAAM,SACV,IAAI,CAAC,qBACL,MAAM,CAAC,YACP,MAAM,GACN,MAAM;IACX;IAEA,iBAAiB,OAAO,SAAiB,QAAgB;QACvD,OAAO,MAAM,SACV,IAAI,CAAC,qBACL,MAAM,GACN,EAAE,CAAC,YAAY,SACf,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,gBAAgB,MACpB,EAAE,CAAC,gBAAgB,IAAI,KAAK,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;IAC5F;IAEA,eAAe;IACf,gBAAgB,OAAO,QAAgB,WAAoB;QACzD,IAAI,QAAQ,SACT,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,qBAAqB;YAAE,WAAW;QAAM;QAEjD,IAAI,WAAW;YACb,QAAQ,MAAM,GAAG,CAAC,qBAAqB;QACzC;QACA,IAAI,SAAS;YACX,QAAQ,MAAM,GAAG,CAAC,qBAAqB;QACzC;QAEA,OAAO,MAAM;IACf;IAEA,iBAAiB,OAAO;QACtB,OAAO,MAAM,SACV,IAAI,CAAC,gBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;IACX;IAEA,iBAAiB,OAAO,SAAiB;QACvC,OAAO,MAAM,SACV,IAAI,CAAC,gBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;IACd;IAEA,aAAa;IACb,eAAe;QACb,OAAO,MAAM,SACV,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC;IACX;IAEA,eAAe;IACf,iBAAiB;QACf,OAAO,MAAM,SACV,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,KAAK,CAAC;IACX;IAEA,qBAAqB,OAAO;QAC1B,OAAO,MAAM,SACV,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM;IAC7C;IAEA,mBAAmB,OAAO,QAAgB;QACxC,OAAO,MAAM,SACV,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,SAAS;YACT,gBAAgB;QAClB,GACC,MAAM,GACN,MAAM;IACX;IAEA,UAAU;IACV,iBAAiB,OAAO;QACtB,OAAO,MAAM,SACV,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;IAChD;IAEA,YAAY;IACZ,kBAAkB,OAAO,QAAgB,YAAoB;QAC3D,OAAO,MAAM,SACV,IAAI,CAAC,mBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,YAAY;YAAE,WAAW;QAAM;IAC1C;IAEA,eAAe;IACf,gBAAgB,OAAO;QACrB,OAAO,MAAM,SACV,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;IAC5C;IAEA,kBAAkB,OAAO;QACvB,OAAO,MAAM,SACV,IAAI,CAAC,gBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;IACX;IAEA,gBAAgB;IAChB,kBAAkB,OAAO,QAAgB,aAAa,KAAK;QACzD,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,YAAY;YACd,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,OAAO,MAAM;IACf;IAEA,wBAAwB,OAAO;QAC7B,OAAO,MAAM,SACV,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,IAAI,OAAO,WAAW;QAAG,GAC3C,EAAE,CAAC,MAAM;IACd;AACF;AAGO,MAAM,gBAAgB;IAC3B,mBAAmB,CAAC,QAAgB;QAClC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,2CAA2C;YAC3C,OAAO;gBACL,aAAa,KAAO;YACtB;QACF;QAEA,OAAO,SACJ,OAAO,CAAC,UACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,WAAW,EAAE,QAAQ;QAChC,GACA,UAED,SAAS;IACd;IAEA,wBAAwB,CAAC,QAAgB;QACvC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBACL,aAAa,KAAO;YACtB;QACF;QAEA,OAAO,SACJ,OAAO,CAAC,eACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,WAAW,EAAE,QAAQ;QAChC,GACA,UAED,SAAS;IACd;IAEA,wBAAwB,CAAC,QAAgB;QACvC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBACL,aAAa,KAAO;YACtB;QACF;QAEA,OAAO,SACJ,OAAO,CAAC,gBACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,WAAW,EAAE,QAAQ;QAChC,GACA,UAED,SAAS;IACd;IAEA,0BAA0B,CAAC,QAAgB;QACzC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBACL,aAAa,KAAO;YACtB;QACF;QAEA,OAAO,SACJ,OAAO,CAAC,iBACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,WAAW,EAAE,QAAQ;QAChC,GACA,UAED,SAAS;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, auth, db, isSupabaseConfigured } from '@/lib/supabase';\nimport { Database } from '@/lib/database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<any>;\n  signUp: (email: string, password: string, metadata?: any) => Promise<any>;\n  signInWithGoogle: () => Promise<any>;\n  signOut: () => Promise<any>;\n  updateProfile: (updates: Partial<Profile>) => Promise<any>;\n  refreshProfile: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Demo mode - create a fake user if Supabase is not configured\n  useEffect(() => {\n    if (!isSupabaseConfigured) {\n      const demoUser = {\n        id: 'demo-user-123',\n        email: '<EMAIL>',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      } as User;\n\n      const demoProfile = {\n        id: 'demo-user-123',\n        username: 'demo_user',\n        full_name: 'Demo User',\n        avatar_url: null,\n        timezone: 'UTC',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        preferences: {\n          theme: 'light',\n          language: 'en',\n          notifications: {\n            push: true,\n            email: false,\n            sound: true,\n            reminders: true,\n            achievements: true,\n            weeklyReport: true\n          },\n          privacy: {\n            analytics: true,\n            crashReports: true,\n            dataSharing: false\n          }\n        }\n      } as Profile;\n\n      setUser(demoUser);\n      setProfile(demoProfile);\n      setSession({ user: demoUser } as Session);\n      setLoading(false);\n      return;\n    }\n  }, []);\n\n  const refreshProfile = async () => {\n    if (user) {\n      const { data, error } = await db.getProfile(user.id);\n      if (data && !error) {\n        setProfile(data);\n      }\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return { error: 'No user logged in' };\n    \n    const { error } = await db.updateProfile(user.id, updates);\n    if (!error) {\n      await refreshProfile();\n    }\n    return { error };\n  };\n\n  useEffect(() => {\n    if (!isSupabaseConfigured || !supabase) return;\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      setSession(session);\n      setUser(session?.user ?? null);\n\n      if (session?.user) {\n        await refreshProfile();\n      }\n\n      setLoading(false);\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          await refreshProfile();\n        } else {\n          setProfile(null);\n        }\n\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signIn: auth.signIn,\n    signUp: auth.signUp,\n    signInWithGoogle: auth.signInWithGoogle,\n    signOut: auth.signOut,\n    updateProfile,\n    refreshProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// Protected route wrapper\nexport function ProtectedRoute({ children }: { children: React.ReactNode }) {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return <AuthModal />;\n  }\n\n  return <>{children}</>;\n}\n\n// Auth Modal Component\nfunction AuthModal() {\n  const [isSignUp, setIsSignUp] = useState(false);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [fullName, setFullName] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const { signIn, signUp, signInWithGoogle } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(null);\n\n    try {\n      if (isSignUp) {\n        const { error } = await signUp(email, password, { full_name: fullName });\n        if (error) throw error;\n      } else {\n        const { error } = await signIn(email, password);\n        if (error) throw error;\n      }\n    } catch (error: any) {\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGoogleSignIn = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const { error } = await signInWithGoogle();\n      if (error) throw error;\n    } catch (error: any) {\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-background px-4\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <h2 className=\"mt-6 text-3xl font-bold text-foreground\">\n            {isSignUp ? 'Create your account' : 'Sign in to your account'}\n          </h2>\n          <p className=\"mt-2 text-sm text-muted-foreground\">\n            {isSignUp \n              ? 'Start building better habits today' \n              : 'Welcome back to HabitFlow'\n            }\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            {isSignUp && (\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-foreground\">\n                  Full Name\n                </label>\n                <input\n                  id=\"fullName\"\n                  name=\"fullName\"\n                  type=\"text\"\n                  required={isSignUp}\n                  value={fullName}\n                  onChange={(e) => setFullName(e.target.value)}\n                  className=\"mt-1 block w-full px-3 py-2 border border-border rounded-md shadow-sm bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-border rounded-md shadow-sm bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-foreground\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete={isSignUp ? 'new-password' : 'current-password'}\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-border rounded-md shadow-sm bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\"\n                placeholder=\"Enter your password\"\n                minLength={6}\n              />\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Loading...' : (isSignUp ? 'Sign up' : 'Sign in')}\n            </button>\n          </div>\n\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-border\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-background text-muted-foreground\">Or continue with</span>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"button\"\n              onClick={handleGoogleSignIn}\n              disabled={loading}\n              className=\"w-full flex justify-center items-center py-2 px-4 border border-border rounded-md shadow-sm text-sm font-medium text-foreground bg-background hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n              </svg>\n              Continue with Google\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={() => setIsSignUp(!isSignUp)}\n              className=\"text-sm text-primary hover:text-primary/80\"\n            >\n              {isSignUp \n                ? 'Already have an account? Sign in' \n                : \"Don't have an account? Sign up\"\n              }\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAsBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;YACzB,MAAM,WAAW;gBACf,IAAI;gBACJ,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,cAAc;gBAClB,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;oBACX,OAAO;oBACP,UAAU;oBACV,eAAe;wBACb,MAAM;wBACN,OAAO;wBACP,OAAO;wBACP,WAAW;wBACX,cAAc;wBACd,cAAc;oBAChB;oBACA,SAAS;wBACP,WAAW;wBACX,cAAc;wBACd,aAAa;oBACf;gBACF;YACF;YAEA,QAAQ;YACR,WAAW;YACX,WAAW;gBAAE,MAAM;YAAS;YAC5B,WAAW;YACX;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,UAAU,CAAC,KAAK,EAAE;YACnD,IAAI,QAAQ,CAAC,OAAO;gBAClB,WAAW;YACb;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAoB;QAE/C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;QAClD,IAAI,CAAC,OAAO;YACV,MAAM;QACR;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sHAAA,CAAA,uBAAoB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;QAExC,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM;YACR;YAEA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM;YACR,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA,QAAQ,sHAAA,CAAA,OAAI,CAAC,MAAM;QACnB,QAAQ,sHAAA,CAAA,OAAI,CAAC,MAAM;QACnB,kBAAkB,sHAAA,CAAA,OAAI,CAAC,gBAAgB;QACvC,SAAS,sHAAA,CAAA,OAAI,CAAC,OAAO;QACrB;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACxE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,8OAAC;;;;;IACV;IAEA,qBAAO;kBAAG;;AACZ;AAEA,uBAAuB;AACvB,SAAS;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAE7C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO,UAAU;oBAAE,WAAW;gBAAS;gBACtE,IAAI,OAAO,MAAM;YACnB,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO,MAAM;YACnB;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;YACxB,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,WAAW,wBAAwB;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCACV,WACG,uCACA;;;;;;;;;;;;8BAKR,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;wBACxC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA4C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,UAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA4C;;;;;;sDAG7E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA4C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAc,WAAW,iBAAiB;4CAC1C,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;4CACZ,WAAW;;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,eAAgB,WAAW,YAAY;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAI/D,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,SAAQ;;0DACpC,8OAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,8OAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,8OAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,8OAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCACxB;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WACG,qCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider } from 'next-themes';\nimport { AuthProvider, ProtectedRoute } from '@/components/auth/auth-provider';\n\n// Available themes\nconst themes = [\n  'light',\n  'dark',\n  'ocean',\n  'forest',\n  'sunset',\n  'minimal',\n  'vibrant',\n  'pastel'\n];\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <ThemeProvider\n      attribute=\"class\"\n      defaultTheme=\"light\"\n      enableSystem\n      themes={themes}\n      storageKey=\"habitflow-theme\"\n    >\n      <AuthProvider>\n        <ProtectedRoute>\n          {children}\n        </ProtectedRoute>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport { themes };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,mBAAmB;AACnB,MAAM,SAAS;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,gJAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,YAAY;QACZ,QAAQ;QACR,YAAW;kBAEX,cAAA,8OAAC,8IAAA,CAAA,eAAY;sBACX,cAAA,8OAAC,8IAAA,CAAA,iBAAc;0BACZ;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}