{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/optionsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nimport { isNan } from '../util/DataUtils';\n\n/**\n * These chart options are decided internally, by Recharts,\n * and will not change during the lifetime of the chart.\n *\n * Changing these options can be done by swapping the root element\n * which will make a brand-new Redux store.\n *\n * If you want to store options that can be changed by the user,\n * use UpdatableChartOptions in rootPropsSlice.ts.\n */\n\nexport function arrayTooltipSearcher(data, strIndex) {\n  if (!strIndex) return undefined;\n  var numIndex = Number.parseInt(strIndex, 10);\n  if (isNan(numIndex)) {\n    return undefined;\n  }\n  return data === null || data === void 0 ? void 0 : data[numIndex];\n}\nvar initialState = {\n  chartName: '',\n  tooltipPayloadSearcher: undefined,\n  eventEmitter: undefined,\n  defaultTooltipEventType: 'axis'\n};\nvar optionsSlice = createSlice({\n  name: 'options',\n  initialState,\n  reducers: {\n    createEventEmitter: state => {\n      if (state.eventEmitter == null) {\n        state.eventEmitter = Symbol('rechartsEventEmitter');\n      }\n    }\n  }\n});\nexport var optionsReducer = optionsSlice.reducer;\nexport var {\n  createEventEmitter\n} = optionsSlice.actions;"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAaO,SAAS,qBAAqB,IAAI,EAAE,QAAQ;IACjD,IAAI,CAAC,UAAU,OAAO;IACtB,IAAI,WAAW,OAAO,QAAQ,CAAC,UAAU;IACzC,IAAI,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,WAAW;QACnB,OAAO;IACT;IACA,OAAO,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS;AACnE;AACA,IAAI,eAAe;IACjB,WAAW;IACX,wBAAwB;IACxB,cAAc;IACd,yBAAyB;AAC3B;AACA,IAAI,eAAe,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,oBAAoB,CAAA;YAClB,IAAI,MAAM,YAAY,IAAI,MAAM;gBAC9B,MAAM,YAAY,GAAG,OAAO;YAC9B;QACF;IACF;AACF;AACO,IAAI,iBAAiB,aAAa,OAAO;AACzC,IAAI,EACT,kBAAkB,EACnB,GAAG,aAAa,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/tooltipSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * One Tooltip can display multiple TooltipPayloadEntries at a time.\n */\n\n/**\n * So what happens is that the tooltip payload is decided based on the available data, and the dataKey.\n * The dataKey can either be defined on the graphical element (like Line, or Bar)\n * or on the tooltip itself.\n *\n * The data can be defined in the chart element, or in the graphical item.\n *\n * So this type is all the settings, other than the data + dataKey complications.\n */\n\n/**\n * This is what Tooltip renders.\n */\n\n/**\n * null means no active index\n * string means: whichever index from the chart data it is.\n * Different charts have different requirements on data shapes,\n * and are also responsible for providing a function that will accept this index\n * and return data.\n */\n\n/**\n * Different items have different data shapes so the state has no opinion on what the data shape should be;\n * the only requirement is that the chart also provides a searcher function\n * that accepts the data, and a key, and returns whatever the payload in Tooltip should be.\n */\n\n/**\n * So this informs the \"tooltip event type\". Tooltip event type can be either \"axis\" or \"item\"\n * and it is used for two things:\n * 1. Sets the active area\n * 2. Sets the background and cursor highlights\n *\n * Some charts only allow to have one type of tooltip event type, some allow both.\n * Those charts that allow both will have one default, and the \"shared\" prop will be used to switch between them.\n * Undefined means \"use the chart default\".\n *\n * Charts that only allow one tooltip event type, will ignore the shared prop.\n */\n\n/**\n * A generic state for user interaction with the chart.\n * User interaction can come through multiple channels: mouse events, keyboard events, or hardcoded in props, or synchronised from other charts.\n *\n * Each of the interaction states is represented as TooltipInteractionState,\n * and then the selectors and Tooltip will decide which of the interaction states to use.\n */\n\nexport var noInteraction = {\n  active: false,\n  index: null,\n  dataKey: undefined,\n  coordinate: undefined\n};\n\n/**\n * The tooltip interaction state stores:\n *\n * - Which graphical item is user interacting with at the moment,\n * - which axis (or, which part of chart background) is user interacting with at the moment\n * - The data that individual graphical items wish to be displayed in case the tooltip gets activated\n */\n\nexport var initialState = {\n  itemInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  axisInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  keyboardInteraction: noInteraction,\n  syncInteraction: {\n    active: false,\n    index: null,\n    dataKey: undefined,\n    label: undefined,\n    coordinate: undefined\n  },\n  tooltipItemPayloads: [],\n  settings: {\n    shared: undefined,\n    trigger: 'hover',\n    axisId: 0,\n    active: false,\n    defaultIndex: undefined\n  }\n};\nvar tooltipSlice = createSlice({\n  name: 'tooltip',\n  initialState,\n  reducers: {\n    addTooltipEntrySettings(state, action) {\n      state.tooltipItemPayloads.push(castDraft(action.payload));\n    },\n    removeTooltipEntrySettings(state, action) {\n      var index = current(state).tooltipItemPayloads.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.tooltipItemPayloads.splice(index, 1);\n      }\n    },\n    setTooltipSettingsState(state, action) {\n      state.settings = action.payload;\n    },\n    setActiveMouseOverItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.hover.active = true;\n      state.itemInteraction.hover.index = action.payload.activeIndex;\n      state.itemInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    mouseLeaveChart(state) {\n      /*\n       * Clear only the active flags. Why?\n       * 1. Keep Coordinate to preserve animation - next time the Tooltip appears, we want to render it from\n       * the last place where it was when it disappeared.\n       * 2. We want to keep all the properties anyway just in case the tooltip has `active=true` prop\n       * and continues being visible even after the mouse has left the chart.\n       */\n      state.itemInteraction.hover.active = false;\n      state.axisInteraction.hover.active = false;\n    },\n    mouseLeaveItem(state) {\n      state.itemInteraction.hover.active = false;\n    },\n    setActiveClickItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.itemInteraction.click.active = true;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.click.index = action.payload.activeIndex;\n      state.itemInteraction.click.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseOverAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.axisInteraction.hover.active = true;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.hover.index = action.payload.activeIndex;\n      state.axisInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseClickAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.click.active = true;\n      state.axisInteraction.click.index = action.payload.activeIndex;\n      state.axisInteraction.click.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setSyncInteraction(state, action) {\n      state.syncInteraction = action.payload;\n    },\n    setKeyboardInteraction(state, action) {\n      state.keyboardInteraction.active = action.payload.active;\n      state.keyboardInteraction.index = action.payload.activeIndex;\n      state.keyboardInteraction.coordinate = action.payload.activeCoordinate;\n      state.keyboardInteraction.dataKey = action.payload.activeDataKey;\n    }\n  }\n});\nexport var {\n  addTooltipEntrySettings,\n  removeTooltipEntrySettings,\n  setTooltipSettingsState,\n  setActiveMouseOverItemIndex,\n  mouseLeaveItem,\n  mouseLeaveChart,\n  setActiveClickItemIndex,\n  setMouseOverAxisIndex,\n  setMouseClickAxisIndex,\n  setSyncInteraction,\n  setKeyboardInteraction\n} = tooltipSlice.actions;\nexport var tooltipReducer = tooltipSlice.reducer;"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;;;AAwDO,IAAI,gBAAgB;IACzB,QAAQ;IACR,OAAO;IACP,SAAS;IACT,YAAY;AACd;AAUO,IAAI,eAAe;IACxB,iBAAiB;QACf,OAAO;QACP,OAAO;IACT;IACA,iBAAiB;QACf,OAAO;QACP,OAAO;IACT;IACA,qBAAqB;IACrB,iBAAiB;QACf,QAAQ;QACR,OAAO;QACP,SAAS;QACT,OAAO;QACP,YAAY;IACd;IACA,qBAAqB,EAAE;IACvB,UAAU;QACR,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;AACF;AACA,IAAI,eAAe,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,yBAAwB,KAAK,EAAE,MAAM;YACnC,MAAM,mBAAmB,CAAC,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QACzD;QACA,4BAA2B,KAAK,EAAE,MAAM;YACtC,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;YAC/E,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,mBAAmB,CAAC,MAAM,CAAC,OAAO;YAC1C;QACF;QACA,yBAAwB,KAAK,EAAE,MAAM;YACnC,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,6BAA4B,KAAK,EAAE,MAAM;YACvC,MAAM,eAAe,CAAC,MAAM,GAAG;YAC/B,MAAM,mBAAmB,CAAC,MAAM,GAAG;YACnC,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9D,MAAM,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,aAAa;YAClE,MAAM,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,gBAAgB;QAC1E;QACA,iBAAgB,KAAK;YACnB;;;;;;OAMC,GACD,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;QACvC;QACA,gBAAe,KAAK;YAClB,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;QACvC;QACA,yBAAwB,KAAK,EAAE,MAAM;YACnC,MAAM,eAAe,CAAC,MAAM,GAAG;YAC/B,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC,MAAM,mBAAmB,CAAC,MAAM,GAAG;YACnC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9D,MAAM,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,aAAa;YAClE,MAAM,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,gBAAgB;QAC1E;QACA,uBAAsB,KAAK,EAAE,MAAM;YACjC,MAAM,eAAe,CAAC,MAAM,GAAG;YAC/B,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC,MAAM,mBAAmB,CAAC,MAAM,GAAG;YACnC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9D,MAAM,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,aAAa;YAClE,MAAM,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,gBAAgB;QAC1E;QACA,wBAAuB,KAAK,EAAE,MAAM;YAClC,MAAM,eAAe,CAAC,MAAM,GAAG;YAC/B,MAAM,mBAAmB,CAAC,MAAM,GAAG;YACnC,MAAM,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9D,MAAM,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,aAAa;YAClE,MAAM,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,gBAAgB;QAC1E;QACA,oBAAmB,KAAK,EAAE,MAAM;YAC9B,MAAM,eAAe,GAAG,OAAO,OAAO;QACxC;QACA,wBAAuB,KAAK,EAAE,MAAM;YAClC,MAAM,mBAAmB,CAAC,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YACxD,MAAM,mBAAmB,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,WAAW;YAC5D,MAAM,mBAAmB,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,gBAAgB;YACtE,MAAM,mBAAmB,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,aAAa;QAClE;IACF;AACF;AACO,IAAI,EACT,uBAAuB,EACvB,0BAA0B,EAC1B,uBAAuB,EACvB,2BAA2B,EAC3B,cAAc,EACd,eAAe,EACf,uBAAuB,EACvB,qBAAqB,EACrB,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB,EACvB,GAAG,aAAa,OAAO;AACjB,IAAI,iBAAiB,aAAa,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/chartDataSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * This is the data that's coming through main chart `data` prop\n * Recharts is very flexible in what it accepts so the type is very flexible too.\n * This will typically be an object, and various components will provide various `dataKey`\n * that dictates how to pull data from that object.\n *\n * TL;DR: before dataKey\n */\n\n/**\n * So this is the same unknown type as ChartData but this is after the dataKey has been applied.\n * We still don't know what the type is - that depends on what exactly it was before the dataKey application,\n * and the dataKey can return whatever anyway - but let's keep it separate as a form of documentation.\n *\n * TL;DR: ChartData after dataKey.\n */\n\nexport var initialChartDataState = {\n  chartData: undefined,\n  computedData: undefined,\n  dataStartIndex: 0,\n  dataEndIndex: 0\n};\nvar chartDataSlice = createSlice({\n  name: 'chartData',\n  initialState: initialChartDataState,\n  reducers: {\n    setChartData(state, action) {\n      state.chartData = action.payload;\n      if (action.payload == null) {\n        state.dataStartIndex = 0;\n        state.dataEndIndex = 0;\n        return;\n      }\n      if (action.payload.length > 0 && state.dataEndIndex !== action.payload.length - 1) {\n        state.dataEndIndex = action.payload.length - 1;\n      }\n    },\n    setComputedData(state, action) {\n      state.computedData = action.payload;\n    },\n    setDataStartEndIndexes(state, action) {\n      var {\n        startIndex,\n        endIndex\n      } = action.payload;\n      if (startIndex != null) {\n        state.dataStartIndex = startIndex;\n      }\n      if (endIndex != null) {\n        state.dataEndIndex = endIndex;\n      }\n    }\n  }\n});\nexport var {\n  setChartData,\n  setDataStartEndIndexes,\n  setComputedData\n} = chartDataSlice.actions;\nexport var chartDataReducer = chartDataSlice.reducer;"], "names": [], "mappings": ";;;;;;;AAAA;;AAmBO,IAAI,wBAAwB;IACjC,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,cAAc;AAChB;AACA,IAAI,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC/B,MAAM;IACN,cAAc;IACd,UAAU;QACR,cAAa,KAAK,EAAE,MAAM;YACxB,MAAM,SAAS,GAAG,OAAO,OAAO;YAChC,IAAI,OAAO,OAAO,IAAI,MAAM;gBAC1B,MAAM,cAAc,GAAG;gBACvB,MAAM,YAAY,GAAG;gBACrB;YACF;YACA,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,KAAK,MAAM,YAAY,KAAK,OAAO,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjF,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,MAAM,GAAG;YAC/C;QACF;QACA,iBAAgB,KAAK,EAAE,MAAM;YAC3B,MAAM,YAAY,GAAG,OAAO,OAAO;QACrC;QACA,wBAAuB,KAAK,EAAE,MAAM;YAClC,IAAI,EACF,UAAU,EACV,QAAQ,EACT,GAAG,OAAO,OAAO;YAClB,IAAI,cAAc,MAAM;gBACtB,MAAM,cAAc,GAAG;YACzB;YACA,IAAI,YAAY,MAAM;gBACpB,MAAM,YAAY,GAAG;YACvB;QACF;IACF;AACF;AACO,IAAI,EACT,YAAY,EACZ,sBAAsB,EACtB,eAAe,EAChB,GAAG,eAAe,OAAO;AACnB,IAAI,mBAAmB,eAAe,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/layoutSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nvar initialState = {\n  layoutType: 'horizontal',\n  width: 0,\n  height: 0,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  scale: 1\n};\nvar chartLayoutSlice = createSlice({\n  name: 'chartLayout',\n  initialState,\n  reducers: {\n    setLayout(state, action) {\n      state.layoutType = action.payload;\n    },\n    setChartSize(state, action) {\n      state.width = action.payload.width;\n      state.height = action.payload.height;\n    },\n    setMargin(state, action) {\n      state.margin.top = action.payload.top;\n      state.margin.right = action.payload.right;\n      state.margin.bottom = action.payload.bottom;\n      state.margin.left = action.payload.left;\n    },\n    setScale(state, action) {\n      state.scale = action.payload;\n    }\n  }\n});\nexport var {\n  setMargin,\n  setLayout,\n  setChartSize,\n  setScale\n} = chartLayoutSlice.actions;\nexport var chartLayoutReducer = chartLayoutSlice.reducer;"], "names": [], "mappings": ";;;;;;;AAAA;;AACA,IAAI,eAAe;IACjB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,QAAQ;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IACA,OAAO;AACT;AACA,IAAI,mBAAmB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU;QACR,WAAU,KAAK,EAAE,MAAM;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QACA,cAAa,KAAK,EAAE,MAAM;YACxB,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAClC,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;QACtC;QACA,WAAU,KAAK,EAAE,MAAM;YACrB,MAAM,MAAM,CAAC,GAAG,GAAG,OAAO,OAAO,CAAC,GAAG;YACrC,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YACzC,MAAM,MAAM,CAAC,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YAC3C,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;QACzC;QACA,UAAS,KAAK,EAAE,MAAM;YACpB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACF;AACF;AACO,IAAI,EACT,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,EACT,GAAG,iBAAiB,OAAO;AACrB,IAAI,qBAAqB,iBAAiB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/RechartsReduxContext.js"], "sourcesContent": ["import { createContext } from 'react';\n\n/*\n * This is a copy of the React-Redux context type, but with our own store type.\n * We could import directly from react-redux like this:\n * import { ReactReduxContextValue } from 'react-redux/src/components/Context';\n * but that makes typescript angry with some errors I am not sure how to resolve\n * so copy it is.\n */\n\n/**\n * We need to use our own independent Redux context because we need to avoid interfering with other people's Redux stores\n * in case they decide to install and use Recharts in another Redux app which is likely to happen.\n *\n * https://react-redux.js.org/using-react-redux/accessing-store#providing-custom-context\n */\nexport var RechartsReduxContext = /*#__PURE__*/createContext(null);"], "names": [], "mappings": ";;;AAAA;;AAgBO,IAAI,uBAAuB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/hooks.js"], "sourcesContent": ["import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nimport { useContext } from 'react';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nvar noopDispatch = a => a;\nexport var useAppDispatch = () => {\n  var context = useContext(RechartsReduxContext);\n  if (context) {\n    return context.store.dispatch;\n  }\n  return noopDispatch;\n};\nvar noop = () => {};\nvar addNestedSubNoop = () => noop;\nvar refEquality = (a, b) => a === b;\n\n/**\n * This is a recharts variant of `useSelector` from 'react-redux' package.\n *\n * The difference is that react-redux version will throw an Error when used outside of Redux context.\n *\n * This, recharts version, will return undefined instead.\n *\n * This is because we want to allow using our components outside the Chart wrapper,\n * and have people provide all props explicitly.\n *\n * If however they use the component inside a chart wrapper then those props become optional,\n * and we read them from Redux state instead.\n *\n * @param selector for pulling things out of Redux store; will not be called if the store is not accessible\n * @return whatever the selector returned; or undefined when outside of Redux store\n */\nexport function useAppSelector(selector) {\n  var context = useContext(RechartsReduxContext);\n  return useSyncExternalStoreWithSelector(context ? context.subscription.addNestedSub : addNestedSubNoop, context ? context.store.getState : noop, context ? context.store.getState : noop, context ? selector : noop, refEquality);\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,IAAI,eAAe,CAAA,IAAK;AACjB,IAAI,iBAAiB;IAC1B,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,uBAAoB;IAC7C,IAAI,SAAS;QACX,OAAO,QAAQ,KAAK,CAAC,QAAQ;IAC/B;IACA,OAAO;AACT;AACA,IAAI,OAAO,KAAO;AAClB,IAAI,mBAAmB,IAAM;AAC7B,IAAI,cAAc,CAAC,GAAG,IAAM,MAAM;AAkB3B,SAAS,eAAe,QAAQ;IACrC,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,uBAAoB;IAC7C,OAAO,CAAA,GAAA,4KAAA,CAAA,mCAAgC,AAAD,EAAE,UAAU,QAAQ,YAAY,CAAC,YAAY,GAAG,kBAAkB,UAAU,QAAQ,KAAK,CAAC,QAAQ,GAAG,MAAM,UAAU,QAAQ,KAAK,CAAC,QAAQ,GAAG,MAAM,UAAU,WAAW,MAAM;AACvN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/legendSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nexport var selectLegendSettings = state => state.legend.settings;\nexport var selectLegendSize = state => state.legend.size;\nvar selectAllLegendPayload2DArray = state => state.legend.payload;\nexport var selectLegendPayload = createSelector([selectAllLegendPayload2DArray, selectLegendSettings], (payloads, _ref) => {\n  var {\n    itemSorter\n  } = _ref;\n  var flat = payloads.flat(1);\n  return itemSorter ? sortBy(flat, itemSorter) : flat;\n});"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AACO,IAAI,uBAAuB,CAAA,QAAS,MAAM,MAAM,CAAC,QAAQ;AACzD,IAAI,mBAAmB,CAAA,QAAS,MAAM,MAAM,CAAC,IAAI;AACxD,IAAI,gCAAgC,CAAA,QAAS,MAAM,MAAM,CAAC,OAAO;AAC1D,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B;CAAqB,EAAE,CAAC,UAAU;IAChH,IAAI,EACF,UAAU,EACX,GAAG;IACJ,IAAI,OAAO,SAAS,IAAI,CAAC;IACzB,OAAO,aAAa,CAAA,GAAA,iJAAA,CAAA,UAAM,AAAD,EAAE,MAAM,cAAc;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/containerSelectors.js"], "sourcesContent": ["export var selectChartWidth = state => state.layout.width;\nexport var selectChartHeight = state => state.layout.height;\nexport var selectContainerScale = state => state.layout.scale;\nexport var selectMargin = state => state.layout.margin;"], "names": [], "mappings": ";;;;;;AAAO,IAAI,mBAAmB,CAAA,QAAS,MAAM,MAAM,CAAC,KAAK;AAClD,IAAI,oBAAoB,CAAA,QAAS,MAAM,MAAM,CAAC,MAAM;AACpD,IAAI,uBAAuB,CAAA,QAAS,MAAM,MAAM,CAAC,KAAK;AACtD,IAAI,eAAe,CAAA,QAAS,MAAM,MAAM,CAAC,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectAllAxes.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nexport var selectAllXAxes = createSelector(state => state.cartesianAxis.xAxis, xAxisMap => {\n  return Object.values(xAxisMap);\n});\nexport var selectAllYAxes = createSelector(state => state.cartesianAxis.yAxis, yAxisMap => {\n  return Object.values(yAxisMap);\n});"], "names": [], "mappings": ";;;;AAAA;;AACO,IAAI,iBAAiB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,MAAM,aAAa,CAAC,KAAK,EAAE,CAAA;IAC7E,OAAO,OAAO,MAAM,CAAC;AACvB;AACO,IAAI,iBAAiB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,MAAM,aAAa,CAAC,KAAK,EAAE,CAAA;IAC7E,OAAO,OAAO,MAAM,CAAC;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectChartOffsetInternal.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport get from 'es-toolkit/compat/get';\nimport { selectLegendSettings, selectLegendSize } from './legendSelectors';\nimport { appendOffsetOfLegend } from '../../util/ChartUtils';\nimport { selectChartHeight, selectChartWidth, selectMargin } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nexport var selectBrushHeight = state => state.brush.height;\n\n/**\n * For internal use only.\n *\n * @param root state\n * @return ChartOffsetInternal\n */\nexport var selectChartOffsetInternal = createSelector([selectChartWidth, selectChartHeight, selectMargin, selectBrushHeight, selectAllXAxes, selectAllYAxes, selectLegendSettings, selectLegendSize], (chartWidth, chartHeight, margin, brushHeight, xAxes, yAxes, legendSettings, legendSize) => {\n  var offsetH = yAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      var width = typeof entry.width === 'number' ? entry.width : DEFAULT_Y_AXIS_WIDTH;\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: result[orientation] + width\n      });\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = xAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: get(result, \"\".concat(orientation)) + entry.height\n      });\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  offset.bottom += brushHeight;\n  offset = appendOffsetOfLegend(offset, legendSettings, legendSize);\n  var offsetWidth = chartWidth - offset.left - offset.right;\n  var offsetHeight = chartHeight - offset.top - offset.bottom;\n  return _objectSpread(_objectSpread({\n    brushBottom\n  }, offset), {}, {\n    // never return negative values for height and width\n    width: Math.max(offsetWidth, 0),\n    height: Math.max(offsetHeight, 0)\n  });\n});\nexport var selectChartViewBox = createSelector(selectChartOffsetInternal, offset => ({\n  x: offset.left,\n  y: offset.top,\n  width: offset.width,\n  height: offset.height\n}));\nexport var selectAxisViewBox = createSelector(selectChartWidth, selectChartHeight, (width, height) => ({\n  x: 0,\n  y: 0,\n  width,\n  height\n}));"], "names": [], "mappings": ";;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;;AAQhT,IAAI,oBAAoB,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM;AAQnD,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;IAAE,2KAAA,CAAA,eAAY;IAAE;IAAmB,sKAAA,CAAA,iBAAc;IAAE,sKAAA,CAAA,iBAAc;IAAE,wKAAA,CAAA,uBAAoB;IAAE,wKAAA,CAAA,mBAAgB;CAAC,EAAE,CAAC,YAAY,aAAa,QAAQ,aAAa,OAAO,OAAO,gBAAgB;IACjR,IAAI,UAAU,MAAM,MAAM,CAAC,CAAC,QAAQ;QAClC,IAAI,EACF,WAAW,EACZ,GAAG;QACJ,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,EAAE;YAChC,IAAI,QAAQ,OAAO,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,GAAG,oJAAA,CAAA,uBAAoB;YAChF,OAAO,cAAc,cAAc,CAAC,GAAG,SAAS,CAAC,GAAG;gBAClD,CAAC,YAAY,EAAE,MAAM,CAAC,YAAY,GAAG;YACvC;QACF;QACA,OAAO;IACT,GAAG;QACD,MAAM,OAAO,IAAI,IAAI;QACrB,OAAO,OAAO,KAAK,IAAI;IACzB;IACA,IAAI,UAAU,MAAM,MAAM,CAAC,CAAC,QAAQ;QAClC,IAAI,EACF,WAAW,EACZ,GAAG;QACJ,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,EAAE;YAChC,OAAO,cAAc,cAAc,CAAC,GAAG,SAAS,CAAC,GAAG;gBAClD,CAAC,YAAY,EAAE,CAAA,GAAA,8IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,GAAG,MAAM,CAAC,gBAAgB,MAAM,MAAM;YACnE;QACF;QACA,OAAO;IACT,GAAG;QACD,KAAK,OAAO,GAAG,IAAI;QACnB,QAAQ,OAAO,MAAM,IAAI;IAC3B;IACA,IAAI,SAAS,cAAc,cAAc,CAAC,GAAG,UAAU;IACvD,IAAI,cAAc,OAAO,MAAM;IAC/B,OAAO,MAAM,IAAI;IACjB,SAAS,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,gBAAgB;IACtD,IAAI,cAAc,aAAa,OAAO,IAAI,GAAG,OAAO,KAAK;IACzD,IAAI,eAAe,cAAc,OAAO,GAAG,GAAG,OAAO,MAAM;IAC3D,OAAO,cAAc,cAAc;QACjC;IACF,GAAG,SAAS,CAAC,GAAG;QACd,oDAAoD;QACpD,OAAO,KAAK,GAAG,CAAC,aAAa;QAC7B,QAAQ,KAAK,GAAG,CAAC,cAAc;IACjC;AACF;AACO,IAAI,qBAAqB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B,CAAA,SAAU,CAAC;QACnF,GAAG,OAAO,IAAI;QACd,GAAG,OAAO,GAAG;QACb,OAAO,OAAO,KAAK;QACnB,QAAQ,OAAO,MAAM;IACvB,CAAC;AACM,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2KAAA,CAAA,mBAAgB,EAAE,2KAAA,CAAA,oBAAiB,EAAE,CAAC,OAAO,SAAW,CAAC;QACrG,GAAG;QACH,GAAG;QACH;QACA;IACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/brushSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectMargin } from './containerSelectors';\nimport { isNumber } from '../../util/DataUtils';\nexport var selectBrushSettings = state => state.brush;\nexport var selectBrushDimensions = createSelector([selectBrushSettings, selectChartOffsetInternal, selectMargin], (brushSettings, offset, margin) => ({\n  height: brushSettings.height,\n  x: isNumber(brushSettings.x) ? brushSettings.x : offset.left,\n  y: isNumber(brushSettings.y) ? brushSettings.y : offset.top + offset.height + offset.brushBottom - ((margin === null || margin === void 0 ? void 0 : margin.bottom) || 0),\n  width: isNumber(brushSettings.width) ? brushSettings.width : offset.width\n}));"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACO,IAAI,sBAAsB,CAAA,QAAS,MAAM,KAAK;AAC9C,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB,kLAAA,CAAA,4BAAyB;IAAE,2KAAA,CAAA,eAAY;CAAC,EAAE,CAAC,eAAe,QAAQ,SAAW,CAAC;QACpJ,QAAQ,cAAc,MAAM;QAC5B,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,CAAC,IAAI,cAAc,CAAC,GAAG,OAAO,IAAI;QAC5D,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,CAAC,IAAI,cAAc,CAAC,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG,OAAO,WAAW,GAAG,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,KAAK,CAAC;QACxK,OAAO,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,KAAK,IAAI,cAAc,KAAK,GAAG,OAAO,KAAK;IAC3E,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/dataSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\n/**\n * This selector always returns the data with the indexes set by a Brush.\n * Trouble is, that might or might not be what you want.\n *\n * In charts with Brush, you will sometimes want to select the full range of data, and sometimes the one decided by the Brush\n * - even if the Brush is active, the panorama inside the Brush should show the full range of data.\n *\n * So instead of this selector, consider using either selectChartDataAndAlwaysIgnoreIndexes or selectChartDataWithIndexesIfNotInPanorama\n *\n * @param state RechartsRootState\n * @returns data defined on the chart root element, such as <PERSON><PERSON><PERSON> or ScatterChart\n */\nexport var selectChartDataWithIndexes = state => state.chartData;\n\n/**\n * This selector will always return the full range of data, ignoring the indexes set by a Brush.\n * Useful for when you want to render the full range of data, even if a Brush is active.\n * For example: in the Brush panorama, in Legend, in Tooltip.\n */\nexport var selectChartDataAndAlwaysIgnoreIndexes = createSelector([selectChartDataWithIndexes], dataState => {\n  var dataEndIndex = dataState.chartData != null ? dataState.chartData.length - 1 : 0;\n  return {\n    chartData: dataState.chartData,\n    computedData: dataState.computedData,\n    dataEndIndex,\n    dataStartIndex: 0\n  };\n});\nexport var selectChartDataWithIndexesIfNotInPanorama = (state, _xAxisId, _yAxisId, isPanorama) => {\n  if (isPanorama) {\n    return selectChartDataAndAlwaysIgnoreIndexes(state);\n  }\n  return selectChartDataWithIndexes(state);\n};"], "names": [], "mappings": ";;;;;AAAA;;AAaO,IAAI,6BAA6B,CAAA,QAAS,MAAM,SAAS;AAOzD,IAAI,wCAAwC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA2B,EAAE,CAAA;IAC9F,IAAI,eAAe,UAAU,SAAS,IAAI,OAAO,UAAU,SAAS,CAAC,MAAM,GAAG,IAAI;IAClF,OAAO;QACL,WAAW,UAAU,SAAS;QAC9B,cAAc,UAAU,YAAY;QACpC;QACA,gBAAgB;IAClB;AACF;AACO,IAAI,4CAA4C,CAAC,OAAO,UAAU,UAAU;IACjF,IAAI,YAAY;QACd,OAAO,sCAAsC;IAC/C;IACA,OAAO,2BAA2B;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/rootPropsSelectors.js"], "sourcesContent": ["export var selectRootMaxBarSize = state => state.rootProps.maxBarSize;\nexport var selectBarGap = state => state.rootProps.barGap;\nexport var selectBarCategoryGap = state => state.rootProps.barCategoryGap;\nexport var selectRootBarSize = state => state.rootProps.barSize;\nexport var selectStackOffsetType = state => state.rootProps.stackOffset;\nexport var selectChartName = state => state.options.chartName;\nexport var selectSyncId = state => state.rootProps.syncId;\nexport var selectSyncMethod = state => state.rootProps.syncMethod;\nexport var selectEventEmitter = state => state.options.eventEmitter;"], "names": [], "mappings": ";;;;;;;;;;;AAAO,IAAI,uBAAuB,CAAA,QAAS,MAAM,SAAS,CAAC,UAAU;AAC9D,IAAI,eAAe,CAAA,QAAS,MAAM,SAAS,CAAC,MAAM;AAClD,IAAI,uBAAuB,CAAA,QAAS,MAAM,SAAS,CAAC,cAAc;AAClE,IAAI,oBAAoB,CAAA,QAAS,MAAM,SAAS,CAAC,OAAO;AACxD,IAAI,wBAAwB,CAAA,QAAS,MAAM,SAAS,CAAC,WAAW;AAChE,IAAI,kBAAkB,CAAA,QAAS,MAAM,OAAO,CAAC,SAAS;AACtD,IAAI,eAAe,CAAA,QAAS,MAAM,SAAS,CAAC,MAAM;AAClD,IAAI,mBAAmB,CAAA,QAAS,MAAM,SAAS,CAAC,UAAU;AAC1D,IAAI,qBAAqB,CAAA,QAAS,MAAM,OAAO,CAAC,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineAxisRangeWithReverse.js"], "sourcesContent": ["export var combineAxisRangeWithReverse = (axisSettings, axisRange) => {\n  if (!axisSettings || !axisRange) {\n    return undefined;\n  }\n  if (axisSettings !== null && axisSettings !== void 0 && axisSettings.reversed) {\n    return [axisRange[1], axisRange[0]];\n  }\n  return axisRange;\n};"], "names": [], "mappings": ";;;AAAO,IAAI,8BAA8B,CAAC,cAAc;IACtD,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAC/B,OAAO;IACT;IACA,IAAI,iBAAiB,QAAQ,iBAAiB,KAAK,KAAK,aAAa,QAAQ,EAAE;QAC7E,OAAO;YAAC,SAAS,CAAC,EAAE;YAAE,SAAS,CAAC,EAAE;SAAC;IACrC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/polarAxisSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getMaxRadius } from '../../util/PolarUtils';\nimport { getPercentValue } from '../../util/DataUtils';\nimport { defaultPolarAngleAxisProps } from '../../polar/defaultPolarAngleAxisProps';\nimport { defaultPolarRadiusAxisProps } from '../../polar/defaultPolarRadiusAxisProps';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nexport var implicitAngleAxis = {\n  allowDataOverflow: false,\n  allowDecimals: false,\n  allowDuplicatedCategory: false,\n  // defaultPolarAngleAxisProps.allowDuplicatedCategory has it set to true but the actual axis rendering ignores the prop because reasons,\n  dataKey: undefined,\n  domain: undefined,\n  id: defaultPolarAngleAxisProps.angleAxisId,\n  includeHidden: false,\n  name: undefined,\n  reversed: defaultPolarAngleAxisProps.reversed,\n  scale: defaultPolarAngleAxisProps.scale,\n  tick: defaultPolarAngleAxisProps.tick,\n  tickCount: undefined,\n  ticks: undefined,\n  type: defaultPolarAngleAxisProps.type,\n  unit: undefined\n};\nexport var implicitRadiusAxis = {\n  allowDataOverflow: defaultPolarRadiusAxisProps.allowDataOverflow,\n  allowDecimals: false,\n  allowDuplicatedCategory: defaultPolarRadiusAxisProps.allowDuplicatedCategory,\n  dataKey: undefined,\n  domain: undefined,\n  id: defaultPolarRadiusAxisProps.radiusAxisId,\n  includeHidden: false,\n  name: undefined,\n  reversed: false,\n  scale: defaultPolarRadiusAxisProps.scale,\n  tick: defaultPolarRadiusAxisProps.tick,\n  tickCount: defaultPolarRadiusAxisProps.tickCount,\n  ticks: undefined,\n  type: defaultPolarRadiusAxisProps.type,\n  unit: undefined\n};\nexport var implicitRadialBarAngleAxis = {\n  allowDataOverflow: false,\n  allowDecimals: false,\n  allowDuplicatedCategory: defaultPolarAngleAxisProps.allowDuplicatedCategory,\n  dataKey: undefined,\n  domain: undefined,\n  id: defaultPolarAngleAxisProps.angleAxisId,\n  includeHidden: false,\n  name: undefined,\n  reversed: false,\n  scale: defaultPolarAngleAxisProps.scale,\n  tick: defaultPolarAngleAxisProps.tick,\n  tickCount: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined\n};\nexport var implicitRadialBarRadiusAxis = {\n  allowDataOverflow: defaultPolarRadiusAxisProps.allowDataOverflow,\n  allowDecimals: false,\n  allowDuplicatedCategory: defaultPolarRadiusAxisProps.allowDuplicatedCategory,\n  dataKey: undefined,\n  domain: undefined,\n  id: defaultPolarRadiusAxisProps.radiusAxisId,\n  includeHidden: false,\n  name: undefined,\n  reversed: false,\n  scale: defaultPolarRadiusAxisProps.scale,\n  tick: defaultPolarRadiusAxisProps.tick,\n  tickCount: defaultPolarRadiusAxisProps.tickCount,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectAngleAxis = (state, angleAxisId) => {\n  if (state.polarAxis.angleAxis[angleAxisId] != null) {\n    return state.polarAxis.angleAxis[angleAxisId];\n  }\n  if (state.layout.layoutType === 'radial') {\n    return implicitRadialBarAngleAxis;\n  }\n  return implicitAngleAxis;\n};\nexport var selectRadiusAxis = (state, radiusAxisId) => {\n  if (state.polarAxis.radiusAxis[radiusAxisId] != null) {\n    return state.polarAxis.radiusAxis[radiusAxisId];\n  }\n  if (state.layout.layoutType === 'radial') {\n    return implicitRadialBarRadiusAxis;\n  }\n  return implicitRadiusAxis;\n};\nexport var selectPolarOptions = state => state.polarOptions;\nexport var selectMaxRadius = createSelector([selectChartWidth, selectChartHeight, selectChartOffsetInternal], getMaxRadius);\nvar selectInnerRadius = createSelector([selectPolarOptions, selectMaxRadius], (polarChartOptions, maxRadius) => {\n  if (polarChartOptions == null) {\n    return undefined;\n  }\n  return getPercentValue(polarChartOptions.innerRadius, maxRadius, 0);\n});\nexport var selectOuterRadius = createSelector([selectPolarOptions, selectMaxRadius], (polarChartOptions, maxRadius) => {\n  if (polarChartOptions == null) {\n    return undefined;\n  }\n  return getPercentValue(polarChartOptions.outerRadius, maxRadius, maxRadius * 0.8);\n});\nvar combineAngleAxisRange = polarOptions => {\n  if (polarOptions == null) {\n    return [0, 0];\n  }\n  var {\n    startAngle,\n    endAngle\n  } = polarOptions;\n  return [startAngle, endAngle];\n};\nexport var selectAngleAxisRange = createSelector([selectPolarOptions], combineAngleAxisRange);\nexport var selectAngleAxisRangeWithReversed = createSelector([selectAngleAxis, selectAngleAxisRange], combineAxisRangeWithReverse);\nexport var selectRadiusAxisRange = createSelector([selectMaxRadius, selectInnerRadius, selectOuterRadius], (maxRadius, innerRadius, outerRadius) => {\n  if (maxRadius == null || innerRadius == null || outerRadius == null) {\n    return undefined;\n  }\n  return [innerRadius, outerRadius];\n});\nexport var selectRadiusAxisRangeWithReversed = createSelector([selectRadiusAxis, selectRadiusAxisRange], combineAxisRangeWithReverse);\nexport var selectPolarViewBox = createSelector([selectChartLayout, selectPolarOptions, selectInnerRadius, selectOuterRadius, selectChartWidth, selectChartHeight], (layout, polarOptions, innerRadius, outerRadius, width, height) => {\n  if (layout !== 'centric' && layout !== 'radial' || polarOptions == null || innerRadius == null || outerRadius == null) {\n    return undefined;\n  }\n  var {\n    cx,\n    cy,\n    startAngle,\n    endAngle\n  } = polarOptions;\n  return {\n    cx: getPercentValue(cx, width, width / 2),\n    cy: getPercentValue(cy, height, height / 2),\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle,\n    clockWise: false\n  };\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,IAAI,oBAAoB;IAC7B,mBAAmB;IACnB,eAAe;IACf,yBAAyB;IACzB,wIAAwI;IACxI,SAAS;IACT,QAAQ;IACR,IAAI,sKAAA,CAAA,6BAA0B,CAAC,WAAW;IAC1C,eAAe;IACf,MAAM;IACN,UAAU,sKAAA,CAAA,6BAA0B,CAAC,QAAQ;IAC7C,OAAO,sKAAA,CAAA,6BAA0B,CAAC,KAAK;IACvC,MAAM,sKAAA,CAAA,6BAA0B,CAAC,IAAI;IACrC,WAAW;IACX,OAAO;IACP,MAAM,sKAAA,CAAA,6BAA0B,CAAC,IAAI;IACrC,MAAM;AACR;AACO,IAAI,qBAAqB;IAC9B,mBAAmB,uKAAA,CAAA,8BAA2B,CAAC,iBAAiB;IAChE,eAAe;IACf,yBAAyB,uKAAA,CAAA,8BAA2B,CAAC,uBAAuB;IAC5E,SAAS;IACT,QAAQ;IACR,IAAI,uKAAA,CAAA,8BAA2B,CAAC,YAAY;IAC5C,eAAe;IACf,MAAM;IACN,UAAU;IACV,OAAO,uKAAA,CAAA,8BAA2B,CAAC,KAAK;IACxC,MAAM,uKAAA,CAAA,8BAA2B,CAAC,IAAI;IACtC,WAAW,uKAAA,CAAA,8BAA2B,CAAC,SAAS;IAChD,OAAO;IACP,MAAM,uKAAA,CAAA,8BAA2B,CAAC,IAAI;IACtC,MAAM;AACR;AACO,IAAI,6BAA6B;IACtC,mBAAmB;IACnB,eAAe;IACf,yBAAyB,sKAAA,CAAA,6BAA0B,CAAC,uBAAuB;IAC3E,SAAS;IACT,QAAQ;IACR,IAAI,sKAAA,CAAA,6BAA0B,CAAC,WAAW;IAC1C,eAAe;IACf,MAAM;IACN,UAAU;IACV,OAAO,sKAAA,CAAA,6BAA0B,CAAC,KAAK;IACvC,MAAM,sKAAA,CAAA,6BAA0B,CAAC,IAAI;IACrC,WAAW;IACX,OAAO;IACP,MAAM;IACN,MAAM;AACR;AACO,IAAI,8BAA8B;IACvC,mBAAmB,uKAAA,CAAA,8BAA2B,CAAC,iBAAiB;IAChE,eAAe;IACf,yBAAyB,uKAAA,CAAA,8BAA2B,CAAC,uBAAuB;IAC5E,SAAS;IACT,QAAQ;IACR,IAAI,uKAAA,CAAA,8BAA2B,CAAC,YAAY;IAC5C,eAAe;IACf,MAAM;IACN,UAAU;IACV,OAAO,uKAAA,CAAA,8BAA2B,CAAC,KAAK;IACxC,MAAM,uKAAA,CAAA,8BAA2B,CAAC,IAAI;IACtC,WAAW,uKAAA,CAAA,8BAA2B,CAAC,SAAS;IAChD,OAAO;IACP,MAAM;IACN,MAAM;AACR;AACO,IAAI,kBAAkB,CAAC,OAAO;IACnC,IAAI,MAAM,SAAS,CAAC,SAAS,CAAC,YAAY,IAAI,MAAM;QAClD,OAAO,MAAM,SAAS,CAAC,SAAS,CAAC,YAAY;IAC/C;IACA,IAAI,MAAM,MAAM,CAAC,UAAU,KAAK,UAAU;QACxC,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,mBAAmB,CAAC,OAAO;IACpC,IAAI,MAAM,SAAS,CAAC,UAAU,CAAC,aAAa,IAAI,MAAM;QACpD,OAAO,MAAM,SAAS,CAAC,UAAU,CAAC,aAAa;IACjD;IACA,IAAI,MAAM,MAAM,CAAC,UAAU,KAAK,UAAU;QACxC,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,qBAAqB,CAAA,QAAS,MAAM,YAAY;AACpD,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;IAAE,kLAAA,CAAA,4BAAyB;CAAC,EAAE,qJAAA,CAAA,eAAY;AAC1H,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAoB;CAAgB,EAAE,CAAC,mBAAmB;IAChG,IAAI,qBAAqB,MAAM;QAC7B,OAAO;IACT;IACA,OAAO,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB,WAAW,EAAE,WAAW;AACnE;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAoB;CAAgB,EAAE,CAAC,mBAAmB;IACvG,IAAI,qBAAqB,MAAM;QAC7B,OAAO;IACT;IACA,OAAO,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB,WAAW,EAAE,WAAW,YAAY;AAC/E;AACA,IAAI,wBAAwB,CAAA;IAC1B,IAAI,gBAAgB,MAAM;QACxB,OAAO;YAAC;YAAG;SAAE;IACf;IACA,IAAI,EACF,UAAU,EACV,QAAQ,EACT,GAAG;IACJ,OAAO;QAAC;QAAY;KAAS;AAC/B;AACO,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAmB,EAAE;AAChE,IAAI,mCAAmC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAiB;CAAqB,EAAE,iMAAA,CAAA,8BAA2B;AAC1H,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAiB;IAAmB;CAAkB,EAAE,CAAC,WAAW,aAAa;IAClI,IAAI,aAAa,QAAQ,eAAe,QAAQ,eAAe,MAAM;QACnE,OAAO;IACT;IACA,OAAO;QAAC;QAAa;KAAY;AACnC;AACO,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAkB;CAAsB,EAAE,iMAAA,CAAA,8BAA2B;AAC7H,IAAI,qBAAqB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAoB;IAAmB;IAAmB,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;CAAC,EAAE,CAAC,QAAQ,cAAc,aAAa,aAAa,OAAO;IACzN,IAAI,WAAW,aAAa,WAAW,YAAY,gBAAgB,QAAQ,eAAe,QAAQ,eAAe,MAAM;QACrH,OAAO;IACT;IACA,IAAI,EACF,EAAE,EACF,EAAE,EACF,UAAU,EACV,QAAQ,EACT,GAAG;IACJ,OAAO;QACL,IAAI,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,OAAO,QAAQ;QACvC,IAAI,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,QAAQ,SAAS;QACzC;QACA;QACA;QACA;QACA,WAAW;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/pickAxisType.js"], "sourcesContent": ["export var pickAxisType = (_state, axisType) => axisType;"], "names": [], "mappings": ";;;AAAO,IAAI,eAAe,CAAC,QAAQ,WAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/pickAxisId.js"], "sourcesContent": ["export var pickAxisId = (_state, _axisType, axisId) => axisId;"], "names": [], "mappings": ";;;AAAO,IAAI,aAAa,CAAC,QAAQ,WAAW,SAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/axisSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport range from 'es-toolkit/compat/range';\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { checkDomainOfScale, getDomainOfStackGroups, getStackedData, getValueByDataKey, isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes, selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { isWellFormedNumberDomain, numericalDomainSpecifiedWithoutRequiringData, parseNumericalUserDomain } from '../../util/isDomainSpecifiedByUser';\nimport { getPercentValue, hasDuplicate, isNan, isNumber, isNumOrStr, mathSign, upperFirst } from '../../util/DataUtils';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getNiceTickValues, getTickValuesFixedDomain } from '../../util/scale';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBrushDimensions, selectBrushSettings } from './brushSelectors';\nimport { selectBarCategoryGap, selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { selectAngleAxis, selectAngleAxisRange, selectRadiusAxis, selectRadiusAxisRange } from './polarAxisSelectors';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nvar defaultNumericDomain = [0, 'auto'];\n\n/**\n * angle, radius, X, Y, and Z axes all have domain and range and scale and associated settings\n */\n\n/**\n * X and Y axes have ticks. Z axis is never displayed and so it lacks ticks\n * and tick settings.\n */\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitXAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: undefined,\n  height: 30,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'bottom',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectXAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.xAxis[axisId];\n  if (axis == null) {\n    return implicitXAxis;\n  }\n  return axis;\n};\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitYAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: defaultNumericDomain,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'left',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined,\n  width: DEFAULT_Y_AXIS_WIDTH\n};\nexport var selectYAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.yAxis[axisId];\n  if (axis == null) {\n    return implicitYAxis;\n  }\n  return axis;\n};\nexport var implicitZAxis = {\n  domain: [0, 'auto'],\n  includeHidden: false,\n  reversed: false,\n  allowDataOverflow: false,\n  allowDuplicatedCategory: false,\n  dataKey: undefined,\n  id: 0,\n  name: '',\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number',\n  unit: ''\n};\nexport var selectZAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.zAxis[axisId];\n  if (axis == null) {\n    return implicitZAxis;\n  }\n  return axis;\n};\nexport var selectBaseAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'zAxis':\n      {\n        return selectZAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\nvar selectCartesianAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * Selects either an X or Y axis. Doesn't work with Z axis - for that, instead use selectBaseAxis.\n * @param state Root state\n * @param axisType xAxis | yAxis\n * @param axisId xAxisId | yAxisId\n * @returns axis settings object\n */\nexport var selectAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * @param state RechartsRootState\n * @return boolean true if there is at least one Bar or RadialBar\n */\nexport var selectHasBar = state => state.graphicalItems.countOfBars > 0;\n\n/**\n * Filters CartesianGraphicalItemSettings by the relevant axis ID\n * @param axisType 'xAxis' | 'yAxis' | 'zAxis' | 'radiusAxis' | 'angleAxis'\n * @param axisId from props, defaults to 0\n *\n * @returns Predicate function that return true for CartesianGraphicalItemSettings that are relevant to the specified axis\n */\nexport function itemAxisPredicate(axisType, axisId) {\n  return item => {\n    switch (axisType) {\n      case 'xAxis':\n        // This is sensitive to the data type, as 0 !== '0'. I wonder if we should be more flexible. How does 2.x branch behave? TODO write test for that\n        return 'xAxisId' in item && item.xAxisId === axisId;\n      case 'yAxis':\n        return 'yAxisId' in item && item.yAxisId === axisId;\n      case 'zAxis':\n        return 'zAxisId' in item && item.zAxisId === axisId;\n      case 'angleAxis':\n        return 'angleAxisId' in item && item.angleAxisId === axisId;\n      case 'radiusAxis':\n        return 'radiusAxisId' in item && item.radiusAxisId === axisId;\n      default:\n        return false;\n    }\n  };\n}\nexport var selectUnfilteredCartesianItems = state => state.graphicalItems.cartesianItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var combineGraphicalItemsSettings = (graphicalItems, axisSettings, axisPredicate) => graphicalItems.filter(axisPredicate).filter(item => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.includeHidden) === true) {\n    return true;\n  }\n  return !item.hide;\n});\nexport var selectCartesianItemsSettings = createSelector([selectUnfilteredCartesianItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nexport var filterGraphicalNotStackedItems = cartesianItems => cartesianItems.filter(item => item.stackId === undefined);\nvar selectCartesianItemsSettingsExceptStacked = createSelector([selectCartesianItemsSettings], filterGraphicalNotStackedItems);\nexport var combineGraphicalItemsData = cartesianItems => cartesianItems.map(item => item.data).filter(Boolean).flat(1);\n\n/**\n * This is a \"cheap\" selector - it returns the data but doesn't iterate them, so it is not sensitive on the array length.\n * Also does not apply dataKey yet.\n * @param state RechartsRootState\n * @returns data defined on the chart graphical items, such as Line or Scatter or Pie, and filtered with appropriate dataKey\n */\nexport var selectCartesianGraphicalItemsData = createSelector([selectCartesianItemsSettings], combineGraphicalItemsData);\nexport var combineDisplayedData = (graphicalItemsData, _ref) => {\n  var {\n    chartData = [],\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (graphicalItemsData.length > 0) {\n    /*\n     * There is no slicing when data is defined on graphical items. Why?\n     * Because Brush ignores data defined on graphical items,\n     * and does not render.\n     * So Brush will never show up in a Scatter chart for example.\n     * This is something we will need to fix.\n     *\n     * Now, when the root chart data is not defined, the dataEndIndex is 0,\n     * which means the itemsData will be sliced to an empty array anyway.\n     * But that's an implementation detail, and we can fix that too.\n     *\n     * Also, in absence of Axis dataKey, we use the dataKey from each item, respectively.\n     * This is the usual pattern for numerical axis, that is the one where bars go up:\n     * users don't specify any dataKey by default and expect the axis to \"just match the data\".\n     */\n    return graphicalItemsData;\n  }\n  return chartData.slice(dataStartIndex, dataEndIndex + 1);\n};\n\n/**\n * This selector will return all data there is in the chart: graphical items, chart root, all together.\n * Useful for figuring out an axis domain (because that needs to know of everything),\n * not useful for rendering individual graphical elements (because they need to know which data is theirs and which is not).\n *\n * This function will discard the original indexes, so it is also not useful for anything that depends on ordering.\n */\nexport var selectDisplayedData = createSelector([selectCartesianGraphicalItemsData, selectChartDataWithIndexesIfNotInPanorama], combineDisplayedData);\nexport var combineAppliedValues = (data, axisSettings, items) => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey)\n    }));\n  }\n  if (items.length > 0) {\n    return items.map(item => item.dataKey).flatMap(dataKey => data.map(entry => ({\n      value: getValueByDataKey(entry, dataKey)\n    })));\n  }\n  return data.map(entry => ({\n    value: entry\n  }));\n};\n\n/**\n * This selector will return all values with the appropriate dataKey applied on them.\n * Which dataKey is appropriate depends on where it is defined.\n *\n * This is an expensive selector - it will iterate all data and compute their value using the provided dataKey.\n */\nexport var selectAllAppliedValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettings], combineAppliedValues);\nexport function isErrorBarRelevantForAxisType(axisType, errorBar) {\n  switch (axisType) {\n    case 'xAxis':\n      return errorBar.direction === 'x';\n    case 'yAxis':\n      return errorBar.direction === 'y';\n    default:\n      return false;\n  }\n}\n\n/**\n * This is type of \"error\" in chart. It is set by using ErrorBar, and it can represent confidence interval,\n * or gap in the data, or standard deviation, or quartiles in boxplot, or whiskers or whatever.\n *\n * We will internally represent it as a tuple of two numbers, where the first number is the lower bound and the second number is the upper bound.\n *\n * It is also true that the first number should be lower than or equal to the associated \"main value\",\n * and the second number should be higher than or equal to the associated \"main value\".\n */\n\nexport function fromMainValueToError(value) {\n  if (isNumber(value) && Number.isFinite(value)) {\n    return [value, value];\n  }\n  if (Array.isArray(value)) {\n    var minError = Math.min(...value);\n    var maxError = Math.max(...value);\n    if (!isNan(minError) && !isNan(maxError) && Number.isFinite(minError) && Number.isFinite(maxError)) {\n      return [minError, maxError];\n    }\n  }\n  return undefined;\n}\nfunction onlyAllowNumbers(data) {\n  return data.filter(v => isNumOrStr(v) || v instanceof Date).map(Number).filter(n => isNan(n) === false);\n}\n\n/**\n * @param entry One item in the 'data' array. Could be anything really - this is defined externally. This is the raw, before dataKey application\n * @param appliedValue This is the result of applying the 'main' dataKey on the `entry`.\n * @param relevantErrorBars Error bars that are relevant for the current axis and layout and all that.\n * @return either undefined or an array of ErrorValue\n */\nexport function getErrorDomainByDataKey(entry, appliedValue, relevantErrorBars) {\n  if (!relevantErrorBars || typeof appliedValue !== 'number' || isNan(appliedValue)) {\n    return [];\n  }\n  if (!relevantErrorBars.length) {\n    return [];\n  }\n  return onlyAllowNumbers(relevantErrorBars.flatMap(eb => {\n    var errorValue = getValueByDataKey(entry, eb.dataKey);\n    var lowBound, highBound;\n    if (Array.isArray(errorValue)) {\n      [lowBound, highBound] = errorValue;\n    } else {\n      lowBound = highBound = errorValue;\n    }\n    if (!isWellBehavedNumber(lowBound) || !isWellBehavedNumber(highBound)) {\n      return undefined;\n    }\n    return [appliedValue - lowBound, appliedValue + highBound];\n  }));\n}\nexport var combineStackGroups = (displayedData, items, stackOffsetType) => {\n  var initialItemsGroups = {};\n  var itemsGroup = items.reduce((acc, item) => {\n    if (item.stackId == null) {\n      return acc;\n    }\n    if (acc[item.stackId] == null) {\n      acc[item.stackId] = [];\n    }\n    acc[item.stackId].push(item);\n    return acc;\n  }, initialItemsGroups);\n  return Object.fromEntries(Object.entries(itemsGroup).map(_ref2 => {\n    var [stackId, graphicalItems] = _ref2;\n    var dataKeys = graphicalItems.map(i => i.dataKey);\n    return [stackId, {\n      // @ts-expect-error getStackedData requires that the input is array of objects, Recharts does not test for that\n      stackedData: getStackedData(displayedData, dataKeys, stackOffsetType),\n      graphicalItems\n    }];\n  }));\n};\n/**\n * Stack groups are groups of graphical items that stack on each other.\n * Stack is a function of axis type (X, Y), axis ID, and stack ID.\n * Graphical items that do not have a stack ID are not going to be present in stack groups.\n */\nexport var selectStackGroups = createSelector([selectDisplayedData, selectCartesianItemsSettings, selectStackOffsetType], combineStackGroups);\nexport var combineDomainOfStackGroups = (stackGroups, _ref3, axisType) => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (axisType === 'zAxis') {\n    // ZAxis ignores stacks\n    return undefined;\n  }\n  var domainOfStackGroups = getDomainOfStackGroups(stackGroups, dataStartIndex, dataEndIndex);\n  if (domainOfStackGroups != null && domainOfStackGroups[0] === 0 && domainOfStackGroups[1] === 0) {\n    return undefined;\n  }\n  return domainOfStackGroups;\n};\nexport var selectDomainOfStackGroups = createSelector([selectStackGroups, selectChartDataWithIndexes, pickAxisType], combineDomainOfStackGroups);\nexport var combineAppliedNumericalValuesIncludingErrorValues = (data, axisSettings, items, axisType) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _item$errorBars, _axisSettings$dataKey;\n        var relevantErrorBars = (_item$errorBars = item.errorBars) === null || _item$errorBars === void 0 ? void 0 : _item$errorBars.filter(errorBar => isErrorBarRelevantForAxisType(axisType, errorBar));\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: getErrorDomainByDataKey(entry, valueByDataKey, relevantErrorBars)\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n};\nexport var selectAllAppliedNumericalValuesIncludingErrorValues = createSelector(selectDisplayedData, selectBaseAxis, selectCartesianItemsSettingsExceptStacked, pickAxisType, combineAppliedNumericalValuesIncludingErrorValues);\nfunction onlyAllowNumbersAndStringsAndDates(item) {\n  var {\n    value\n  } = item;\n  if (isNumOrStr(value) || value instanceof Date) {\n    return value;\n  }\n  return undefined;\n}\nvar computeNumericalDomain = dataWithErrorDomains => {\n  var allDataSquished = dataWithErrorDomains\n  // This flatMap has to be flat because we're creating a new array in the return value\n  .flatMap(d => [d.value, d.errorDomain])\n  // This flat is needed because a) errorDomain is an array, and b) value may be a number, or it may be a range (for Area, for example)\n  .flat(1);\n  var onlyNumbers = onlyAllowNumbers(allDataSquished);\n  if (onlyNumbers.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...onlyNumbers), Math.max(...onlyNumbers)];\n};\nvar computeDomainOfTypeCategory = (allDataSquished, axisSettings, isCategorical) => {\n  var categoricalDomain = allDataSquished.map(onlyAllowNumbersAndStringsAndDates).filter(v => v != null);\n  if (isCategorical && (axisSettings.dataKey == null || axisSettings.allowDuplicatedCategory && hasDuplicate(categoricalDomain))) {\n    /*\n     * 1. In an absence of dataKey, Recharts will use array indexes as its categorical domain\n     * 2. When category axis has duplicated text, serial numbers are used to generate scale\n     */\n    return range(0, allDataSquished.length);\n  }\n  if (axisSettings.allowDuplicatedCategory) {\n    return categoricalDomain;\n  }\n  return Array.from(new Set(categoricalDomain));\n};\nexport var getDomainDefinition = axisSettings => {\n  var _axisSettings$domain;\n  if (axisSettings == null || !('domain' in axisSettings)) {\n    return defaultNumericDomain;\n  }\n  if (axisSettings.domain != null) {\n    return axisSettings.domain;\n  }\n  if (axisSettings.ticks != null) {\n    if (axisSettings.type === 'number') {\n      var allValues = onlyAllowNumbers(axisSettings.ticks);\n      return [Math.min(...allValues), Math.max(...allValues)];\n    }\n    if (axisSettings.type === 'category') {\n      return axisSettings.ticks.map(String);\n    }\n  }\n  return (_axisSettings$domain = axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.domain) !== null && _axisSettings$domain !== void 0 ? _axisSettings$domain : defaultNumericDomain;\n};\nexport var mergeDomains = function mergeDomains() {\n  for (var _len = arguments.length, domains = new Array(_len), _key = 0; _key < _len; _key++) {\n    domains[_key] = arguments[_key];\n  }\n  var allDomains = domains.filter(Boolean);\n  if (allDomains.length === 0) {\n    return undefined;\n  }\n  var allValues = allDomains.flat();\n  var min = Math.min(...allValues);\n  var max = Math.max(...allValues);\n  return [min, max];\n};\nexport var selectReferenceDots = state => state.referenceElements.dots;\nexport var filterReferenceElements = (elements, axisType, axisId) => {\n  return elements.filter(el => el.ifOverflow === 'extendDomain').filter(el => {\n    if (axisType === 'xAxis') {\n      return el.xAxisId === axisId;\n    }\n    return el.yAxisId === axisId;\n  });\n};\nexport var selectReferenceDotsByAxis = createSelector([selectReferenceDots, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceAreas = state => state.referenceElements.areas;\nexport var selectReferenceAreasByAxis = createSelector([selectReferenceAreas, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceLines = state => state.referenceElements.lines;\nexport var selectReferenceLinesByAxis = createSelector([selectReferenceLines, pickAxisType, pickAxisId], filterReferenceElements);\nexport var combineDotsDomain = (dots, axisType) => {\n  var allCoords = onlyAllowNumbers(dots.map(dot => axisType === 'xAxis' ? dot.x : dot.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceDotsDomain = createSelector(selectReferenceDotsByAxis, pickAxisType, combineDotsDomain);\nexport var combineAreasDomain = (areas, axisType) => {\n  var allCoords = onlyAllowNumbers(areas.flatMap(area => [axisType === 'xAxis' ? area.x1 : area.y1, axisType === 'xAxis' ? area.x2 : area.y2]));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceAreasDomain = createSelector([selectReferenceAreasByAxis, pickAxisType], combineAreasDomain);\nexport var combineLinesDomain = (lines, axisType) => {\n  var allCoords = onlyAllowNumbers(lines.map(line => axisType === 'xAxis' ? line.x : line.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceLinesDomain = createSelector(selectReferenceLinesByAxis, pickAxisType, combineLinesDomain);\nvar selectReferenceElementsDomain = createSelector(selectReferenceDotsDomain, selectReferenceLinesDomain, selectReferenceAreasDomain, (dotsDomain, linesDomain, areasDomain) => {\n  return mergeDomains(dotsDomain, areasDomain, linesDomain);\n});\nexport var selectDomainDefinition = createSelector([selectBaseAxis], getDomainDefinition);\nexport var combineNumericalDomain = (axisSettings, domainDefinition, domainOfStackGroups, allDataWithErrorDomains, referenceElementsDomain) => {\n  var domainFromUserPreference = numericalDomainSpecifiedWithoutRequiringData(domainDefinition, axisSettings.allowDataOverflow);\n  if (domainFromUserPreference != null) {\n    // We're done! No need to compute anything else.\n    return domainFromUserPreference;\n  }\n  return parseNumericalUserDomain(domainDefinition, mergeDomains(domainOfStackGroups, referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains)), axisSettings.allowDataOverflow);\n};\nvar selectNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, selectDomainOfStackGroups, selectAllAppliedNumericalValuesIncludingErrorValues, selectReferenceElementsDomain], combineNumericalDomain);\n\n/**\n * Expand by design maps everything between 0 and 1,\n * there is nothing to compute.\n * See https://d3js.org/d3-shape/stack#stack-offsets\n */\nvar expandDomain = [0, 1];\nexport var combineAxisDomain = (axisSettings, layout, displayedData, allAppliedValues, stackOffsetType, axisType, numericalDomain) => {\n  if (axisSettings == null || displayedData == null || displayedData.length === 0) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    type\n  } = axisSettings;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && dataKey == null) {\n    return range(0, displayedData.length);\n  }\n  if (type === 'category') {\n    return computeDomainOfTypeCategory(allAppliedValues, axisSettings, isCategorical);\n  }\n  if (stackOffsetType === 'expand') {\n    return expandDomain;\n  }\n  return numericalDomain;\n};\nexport var selectAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectDisplayedData, selectAllAppliedValues, selectStackOffsetType, pickAxisType, selectNumericalDomain], combineAxisDomain);\nexport var combineRealScaleType = (axisConfig, layout, hasBar, chartType, axisType) => {\n  if (axisConfig == null) {\n    return undefined;\n  }\n  var {\n    scale,\n    type\n  } = axisConfig;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return 'band';\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return 'linear';\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return 'point';\n    }\n    if (type === 'category') {\n      return 'band';\n    }\n    return 'linear';\n  }\n  if (typeof scale === 'string') {\n    var name = \"scale\".concat(upperFirst(scale));\n    return name in d3Scales ? name : 'point';\n  }\n  return undefined;\n};\nexport var selectRealScaleType = createSelector([selectBaseAxis, selectChartLayout, selectHasBar, selectChartName, pickAxisType], combineRealScaleType);\nfunction getD3ScaleFromType(realScaleType) {\n  if (realScaleType == null) {\n    return undefined;\n  }\n  if (realScaleType in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[realScaleType]();\n  }\n  var name = \"scale\".concat(upperFirst(realScaleType));\n  if (name in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[name]();\n  }\n  return undefined;\n}\nexport function combineScaleFunction(axis, realScaleType, axisDomain, axisRange) {\n  if (axisDomain == null || axisRange == null) {\n    return undefined;\n  }\n  if (typeof axis.scale === 'function') {\n    // @ts-expect-error we're going to assume here that if axis.scale is a function then it is a d3Scale function\n    return axis.scale.copy().domain(axisDomain).range(axisRange);\n  }\n  var d3ScaleFunction = getD3ScaleFromType(realScaleType);\n  if (d3ScaleFunction == null) {\n    return undefined;\n  }\n  var scale = d3ScaleFunction.domain(axisDomain).range(axisRange);\n  // I don't like this function because it mutates the scale. We should come up with a way to compute the domain up front.\n  checkDomainOfScale(scale);\n  return scale;\n}\nexport var combineNiceTicks = (axisDomain, axisSettings, realScaleType) => {\n  var domainDefinition = getDomainDefinition(axisSettings);\n  if (realScaleType !== 'auto' && realScaleType !== 'linear') {\n    return undefined;\n  }\n  if (axisSettings != null && axisSettings.tickCount && Array.isArray(domainDefinition) && (domainDefinition[0] === 'auto' || domainDefinition[1] === 'auto') && isWellFormedNumberDomain(axisDomain)) {\n    return getNiceTickValues(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  if (axisSettings != null && axisSettings.tickCount && axisSettings.type === 'number' && isWellFormedNumberDomain(axisDomain)) {\n    return getTickValuesFixedDomain(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  return undefined;\n};\nexport var selectNiceTicks = createSelector([selectAxisDomain, selectAxisSettings, selectRealScaleType], combineNiceTicks);\nexport var combineAxisDomainWithNiceTicks = (axisSettings, domain, niceTicks, axisType) => {\n  if (\n  /*\n   * Angle axis for some reason uses nice ticks when rendering axis tick labels,\n   * but doesn't use nice ticks for extending domain like all the other axes do.\n   * Not really sure why? Is there a good reason,\n   * or is it just because someone added support for nice ticks to the other axes and forgot this one?\n   */\n  axisType !== 'angleAxis' && (axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.type) === 'number' && isWellFormedNumberDomain(domain) && Array.isArray(niceTicks) && niceTicks.length > 0) {\n    var minFromDomain = domain[0];\n    var minFromTicks = niceTicks[0];\n    var maxFromDomain = domain[1];\n    var maxFromTicks = niceTicks[niceTicks.length - 1];\n    return [Math.min(minFromDomain, minFromTicks), Math.max(maxFromDomain, maxFromTicks)];\n  }\n  return domain;\n};\nexport var selectAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectAxisDomain, selectNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);\n\n/**\n * Returns the smallest gap, between two numbers in the data, as a ratio of the whole range (max - min).\n * Ignores domain provided by user and only considers domain from data.\n *\n * The result is a number between 0 and 1.\n */\nexport var selectSmallestDistanceBetweenValues = createSelector(selectAllAppliedValues, selectBaseAxis, (allDataSquished, axisSettings) => {\n  if (!axisSettings || axisSettings.type !== 'number') {\n    return undefined;\n  }\n  var smallestDistanceBetweenValues = Infinity;\n  var sortedValues = Array.from(onlyAllowNumbers(allDataSquished.map(d => d.value))).sort((a, b) => a - b);\n  if (sortedValues.length < 2) {\n    return Infinity;\n  }\n  var diff = sortedValues[sortedValues.length - 1] - sortedValues[0];\n  if (diff === 0) {\n    return Infinity;\n  }\n  // Only do n - 1 distance calculations because there's only n - 1 distances between n values.\n  for (var i = 0; i < sortedValues.length - 1; i++) {\n    var distance = sortedValues[i + 1] - sortedValues[i];\n    smallestDistanceBetweenValues = Math.min(smallestDistanceBetweenValues, distance);\n  }\n  return smallestDistanceBetweenValues / diff;\n});\nvar selectCalculatedPadding = createSelector(selectSmallestDistanceBetweenValues, selectChartLayout, selectBarCategoryGap, selectChartOffsetInternal, (_1, _2, _3, padding) => padding, (smallestDistanceInPercent, layout, barCategoryGap, offset, padding) => {\n  if (!isWellBehavedNumber(smallestDistanceInPercent)) {\n    return 0;\n  }\n  var rangeWidth = layout === 'vertical' ? offset.height : offset.width;\n  if (padding === 'gap') {\n    return smallestDistanceInPercent * rangeWidth / 2;\n  }\n  if (padding === 'no-gap') {\n    var gap = getPercentValue(barCategoryGap, smallestDistanceInPercent * rangeWidth);\n    var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n    return halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n  }\n  return 0;\n});\nexport var selectCalculatedXAxisPadding = (state, axisId) => {\n  var xAxisSettings = selectXAxisSettings(state, axisId);\n  if (xAxisSettings == null || typeof xAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'xAxis', axisId, xAxisSettings.padding);\n};\nexport var selectCalculatedYAxisPadding = (state, axisId) => {\n  var yAxisSettings = selectYAxisSettings(state, axisId);\n  if (yAxisSettings == null || typeof yAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'yAxis', axisId, yAxisSettings.padding);\n};\nvar selectXAxisPadding = createSelector(selectXAxisSettings, selectCalculatedXAxisPadding, (xAxisSettings, calculated) => {\n  var _padding$left, _padding$right;\n  if (xAxisSettings == null) {\n    return {\n      left: 0,\n      right: 0\n    };\n  }\n  var {\n    padding\n  } = xAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      left: calculated,\n      right: calculated\n    };\n  }\n  return {\n    left: ((_padding$left = padding.left) !== null && _padding$left !== void 0 ? _padding$left : 0) + calculated,\n    right: ((_padding$right = padding.right) !== null && _padding$right !== void 0 ? _padding$right : 0) + calculated\n  };\n});\nvar selectYAxisPadding = createSelector(selectYAxisSettings, selectCalculatedYAxisPadding, (yAxisSettings, calculated) => {\n  var _padding$top, _padding$bottom;\n  if (yAxisSettings == null) {\n    return {\n      top: 0,\n      bottom: 0\n    };\n  }\n  var {\n    padding\n  } = yAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      top: calculated,\n      bottom: calculated\n    };\n  }\n  return {\n    top: ((_padding$top = padding.top) !== null && _padding$top !== void 0 ? _padding$top : 0) + calculated,\n    bottom: ((_padding$bottom = padding.bottom) !== null && _padding$bottom !== void 0 ? _padding$bottom : 0) + calculated\n  };\n});\nexport var combineXAxisRange = createSelector([selectChartOffsetInternal, selectXAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, padding, brushDimensions, _ref4, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref4;\n  if (isPanorama) {\n    return [brushPadding.left, brushDimensions.width - brushPadding.right];\n  }\n  return [offset.left + padding.left, offset.left + offset.width - padding.right];\n});\nexport var combineYAxisRange = createSelector([selectChartOffsetInternal, selectChartLayout, selectYAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, layout, padding, brushDimensions, _ref5, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref5;\n  if (isPanorama) {\n    return [brushDimensions.height - brushPadding.bottom, brushPadding.top];\n  }\n  if (layout === 'horizontal') {\n    return [offset.top + offset.height - padding.bottom, offset.top + padding.top];\n  }\n  return [offset.top + padding.top, offset.top + offset.height - padding.bottom];\n});\nexport var selectAxisRange = (state, axisType, axisId, isPanorama) => {\n  var _selectZAxisSettings;\n  switch (axisType) {\n    case 'xAxis':\n      return combineXAxisRange(state, axisId, isPanorama);\n    case 'yAxis':\n      return combineYAxisRange(state, axisId, isPanorama);\n    case 'zAxis':\n      return (_selectZAxisSettings = selectZAxisSettings(state, axisId)) === null || _selectZAxisSettings === void 0 ? void 0 : _selectZAxisSettings.range;\n    case 'angleAxis':\n      return selectAngleAxisRange(state);\n    case 'radiusAxis':\n      return selectRadiusAxisRange(state, axisId);\n    default:\n      return undefined;\n  }\n};\nexport var selectAxisRangeWithReverse = createSelector([selectBaseAxis, selectAxisRange], combineAxisRangeWithReverse);\nexport var selectAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomainIncludingNiceTicks, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectErrorBarsSettings = createSelector(selectCartesianItemsSettings, pickAxisType, (items, axisType) => {\n  return items.flatMap(item => {\n    var _item$errorBars2;\n    return (_item$errorBars2 = item.errorBars) !== null && _item$errorBars2 !== void 0 ? _item$errorBars2 : [];\n  }).filter(e => {\n    return isErrorBarRelevantForAxisType(axisType, e);\n  });\n});\nfunction compareIds(a, b) {\n  if (a.id < b.id) {\n    return -1;\n  }\n  if (a.id > b.id) {\n    return 1;\n  }\n  return 0;\n}\nvar pickAxisOrientation = (_state, orientation) => orientation;\nvar pickMirror = (_state, _orientation, mirror) => mirror;\nvar selectAllXAxesWithOffsetType = createSelector(selectAllXAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar selectAllYAxesWithOffsetType = createSelector(selectAllYAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar getXAxisSize = (offset, axisSettings) => {\n  return {\n    width: offset.width,\n    height: axisSettings.height\n  };\n};\nvar getYAxisSize = (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n};\nexport var selectXAxisSize = createSelector(selectChartOffsetInternal, selectXAxisSettings, getXAxisSize);\nvar combineXAxisPositionStartingPoint = (offset, orientation, chartHeight) => {\n  switch (orientation) {\n    case 'top':\n      return offset.top;\n    case 'bottom':\n      return chartHeight - offset.bottom;\n    default:\n      return 0;\n  }\n};\nvar combineYAxisPositionStartingPoint = (offset, orientation, chartWidth) => {\n  switch (orientation) {\n    case 'left':\n      return offset.left;\n    case 'right':\n      return chartWidth - offset.right;\n    default:\n      return 0;\n  }\n};\nexport var selectAllXAxesOffsetSteps = createSelector(selectChartHeight, selectChartOffsetInternal, selectAllXAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartHeight, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getXAxisSize(offset, axis);\n    if (position == null) {\n      position = combineXAxisPositionStartingPoint(offset, orientation, chartHeight);\n    }\n    var needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.height;\n    position += (needSpace ? -1 : 1) * axisSize.height;\n  });\n  return steps;\n});\nexport var selectAllYAxesOffsetSteps = createSelector(selectChartWidth, selectChartOffsetInternal, selectAllYAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartWidth, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getYAxisSize(offset, axis);\n    if (position == null) {\n      position = combineYAxisPositionStartingPoint(offset, orientation, chartWidth);\n    }\n    var needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.width;\n    position += (needSpace ? -1 : 1) * axisSize.width;\n  });\n  return steps;\n});\nexport var selectXAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectXAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllXAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: offset.left,\n      y: 0\n    };\n  }\n  return {\n    x: offset.left,\n    y: stepOfThisAxis\n  };\n};\nexport var selectYAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectYAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllYAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: 0,\n      y: offset.top\n    };\n  }\n  return {\n    x: stepOfThisAxis,\n    y: offset.top\n  };\n};\nexport var selectYAxisSize = createSelector(selectChartOffsetInternal, selectYAxisSettings, (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n});\nexport var selectCartesianAxisSize = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSize(state, axisId).width;\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSize(state, axisId).height;\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n};\nexport var combineDuplicateDomain = (chartLayout, appliedValues, axis, axisType) => {\n  if (axis == null) {\n    return undefined;\n  }\n  var {\n    allowDuplicatedCategory,\n    type,\n    dataKey\n  } = axis;\n  var isCategorical = isCategoricalAxis(chartLayout, axisType);\n  var allData = appliedValues.map(av => av.value);\n  if (dataKey && isCategorical && type === 'category' && allowDuplicatedCategory && hasDuplicate(allData)) {\n    return allData;\n  }\n  return undefined;\n};\nexport var selectDuplicateDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectBaseAxis, pickAxisType], combineDuplicateDomain);\nexport var combineCategoricalDomain = (layout, appliedValues, axis, axisType) => {\n  if (axis == null || axis.dataKey == null) {\n    return undefined;\n  }\n  var {\n    type,\n    scale\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && (type === 'number' || scale !== 'auto')) {\n    return appliedValues.map(d => d.value);\n  }\n  return undefined;\n};\nexport var selectCategoricalDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectAxisPropsNeededForCartesianGridTicksGenerator = createSelector([selectChartLayout, selectCartesianAxisSettings, selectRealScaleType, selectAxisScale, selectDuplicateDomain, selectCategoricalDomain, selectAxisRange, selectNiceTicks, pickAxisType], (layout, axis, realScaleType, scale, duplicateDomain, categoricalDomain, axisRange, niceTicks, axisType) => {\n  if (axis == null) {\n    return null;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  return {\n    angle: axis.angle,\n    interval: axis.interval,\n    minTickGap: axis.minTickGap,\n    orientation: axis.orientation,\n    tick: axis.tick,\n    tickCount: axis.tickCount,\n    tickFormatter: axis.tickFormatter,\n    ticks: axis.ticks,\n    type: axis.type,\n    unit: axis.unit,\n    axisType,\n    categoricalDomain,\n    duplicateDomain,\n    isCategorical,\n    niceTicks,\n    range: axisRange,\n    realScaleType,\n    scale\n  };\n});\nexport var combineAxisTicks = (layout, axis, realScaleType, scale, niceTicks, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    type,\n    ticks,\n    tickCount\n  } = axis;\n\n  // This is testing for `scaleBand` but for band axis the type is reported as `band` so this looks like a dead code with a workaround elsewhere?\n  var offsetForBand = realScaleType === 'scaleBand' && typeof scale.bandwidth === 'function' ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && axisRange != null && axisRange.length >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  var ticksOrNiceTicks = ticks || niceTicks;\n  if (ticksOrNiceTicks) {\n    var result = ticksOrNiceTicks.map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        index,\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfAxis = createSelector([selectChartLayout, selectAxisSettings, selectRealScaleType, selectAxisScale, selectNiceTicks, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var combineGraphicalItemTicks = (layout, axis, scale, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null || axisRange == null || axisRange[0] === axisRange[1]) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    tickCount\n  } = axis;\n  var offset = 0;\n  offset = axisType === 'angleAxis' && (axisRange === null || axisRange === void 0 ? void 0 : axisRange.length) >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfGraphicalItem = createSelector([selectChartLayout, selectAxisSettings, selectAxisScale, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineGraphicalItemTicks);\nexport var selectAxisWithScale = createSelector(selectBaseAxis, selectAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\nvar selectZAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomain, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectZAxisWithScale = createSelector((state, _axisType, axisId) => selectZAxisSettings(state, axisId), selectZAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\n\n/**\n * We are also going to need to implement polar chart directions if we want to support keyboard controls for those.\n */\n\nexport var selectChartDirection = createSelector([selectChartLayout, selectAllXAxes, selectAllYAxes], (layout, allXAxes, allYAxes) => {\n  switch (layout) {\n    case 'horizontal':\n      {\n        return allXAxes.some(axis => axis.reversed) ? 'right-to-left' : 'left-to-right';\n      }\n    case 'vertical':\n      {\n        return allYAxes.some(axis => axis.reversed) ? 'bottom-to-top' : 'top-to-bottom';\n      }\n    // TODO: make this better. For now, right arrow triggers \"forward\", left arrow \"back\"\n    // however, the tooltip moves an unintuitive direction because of how the indices are rendered\n    case 'centric':\n    case 'radial':\n      {\n        return 'left-to-right';\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxBA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;;;;;;;;;;;;;;;AAqBvT,IAAI,uBAAuB;IAAC;IAAG;CAAO;AAgB/B,IAAI,gBAAgB;IACzB,mBAAmB;IACnB,eAAe;IACf,yBAAyB;IACzB,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,eAAe;IACf,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,aAAa;IACb,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,UAAU;IACV,OAAO;IACP,MAAM;IACN,WAAW;IACX,eAAe;IACf,OAAO;IACP,MAAM;IACN,MAAM;AACR;AACO,IAAI,sBAAsB,CAAC,OAAO;IACvC,IAAI,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO;IAC5C,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,OAAO;AACT;AAOO,IAAI,gBAAgB;IACzB,mBAAmB;IACnB,eAAe;IACf,yBAAyB;IACzB,OAAO;IACP,SAAS;IACT,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,eAAe;IACf,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,aAAa;IACb,SAAS;QACP,KAAK;QACL,QAAQ;IACV;IACA,UAAU;IACV,OAAO;IACP,MAAM;IACN,WAAW;IACX,eAAe;IACf,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO,oJAAA,CAAA,uBAAoB;AAC7B;AACO,IAAI,sBAAsB,CAAC,OAAO;IACvC,IAAI,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO;IAC5C,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,gBAAgB;IACzB,QAAQ;QAAC;QAAG;KAAO;IACnB,eAAe;IACf,UAAU;IACV,mBAAmB;IACnB,yBAAyB;IACzB,SAAS;IACT,IAAI;IACJ,MAAM;IACN,OAAO;QAAC;QAAI;KAAG;IACf,OAAO;IACP,MAAM;IACN,MAAM;AACR;AACO,IAAI,sBAAsB,CAAC,OAAO;IACvC,IAAI,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO;IAC5C,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,iBAAiB,CAAC,OAAO,UAAU;IAC5C,OAAQ;QACN,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,CAAA,GAAA,2KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAChC;QACF,KAAK;YACH;gBACE,OAAO,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YACjC;QACF;YACE,MAAM,IAAI,MAAM,yBAAyB,MAAM,CAAC;IACpD;AACF;AACA,IAAI,8BAA8B,CAAC,OAAO,UAAU;IAClD,OAAQ;QACN,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF;YACE,MAAM,IAAI,MAAM,yBAAyB,MAAM,CAAC;IACpD;AACF;AASO,IAAI,qBAAqB,CAAC,OAAO,UAAU;IAChD,OAAQ;QACN,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,oBAAoB,OAAO;YACpC;QACF,KAAK;YACH;gBACE,OAAO,CAAA,GAAA,2KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAChC;QACF,KAAK;YACH;gBACE,OAAO,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YACjC;QACF;YACE,MAAM,IAAI,MAAM,yBAAyB,MAAM,CAAC;IACpD;AACF;AAMO,IAAI,eAAe,CAAA,QAAS,MAAM,cAAc,CAAC,WAAW,GAAG;AAS/D,SAAS,kBAAkB,QAAQ,EAAE,MAAM;IAChD,OAAO,CAAA;QACL,OAAQ;YACN,KAAK;gBACH,iJAAiJ;gBACjJ,OAAO,aAAa,QAAQ,KAAK,OAAO,KAAK;YAC/C,KAAK;gBACH,OAAO,aAAa,QAAQ,KAAK,OAAO,KAAK;YAC/C,KAAK;gBACH,OAAO,aAAa,QAAQ,KAAK,OAAO,KAAK;YAC/C,KAAK;gBACH,OAAO,iBAAiB,QAAQ,KAAK,WAAW,KAAK;YACvD,KAAK;gBACH,OAAO,kBAAkB,QAAQ,KAAK,YAAY,KAAK;YACzD;gBACE,OAAO;QACX;IACF;AACF;AACO,IAAI,iCAAiC,CAAA,QAAS,MAAM,cAAc,CAAC,cAAc;AACxF,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,qKAAA,CAAA,eAAY;IAAE,mKAAA,CAAA,aAAU;CAAC,EAAE;AAC9D,IAAI,gCAAgC,CAAC,gBAAgB,cAAc,gBAAkB,eAAe,MAAM,CAAC,eAAe,MAAM,CAAC,CAAA;QACtI,IAAI,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,aAAa,MAAM,MAAM;YACrG,OAAO;QACT;QACA,OAAO,CAAC,KAAK,IAAI;IACnB;AACO,IAAI,+BAA+B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgC;IAAgB;CAAoB,EAAE;AACzH,IAAI,iCAAiC,CAAA,iBAAkB,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AAC7G,IAAI,4CAA4C,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA6B,EAAE;AACxF,IAAI,4BAA4B,CAAA,iBAAkB,eAAe,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,MAAM,CAAC,SAAS,IAAI,CAAC;AAQ7G,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA6B,EAAE;AACvF,IAAI,uBAAuB,CAAC,oBAAoB;IACrD,IAAI,EACF,YAAY,EAAE,EACd,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,mBAAmB,MAAM,GAAG,GAAG;QACjC;;;;;;;;;;;;;;KAcC,GACD,OAAO;IACT;IACA,OAAO,UAAU,KAAK,CAAC,gBAAgB,eAAe;AACxD;AASO,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmC,sKAAA,CAAA,4CAAyC;CAAC,EAAE;AACzH,IAAI,uBAAuB,CAAC,MAAM,cAAc;IACrD,IAAI,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,OAAO,KAAK,MAAM;QAC9F,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,aAAa,OAAO;YACrD,CAAC;IACH;IACA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,OAAO,CAAC,CAAA,UAAW,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC3E,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAClC,CAAC;IACH;IACA,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;YACxB,OAAO;QACT,CAAC;AACH;AAQO,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB;IAAgB;CAA6B,EAAE;AACjH,SAAS,8BAA8B,QAAQ,EAAE,QAAQ;IAC9D,OAAQ;QACN,KAAK;YACH,OAAO,SAAS,SAAS,KAAK;QAChC,KAAK;YACH,OAAO,SAAS,SAAS,KAAK;QAChC;YACE,OAAO;IACX;AACF;AAYO,SAAS,qBAAqB,KAAK;IACxC,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,OAAO,QAAQ,CAAC,QAAQ;QAC7C,OAAO;YAAC;YAAO;SAAM;IACvB;IACA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,IAAI,WAAW,KAAK,GAAG,IAAI;QAC3B,IAAI,WAAW,KAAK,GAAG,IAAI;QAC3B,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,CAAC,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,OAAO,QAAQ,CAAC,aAAa,OAAO,QAAQ,CAAC,WAAW;YAClG,OAAO;gBAAC;gBAAU;aAAS;QAC7B;IACF;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,IAAI;IAC5B,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,OAAO;AACnG;AAQO,SAAS,wBAAwB,KAAK,EAAE,YAAY,EAAE,iBAAiB;IAC5E,IAAI,CAAC,qBAAqB,OAAO,iBAAiB,YAAY,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,eAAe;QACjF,OAAO,EAAE;IACX;IACA,IAAI,CAAC,kBAAkB,MAAM,EAAE;QAC7B,OAAO,EAAE;IACX;IACA,OAAO,iBAAiB,kBAAkB,OAAO,CAAC,CAAA;QAChD,IAAI,aAAa,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,GAAG,OAAO;QACpD,IAAI,UAAU;QACd,IAAI,MAAM,OAAO,CAAC,aAAa;YAC7B,CAAC,UAAU,UAAU,GAAG;QAC1B,OAAO;YACL,WAAW,YAAY;QACzB;QACA,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;YACrE,OAAO;QACT;QACA,OAAO;YAAC,eAAe;YAAU,eAAe;SAAU;IAC5D;AACF;AACO,IAAI,qBAAqB,CAAC,eAAe,OAAO;IACrD,IAAI,qBAAqB,CAAC;IAC1B,IAAI,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK;QAClC,IAAI,KAAK,OAAO,IAAI,MAAM;YACxB,OAAO;QACT;QACA,IAAI,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,MAAM;YAC7B,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,EAAE;QACxB;QACA,GAAG,CAAC,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC;QACvB,OAAO;IACT,GAAG;IACH,OAAO,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAA;QACvD,IAAI,CAAC,SAAS,eAAe,GAAG;QAChC,IAAI,WAAW,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAChD,OAAO;YAAC;YAAS;gBACf,+GAA+G;gBAC/G,aAAa,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,UAAU;gBACrD;YACF;SAAE;IACJ;AACF;AAMO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB;IAA8B,2KAAA,CAAA,wBAAqB;CAAC,EAAE;AACnH,IAAI,6BAA6B,CAAC,aAAa,OAAO;IAC3D,IAAI,EACF,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,aAAa,SAAS;QACxB,uBAAuB;QACvB,OAAO;IACT;IACA,IAAI,sBAAsB,CAAA,GAAA,qJAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,gBAAgB;IAC9E,IAAI,uBAAuB,QAAQ,mBAAmB,CAAC,EAAE,KAAK,KAAK,mBAAmB,CAAC,EAAE,KAAK,GAAG;QAC/F,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB,sKAAA,CAAA,6BAA0B;IAAE,qKAAA,CAAA,eAAY;CAAC,EAAE;AAC9G,IAAI,oDAAoD,CAAC,MAAM,cAAc,OAAO;IACzF,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO,KAAK,OAAO,CAAC,CAAA;YAClB,OAAO,MAAM,OAAO,CAAC,CAAA;gBACnB,IAAI,iBAAiB;gBACrB,IAAI,oBAAoB,CAAC,kBAAkB,KAAK,SAAS,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,MAAM,CAAC,CAAA,WAAY,8BAA8B,UAAU;gBACxL,IAAI,iBAAiB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,CAAC,wBAAwB,aAAa,OAAO,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,KAAK,OAAO;gBAChL,OAAO;oBACL,OAAO;oBACP,aAAa,wBAAwB,OAAO,gBAAgB;gBAC9D;YACF;QACF,GAAG,MAAM,CAAC;IACZ;IACA,IAAI,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,OAAO,KAAK,MAAM;QAC9F,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,aAAa,OAAO;gBACnD,aAAa,EAAE;YACjB,CAAC;IACH;IACA,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;YACxB,OAAO;YACP,aAAa,EAAE;QACjB,CAAC;AACH;AACO,IAAI,sDAAsD,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB,gBAAgB,2CAA2C,qKAAA,CAAA,eAAY,EAAE;AAC9K,SAAS,mCAAmC,IAAI;IAC9C,IAAI,EACF,KAAK,EACN,GAAG;IACJ,IAAI,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,UAAU,iBAAiB,MAAM;QAC9C,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,yBAAyB,CAAA;IAC3B,IAAI,kBAAkB,oBACtB,qFAAqF;KACpF,OAAO,CAAC,CAAA,IAAK;YAAC,EAAE,KAAK;YAAE,EAAE,WAAW;SAAC,CACtC,qIAAqI;KACpI,IAAI,CAAC;IACN,IAAI,cAAc,iBAAiB;IACnC,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,OAAO;IACT;IACA,OAAO;QAAC,KAAK,GAAG,IAAI;QAAc,KAAK,GAAG,IAAI;KAAa;AAC7D;AACA,IAAI,8BAA8B,CAAC,iBAAiB,cAAc;IAChE,IAAI,oBAAoB,gBAAgB,GAAG,CAAC,oCAAoC,MAAM,CAAC,CAAA,IAAK,KAAK;IACjG,IAAI,iBAAiB,CAAC,aAAa,OAAO,IAAI,QAAQ,aAAa,uBAAuB,IAAI,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,GAAG;QAC9H;;;KAGC,GACD,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAK,AAAD,EAAE,GAAG,gBAAgB,MAAM;IACxC;IACA,IAAI,aAAa,uBAAuB,EAAE;QACxC,OAAO;IACT;IACA,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;AAC5B;AACO,IAAI,sBAAsB,CAAA;IAC/B,IAAI;IACJ,IAAI,gBAAgB,QAAQ,CAAC,CAAC,YAAY,YAAY,GAAG;QACvD,OAAO;IACT;IACA,IAAI,aAAa,MAAM,IAAI,MAAM;QAC/B,OAAO,aAAa,MAAM;IAC5B;IACA,IAAI,aAAa,KAAK,IAAI,MAAM;QAC9B,IAAI,aAAa,IAAI,KAAK,UAAU;YAClC,IAAI,YAAY,iBAAiB,aAAa,KAAK;YACnD,OAAO;gBAAC,KAAK,GAAG,IAAI;gBAAY,KAAK,GAAG,IAAI;aAAW;QACzD;QACA,IAAI,aAAa,IAAI,KAAK,YAAY;YACpC,OAAO,aAAa,KAAK,CAAC,GAAG,CAAC;QAChC;IACF;IACA,OAAO,CAAC,uBAAuB,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,MAAM,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB;AACvL;AACO,IAAI,eAAe,SAAS;IACjC,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,UAAU,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC1F,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IACjC;IACA,IAAI,aAAa,QAAQ,MAAM,CAAC;IAChC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IACA,IAAI,YAAY,WAAW,IAAI;IAC/B,IAAI,MAAM,KAAK,GAAG,IAAI;IACtB,IAAI,MAAM,KAAK,GAAG,IAAI;IACtB,OAAO;QAAC;QAAK;KAAI;AACnB;AACO,IAAI,sBAAsB,CAAA,QAAS,MAAM,iBAAiB,CAAC,IAAI;AAC/D,IAAI,0BAA0B,CAAC,UAAU,UAAU;IACxD,OAAO,SAAS,MAAM,CAAC,CAAA,KAAM,GAAG,UAAU,KAAK,gBAAgB,MAAM,CAAC,CAAA;QACpE,IAAI,aAAa,SAAS;YACxB,OAAO,GAAG,OAAO,KAAK;QACxB;QACA,OAAO,GAAG,OAAO,KAAK;IACxB;AACF;AACO,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB,qKAAA,CAAA,eAAY;IAAE,mKAAA,CAAA,aAAU;CAAC,EAAE;AAChG,IAAI,uBAAuB,CAAA,QAAS,MAAM,iBAAiB,CAAC,KAAK;AACjE,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAsB,qKAAA,CAAA,eAAY;IAAE,mKAAA,CAAA,aAAU;CAAC,EAAE;AAClG,IAAI,uBAAuB,CAAA,QAAS,MAAM,iBAAiB,CAAC,KAAK;AACjE,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAsB,qKAAA,CAAA,eAAY;IAAE,mKAAA,CAAA,aAAU;CAAC,EAAE;AAClG,IAAI,oBAAoB,CAAC,MAAM;IACpC,IAAI,YAAY,iBAAiB,KAAK,GAAG,CAAC,CAAA,MAAO,aAAa,UAAU,IAAI,CAAC,GAAG,IAAI,CAAC;IACrF,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO;IACT;IACA,OAAO;QAAC,KAAK,GAAG,IAAI;QAAY,KAAK,GAAG,IAAI;KAAW;AACzD;AACA,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B,qKAAA,CAAA,eAAY,EAAE;AACjF,IAAI,qBAAqB,CAAC,OAAO;IACtC,IAAI,YAAY,iBAAiB,MAAM,OAAO,CAAC,CAAA,OAAQ;YAAC,aAAa,UAAU,KAAK,EAAE,GAAG,KAAK,EAAE;YAAE,aAAa,UAAU,KAAK,EAAE,GAAG,KAAK,EAAE;SAAC;IAC3I,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO;IACT;IACA,OAAO;QAAC,KAAK,GAAG,IAAI;QAAY,KAAK,GAAG,IAAI;KAAW;AACzD;AACA,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA4B,qKAAA,CAAA,eAAY;CAAC,EAAE;AACrF,IAAI,qBAAqB,CAAC,OAAO;IACtC,IAAI,YAAY,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,aAAa,UAAU,KAAK,CAAC,GAAG,KAAK,CAAC;IACzF,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO;IACT;IACA,OAAO;QAAC,KAAK,GAAG,IAAI;QAAY,KAAK,GAAG,IAAI;KAAW;AACzD;AACA,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,qKAAA,CAAA,eAAY,EAAE;AAC1F,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B,4BAA4B,4BAA4B,CAAC,YAAY,aAAa;IAC9J,OAAO,aAAa,YAAY,aAAa;AAC/C;AACO,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAe,EAAE;AAC9D,IAAI,yBAAyB,CAAC,cAAc,kBAAkB,qBAAqB,yBAAyB;IACjH,IAAI,2BAA2B,CAAA,GAAA,kKAAA,CAAA,+CAA4C,AAAD,EAAE,kBAAkB,aAAa,iBAAiB;IAC5H,IAAI,4BAA4B,MAAM;QACpC,gDAAgD;QAChD,OAAO;IACT;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE,kBAAkB,aAAa,qBAAqB,yBAAyB,uBAAuB,2BAA2B,aAAa,iBAAiB;AAC/L;AACA,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB;IAAwB;IAA2B;IAAqD;CAA8B,EAAE;AAEpM;;;;CAIC,GACD,IAAI,eAAe;IAAC;IAAG;CAAE;AAClB,IAAI,oBAAoB,CAAC,cAAc,QAAQ,eAAe,kBAAkB,iBAAiB,UAAU;IAChH,IAAI,gBAAgB,QAAQ,iBAAiB,QAAQ,cAAc,MAAM,KAAK,GAAG;QAC/E,OAAO;IACT;IACA,IAAI,EACF,OAAO,EACP,IAAI,EACL,GAAG;IACJ,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,IAAI,iBAAiB,WAAW,MAAM;QACpC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAK,AAAD,EAAE,GAAG,cAAc,MAAM;IACtC;IACA,IAAI,SAAS,YAAY;QACvB,OAAO,4BAA4B,kBAAkB,cAAc;IACrE;IACA,IAAI,oBAAoB,UAAU;QAChC,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,mBAAmB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB,gKAAA,CAAA,oBAAiB;IAAE;IAAqB;IAAwB,2KAAA,CAAA,wBAAqB;IAAE,qKAAA,CAAA,eAAY;IAAE;CAAsB,EAAE;AACpL,IAAI,uBAAuB,CAAC,YAAY,QAAQ,QAAQ,WAAW;IACxE,IAAI,cAAc,MAAM;QACtB,OAAO;IACT;IACA,IAAI,EACF,KAAK,EACL,IAAI,EACL,GAAG;IACJ,IAAI,UAAU,QAAQ;QACpB,IAAI,WAAW,YAAY,aAAa,cAAc;YACpD,OAAO;QACT;QACA,IAAI,WAAW,YAAY,aAAa,aAAa;YACnD,OAAO;QACT;QACA,IAAI,SAAS,cAAc,aAAa,CAAC,UAAU,OAAO,CAAC,gBAAgB,KAAK,UAAU,OAAO,CAAC,gBAAgB,KAAK,UAAU,OAAO,CAAC,oBAAoB,KAAK,CAAC,MAAM,GAAG;YAC1K,OAAO;QACT;QACA,IAAI,SAAS,YAAY;YACvB,OAAO;QACT;QACA,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,OAAO,QAAQ,MAAM,CAAC,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;QACrC,OAAO,QAAQ,yJAAW,OAAO;IACnC;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB,gKAAA,CAAA,oBAAiB;IAAE;IAAc,2KAAA,CAAA,kBAAe;IAAE,qKAAA,CAAA,eAAY;CAAC,EAAE;AAClI,SAAS,mBAAmB,aAAa;IACvC,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,IAAI,iBAAiB,wJAAU;QAC7B,8DAA8D;QAC9D,OAAO,sJAAQ,CAAC,cAAc;IAChC;IACA,IAAI,OAAO,QAAQ,MAAM,CAAC,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;IACrC,IAAI,QAAQ,wJAAU;QACpB,8DAA8D;QAC9D,OAAO,sJAAQ,CAAC,KAAK;IACvB;IACA,OAAO;AACT;AACO,SAAS,qBAAqB,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS;IAC7E,IAAI,cAAc,QAAQ,aAAa,MAAM;QAC3C,OAAO;IACT;IACA,IAAI,OAAO,KAAK,KAAK,KAAK,YAAY;QACpC,6GAA6G;QAC7G,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,KAAK,CAAC;IACpD;IACA,IAAI,kBAAkB,mBAAmB;IACzC,IAAI,mBAAmB,MAAM;QAC3B,OAAO;IACT;IACA,IAAI,QAAQ,gBAAgB,MAAM,CAAC,YAAY,KAAK,CAAC;IACrD,wHAAwH;IACxH,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE;IACnB,OAAO;AACT;AACO,IAAI,mBAAmB,CAAC,YAAY,cAAc;IACvD,IAAI,mBAAmB,oBAAoB;IAC3C,IAAI,kBAAkB,UAAU,kBAAkB,UAAU;QAC1D,OAAO;IACT;IACA,IAAI,gBAAgB,QAAQ,aAAa,SAAS,IAAI,MAAM,OAAO,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE,KAAK,UAAU,gBAAgB,CAAC,EAAE,KAAK,MAAM,KAAK,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa;QACnM,OAAO,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,aAAa,SAAS,EAAE,aAAa,aAAa;IACzF;IACA,IAAI,gBAAgB,QAAQ,aAAa,SAAS,IAAI,aAAa,IAAI,KAAK,YAAY,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa;QAC5H,OAAO,CAAA,GAAA,qKAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,aAAa,SAAS,EAAE,aAAa,aAAa;IAChG;IACA,OAAO;AACT;AACO,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAkB;IAAoB;CAAoB,EAAE;AAClG,IAAI,iCAAiC,CAAC,cAAc,QAAQ,WAAW;IAC5E,IACA;;;;;GAKC,GACD,aAAa,eAAe,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,IAAI,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE,WAAW,MAAM,OAAO,CAAC,cAAc,UAAU,MAAM,GAAG,GAAG;QAChN,IAAI,gBAAgB,MAAM,CAAC,EAAE;QAC7B,IAAI,eAAe,SAAS,CAAC,EAAE;QAC/B,IAAI,gBAAgB,MAAM,CAAC,EAAE;QAC7B,IAAI,eAAe,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QAClD,OAAO;YAAC,KAAK,GAAG,CAAC,eAAe;YAAe,KAAK,GAAG,CAAC,eAAe;SAAc;IACvF;IACA,OAAO;AACT;AACO,IAAI,qCAAqC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB;IAAkB;IAAiB,qKAAA,CAAA,eAAY;CAAC,EAAE;AAQ3H,IAAI,sCAAsC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gBAAgB,CAAC,iBAAiB;IACxH,IAAI,CAAC,gBAAgB,aAAa,IAAI,KAAK,UAAU;QACnD,OAAO;IACT;IACA,IAAI,gCAAgC;IACpC,IAAI,eAAe,MAAM,IAAI,CAAC,iBAAiB,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACtG,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,OAAO;IACT;IACA,IAAI,OAAO,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG,YAAY,CAAC,EAAE;IAClE,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IACA,6FAA6F;IAC7F,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,GAAG,GAAG,IAAK;QAChD,IAAI,WAAW,YAAY,CAAC,IAAI,EAAE,GAAG,YAAY,CAAC,EAAE;QACpD,gCAAgC,KAAK,GAAG,CAAC,+BAA+B;IAC1E;IACA,OAAO,gCAAgC;AACzC;AACA,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,qCAAqC,gKAAA,CAAA,oBAAiB,EAAE,2KAAA,CAAA,uBAAoB,EAAE,kLAAA,CAAA,4BAAyB,EAAE,CAAC,IAAI,IAAI,IAAI,UAAY,SAAS,CAAC,2BAA2B,QAAQ,gBAAgB,QAAQ;IAClP,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,4BAA4B;QACnD,OAAO;IACT;IACA,IAAI,aAAa,WAAW,aAAa,OAAO,MAAM,GAAG,OAAO,KAAK;IACrE,IAAI,YAAY,OAAO;QACrB,OAAO,4BAA4B,aAAa;IAClD;IACA,IAAI,YAAY,UAAU;QACxB,IAAI,MAAM,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,4BAA4B;QACtE,IAAI,WAAW,4BAA4B,aAAa;QACxD,OAAO,WAAW,MAAM,CAAC,WAAW,GAAG,IAAI,aAAa;IAC1D;IACA,OAAO;AACT;AACO,IAAI,+BAA+B,CAAC,OAAO;IAChD,IAAI,gBAAgB,oBAAoB,OAAO;IAC/C,IAAI,iBAAiB,QAAQ,OAAO,cAAc,OAAO,KAAK,UAAU;QACtE,OAAO;IACT;IACA,OAAO,wBAAwB,OAAO,SAAS,QAAQ,cAAc,OAAO;AAC9E;AACO,IAAI,+BAA+B,CAAC,OAAO;IAChD,IAAI,gBAAgB,oBAAoB,OAAO;IAC/C,IAAI,iBAAiB,QAAQ,OAAO,cAAc,OAAO,KAAK,UAAU;QACtE,OAAO;IACT;IACA,OAAO,wBAAwB,OAAO,SAAS,QAAQ,cAAc,OAAO;AAC9E;AACA,IAAI,qBAAqB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB,8BAA8B,CAAC,eAAe;IACzG,IAAI,eAAe;IACnB,IAAI,iBAAiB,MAAM;QACzB,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;IACA,IAAI,EACF,OAAO,EACR,GAAG;IACJ,IAAI,OAAO,YAAY,UAAU;QAC/B,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;QACL,MAAM,CAAC,CAAC,gBAAgB,QAAQ,IAAI,MAAM,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,CAAC,IAAI;QAClG,OAAO,CAAC,CAAC,iBAAiB,QAAQ,KAAK,MAAM,QAAQ,mBAAmB,KAAK,IAAI,iBAAiB,CAAC,IAAI;IACzG;AACF;AACA,IAAI,qBAAqB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB,8BAA8B,CAAC,eAAe;IACzG,IAAI,cAAc;IAClB,IAAI,iBAAiB,MAAM;QACzB,OAAO;YACL,KAAK;YACL,QAAQ;QACV;IACF;IACA,IAAI,EACF,OAAO,EACR,GAAG;IACJ,IAAI,OAAO,YAAY,UAAU;QAC/B,OAAO;YACL,KAAK;YACL,QAAQ;QACV;IACF;IACA,OAAO;QACL,KAAK,CAAC,CAAC,eAAe,QAAQ,GAAG,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe,CAAC,IAAI;QAC7F,QAAQ,CAAC,CAAC,kBAAkB,QAAQ,MAAM,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,CAAC,IAAI;IAC9G;AACF;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,kLAAA,CAAA,4BAAyB;IAAE;IAAoB,uKAAA,CAAA,wBAAqB;IAAE,uKAAA,CAAA,sBAAmB;IAAE,CAAC,QAAQ,SAAS,aAAe;CAAW,EAAE,CAAC,QAAQ,SAAS,iBAAiB,OAAO;IAChO,IAAI,EACF,SAAS,YAAY,EACtB,GAAG;IACJ,IAAI,YAAY;QACd,OAAO;YAAC,aAAa,IAAI;YAAE,gBAAgB,KAAK,GAAG,aAAa,KAAK;SAAC;IACxE;IACA,OAAO;QAAC,OAAO,IAAI,GAAG,QAAQ,IAAI;QAAE,OAAO,IAAI,GAAG,OAAO,KAAK,GAAG,QAAQ,KAAK;KAAC;AACjF;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,kLAAA,CAAA,4BAAyB;IAAE,gKAAA,CAAA,oBAAiB;IAAE;IAAoB,uKAAA,CAAA,wBAAqB;IAAE,uKAAA,CAAA,sBAAmB;IAAE,CAAC,QAAQ,SAAS,aAAe;CAAW,EAAE,CAAC,QAAQ,QAAQ,SAAS,iBAAiB,OAAO;IAC3P,IAAI,EACF,SAAS,YAAY,EACtB,GAAG;IACJ,IAAI,YAAY;QACd,OAAO;YAAC,gBAAgB,MAAM,GAAG,aAAa,MAAM;YAAE,aAAa,GAAG;SAAC;IACzE;IACA,IAAI,WAAW,cAAc;QAC3B,OAAO;YAAC,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG,QAAQ,MAAM;YAAE,OAAO,GAAG,GAAG,QAAQ,GAAG;SAAC;IAChF;IACA,OAAO;QAAC,OAAO,GAAG,GAAG,QAAQ,GAAG;QAAE,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG,QAAQ,MAAM;KAAC;AAChF;AACO,IAAI,kBAAkB,CAAC,OAAO,UAAU,QAAQ;IACrD,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,OAAO,kBAAkB,OAAO,QAAQ;QAC1C,KAAK;YACH,OAAO,kBAAkB,OAAO,QAAQ;QAC1C,KAAK;YACH,OAAO,CAAC,uBAAuB,oBAAoB,OAAO,OAAO,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,KAAK;QACtJ,KAAK;YACH,OAAO,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;QAC9B,KAAK;YACH,OAAO,CAAA,GAAA,2KAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;QACtC;YACE,OAAO;IACX;AACF;AACO,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB;CAAgB,EAAE,iMAAA,CAAA,8BAA2B;AAC9G,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB;IAAqB;IAAoC;CAA2B,EAAE;AAC5I,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,qKAAA,CAAA,eAAY,EAAE,CAAC,OAAO;IACtG,OAAO,MAAM,OAAO,CAAC,CAAA;QACnB,IAAI;QACJ,OAAO,CAAC,mBAAmB,KAAK,SAAS,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,EAAE;IAC5G,GAAG,MAAM,CAAC,CAAA;QACR,OAAO,8BAA8B,UAAU;IACjD;AACF;AACA,SAAS,WAAW,CAAC,EAAE,CAAC;IACtB,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QACf,OAAO,CAAC;IACV;IACA,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QACf,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,sBAAsB,CAAC,QAAQ,cAAgB;AACnD,IAAI,aAAa,CAAC,QAAQ,cAAc,SAAW;AACnD,IAAI,+BAA+B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,sKAAA,CAAA,iBAAc,EAAE,qBAAqB,YAAY,CAAC,SAAS,aAAa,SAAW,QAAQ,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,IAAI,CAAC;AAC1O,IAAI,+BAA+B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,sKAAA,CAAA,iBAAc,EAAE,qBAAqB,YAAY,CAAC,SAAS,aAAa,SAAW,QAAQ,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,IAAI,CAAC;AAC1O,IAAI,eAAe,CAAC,QAAQ;IAC1B,OAAO;QACL,OAAO,OAAO,KAAK;QACnB,QAAQ,aAAa,MAAM;IAC7B;AACF;AACA,IAAI,eAAe,CAAC,QAAQ;IAC1B,IAAI,QAAQ,OAAO,aAAa,KAAK,KAAK,WAAW,aAAa,KAAK,GAAG,oJAAA,CAAA,uBAAoB;IAC9F,OAAO;QACL;QACA,QAAQ,OAAO,MAAM;IACvB;AACF;AACO,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,kLAAA,CAAA,4BAAyB,EAAE,qBAAqB;AAC5F,IAAI,oCAAoC,CAAC,QAAQ,aAAa;IAC5D,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,GAAG;QACnB,KAAK;YACH,OAAO,cAAc,OAAO,MAAM;QACpC;YACE,OAAO;IACX;AACF;AACA,IAAI,oCAAoC,CAAC,QAAQ,aAAa;IAC5D,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,IAAI;QACpB,KAAK;YACH,OAAO,aAAa,OAAO,KAAK;QAClC;YACE,OAAO;IACX;AACF;AACO,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2KAAA,CAAA,oBAAiB,EAAE,kLAAA,CAAA,4BAAyB,EAAE,8BAA8B,qBAAqB,YAAY,CAAC,aAAa,QAAQ,2BAA2B,aAAa;IAC/N,IAAI,QAAQ,CAAC;IACb,IAAI;IACJ,0BAA0B,OAAO,CAAC,CAAA;QAChC,IAAI,WAAW,aAAa,QAAQ;QACpC,IAAI,YAAY,MAAM;YACpB,WAAW,kCAAkC,QAAQ,aAAa;QACpE;QACA,IAAI,YAAY,gBAAgB,SAAS,CAAC,UAAU,gBAAgB,YAAY;QAChF,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,OAAO,aAAa,SAAS,MAAM;QAC/D,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,MAAM;IACpD;IACA,OAAO;AACT;AACO,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,2KAAA,CAAA,mBAAgB,EAAE,kLAAA,CAAA,4BAAyB,EAAE,8BAA8B,qBAAqB,YAAY,CAAC,YAAY,QAAQ,2BAA2B,aAAa;IAC7N,IAAI,QAAQ,CAAC;IACb,IAAI;IACJ,0BAA0B,OAAO,CAAC,CAAA;QAChC,IAAI,WAAW,aAAa,QAAQ;QACpC,IAAI,YAAY,MAAM;YACpB,WAAW,kCAAkC,QAAQ,aAAa;QACpE;QACA,IAAI,YAAY,gBAAgB,UAAU,CAAC,UAAU,gBAAgB,WAAW;QAChF,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,OAAO,aAAa,SAAS,KAAK;QAC9D,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,KAAK;IACnD;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,CAAC,OAAO;IACvC,IAAI,SAAS,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD,EAAE;IACvC,IAAI,eAAe,oBAAoB,OAAO;IAC9C,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,IAAI,WAAW,0BAA0B,OAAO,aAAa,WAAW,EAAE,aAAa,MAAM;IAC7F,IAAI,iBAAiB,QAAQ,CAAC,OAAO;IACrC,IAAI,kBAAkB,MAAM;QAC1B,OAAO;YACL,GAAG,OAAO,IAAI;YACd,GAAG;QACL;IACF;IACA,OAAO;QACL,GAAG,OAAO,IAAI;QACd,GAAG;IACL;AACF;AACO,IAAI,sBAAsB,CAAC,OAAO;IACvC,IAAI,SAAS,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD,EAAE;IACvC,IAAI,eAAe,oBAAoB,OAAO;IAC9C,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,IAAI,WAAW,0BAA0B,OAAO,aAAa,WAAW,EAAE,aAAa,MAAM;IAC7F,IAAI,iBAAiB,QAAQ,CAAC,OAAO;IACrC,IAAI,kBAAkB,MAAM;QAC1B,OAAO;YACL,GAAG;YACH,GAAG,OAAO,GAAG;QACf;IACF;IACA,OAAO;QACL,GAAG;QACH,GAAG,OAAO,GAAG;IACf;AACF;AACO,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,kLAAA,CAAA,4BAAyB,EAAE,qBAAqB,CAAC,QAAQ;IACnG,IAAI,QAAQ,OAAO,aAAa,KAAK,KAAK,WAAW,aAAa,KAAK,GAAG,oJAAA,CAAA,uBAAoB;IAC9F,OAAO;QACL;QACA,QAAQ,OAAO,MAAM;IACvB;AACF;AACO,IAAI,0BAA0B,CAAC,OAAO,UAAU;IACrD,OAAQ;QACN,KAAK;YACH;gBACE,OAAO,gBAAgB,OAAO,QAAQ,KAAK;YAC7C;QACF,KAAK;YACH;gBACE,OAAO,gBAAgB,OAAO,QAAQ,MAAM;YAC9C;QACF;YACE;gBACE,OAAO;YACT;IACJ;AACF;AACO,IAAI,yBAAyB,CAAC,aAAa,eAAe,MAAM;IACrE,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,IAAI,EACF,uBAAuB,EACvB,IAAI,EACJ,OAAO,EACR,GAAG;IACJ,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa;IACnD,IAAI,UAAU,cAAc,GAAG,CAAC,CAAA,KAAM,GAAG,KAAK;IAC9C,IAAI,WAAW,iBAAiB,SAAS,cAAc,2BAA2B,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,UAAU;QACvG,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAwB;IAAgB,qKAAA,CAAA,eAAY;CAAC,EAAE;AACtH,IAAI,2BAA2B,CAAC,QAAQ,eAAe,MAAM;IAClE,IAAI,QAAQ,QAAQ,KAAK,OAAO,IAAI,MAAM;QACxC,OAAO;IACT;IACA,IAAI,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,IAAI,iBAAiB,CAAC,SAAS,YAAY,UAAU,MAAM,GAAG;QAC5D,OAAO,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IACvC;IACA,OAAO;AACT;AACO,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAwB;IAAoB,qKAAA,CAAA,eAAY;CAAC,EAAE;AAC5H,IAAI,sDAAsD,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAA6B;IAAqB;IAAiB;IAAuB;IAAyB;IAAiB;IAAiB,qKAAA,CAAA,eAAY;CAAC,EAAE,CAAC,QAAQ,MAAM,eAAe,OAAO,iBAAiB,mBAAmB,WAAW,WAAW;IACrW,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,OAAO;QACL,OAAO,KAAK,KAAK;QACjB,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,aAAa,KAAK,WAAW;QAC7B,MAAM,KAAK,IAAI;QACf,WAAW,KAAK,SAAS;QACzB,eAAe,KAAK,aAAa;QACjC,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;QACf;QACA;QACA;QACA;QACA;QACA,OAAO;QACP;QACA;IACF;AACF;AACO,IAAI,mBAAmB,CAAC,QAAQ,MAAM,eAAe,OAAO,WAAW,WAAW,iBAAiB,mBAAmB;IAC3H,IAAI,QAAQ,QAAQ,SAAS,MAAM;QACjC,OAAO;IACT;IACA,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,IAAI,EACF,IAAI,EACJ,KAAK,EACL,SAAS,EACV,GAAG;IAEJ,+IAA+I;IAC/I,IAAI,gBAAgB,kBAAkB,eAAe,OAAO,MAAM,SAAS,KAAK,aAAa,MAAM,SAAS,KAAK,IAAI;IACrH,IAAI,SAAS,SAAS,cAAc,MAAM,SAAS,GAAG,MAAM,SAAS,KAAK,gBAAgB;IAC1F,SAAS,aAAa,eAAe,aAAa,QAAQ,UAAU,MAAM,IAAI,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI,IAAI,SAAS;IAEvI,2EAA2E;IAC3E,IAAI,mBAAmB,SAAS;IAChC,IAAI,kBAAkB;QACpB,IAAI,SAAS,iBAAiB,GAAG,CAAC,CAAC,OAAO;YACxC,IAAI,eAAe,kBAAkB,gBAAgB,OAAO,CAAC,SAAS;YACtE,OAAO;gBACL;gBACA,mEAAmE;gBACnE,+EAA+E;gBAC/E,YAAY,MAAM,gBAAgB;gBAClC,OAAO;gBACP;YACF;QACF;QACA,OAAO,OAAO,MAAM,CAAC,CAAA,MAAO,CAAC,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,UAAU;IACnD;IAEA,qGAAqG;IACrG,IAAI,iBAAiB,mBAAmB;QACtC,OAAO,kBAAkB,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBAC9C,YAAY,MAAM,SAAS;gBAC3B,OAAO;gBACP;gBACA;YACF,CAAC;IACH;IACA,IAAI,MAAM,KAAK,EAAE;QACf,OAAO,MAAM,KAAK,CAAC,UACnB,6EAA6E;SAC5E,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,YAAY,MAAM,SAAS;gBAC3B,OAAO;gBACP;YACF,CAAC;IACH;IAEA,2EAA2E;IAC3E,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC3C,YAAY,MAAM,SAAS;YAC3B,OAAO,kBAAkB,eAAe,CAAC,MAAM,GAAG;YAClD;YACA;QACF,CAAC;AACH;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAoB;IAAqB;IAAiB;IAAiB;IAAiB;IAAuB;IAAyB,qKAAA,CAAA,eAAY;CAAC,EAAE;AACtN,IAAI,4BAA4B,CAAC,QAAQ,MAAM,OAAO,WAAW,iBAAiB,mBAAmB;IAC1G,IAAI,QAAQ,QAAQ,SAAS,QAAQ,aAAa,QAAQ,SAAS,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE;QACvF,OAAO;IACT;IACA,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,IAAI,EACF,SAAS,EACV,GAAG;IACJ,IAAI,SAAS;IACb,SAAS,aAAa,eAAe,CAAC,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,MAAM,KAAK,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI,IAAI,SAAS;IAE1K,qGAAqG;IACrG,IAAI,iBAAiB,mBAAmB;QACtC,OAAO,kBAAkB,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBAC9C,YAAY,MAAM,SAAS;gBAC3B,OAAO;gBACP;gBACA;YACF,CAAC;IACH;IACA,IAAI,MAAM,KAAK,EAAE;QACf,OAAO,MAAM,KAAK,CAAC,UACnB,6EAA6E;SAC5E,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,YAAY,MAAM,SAAS;gBAC3B,OAAO;gBACP;YACF,CAAC;IACH;IAEA,2EAA2E;IAC3E,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC3C,YAAY,MAAM,SAAS;YAC3B,OAAO,kBAAkB,eAAe,CAAC,MAAM,GAAG;YAClD;YACA;QACF,CAAC;AACH;AACO,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAoB;IAAiB;IAAiB;IAAuB;IAAyB,qKAAA,CAAA,eAAY;CAAC,EAAE;AACzL,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,iBAAiB,CAAC,MAAM;IACtF,IAAI,QAAQ,QAAQ,SAAS,MAAM;QACjC,OAAO;IACT;IACA,OAAO,cAAc,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG;QAChD;IACF;AACF;AACA,IAAI,mBAAmB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAgB;IAAqB;IAAkB;CAA2B,EAAE;AACpH,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,OAAO,WAAW,SAAW,oBAAoB,OAAO,SAAS,kBAAkB,CAAC,MAAM;IAC1I,IAAI,QAAQ,QAAQ,SAAS,MAAM;QACjC,OAAO;IACT;IACA,OAAO,cAAc,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG;QAChD;IACF;AACF;AAMO,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE,sKAAA,CAAA,iBAAc;IAAE,sKAAA,CAAA,iBAAc;CAAC,EAAE,CAAC,QAAQ,UAAU;IACvH,OAAQ;QACN,KAAK;YACH;gBACE,OAAO,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,kBAAkB;YAClE;QACF,KAAK;YACH;gBACE,OAAO,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,kBAAkB;YAClE;QACF,qFAAqF;QACrF,8FAA8F;QAC9F,KAAK;QACL,KAAK;YACH;gBACE,OAAO;YACT;QACF;YACE;gBACE,OAAO;YACT;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2202, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectTooltipEventType.js"], "sourcesContent": ["import { useAppSelector } from '../hooks';\nexport var selectDefaultTooltipEventType = state => state.options.defaultTooltipEventType;\nexport var selectValidateTooltipEventTypes = state => state.options.validateTooltipEventTypes;\nexport function combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes) {\n  if (shared == null) {\n    return defaultTooltipEventType;\n  }\n  var eventType = shared ? 'axis' : 'item';\n  if (validateTooltipEventTypes == null) {\n    return defaultTooltipEventType;\n  }\n  return validateTooltipEventTypes.includes(eventType) ? eventType : defaultTooltipEventType;\n}\nexport function selectTooltipEventType(state, shared) {\n  var defaultTooltipEventType = selectDefaultTooltipEventType(state);\n  var validateTooltipEventTypes = selectValidateTooltipEventTypes(state);\n  return combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes);\n}\nexport function useTooltipEventType(shared) {\n  return useAppSelector(state => selectTooltipEventType(state, shared));\n}"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,IAAI,gCAAgC,CAAA,QAAS,MAAM,OAAO,CAAC,uBAAuB;AAClF,IAAI,kCAAkC,CAAA,QAAS,MAAM,OAAO,CAAC,yBAAyB;AACtF,SAAS,wBAAwB,MAAM,EAAE,uBAAuB,EAAE,yBAAyB;IAChG,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,YAAY,SAAS,SAAS;IAClC,IAAI,6BAA6B,MAAM;QACrC,OAAO;IACT;IACA,OAAO,0BAA0B,QAAQ,CAAC,aAAa,YAAY;AACrE;AACO,SAAS,uBAAuB,KAAK,EAAE,MAAM;IAClD,IAAI,0BAA0B,8BAA8B;IAC5D,IAAI,4BAA4B,gCAAgC;IAChE,OAAO,wBAAwB,QAAQ,yBAAyB;AAClE;AACO,SAAS,oBAAoB,MAAM;IACxC,OAAO,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,QAAS,uBAAuB,OAAO;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2235, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineActiveLabel.js"], "sourcesContent": ["import { isNan } from '../../../util/DataUtils';\nexport var combineActiveLabel = (tooltipTicks, activeIndex) => {\n  var _tooltipTicks$n;\n  var n = Number(activeIndex);\n  if (isNan(n) || activeIndex == null) {\n    return undefined;\n  }\n  return n >= 0 ? tooltipTicks === null || tooltipTicks === void 0 || (_tooltipTicks$n = tooltipTicks[n]) === null || _tooltipTicks$n === void 0 ? void 0 : _tooltipTicks$n.value : undefined;\n};"], "names": [], "mappings": ";;;AAAA;;AACO,IAAI,qBAAqB,CAAC,cAAc;IAC7C,IAAI;IACJ,IAAI,IAAI,OAAO;IACf,IAAI,CAAA,GAAA,oJAAA,CAAA,QAAK,AAAD,EAAE,MAAM,eAAe,MAAM;QACnC,OAAO;IACT;IACA,OAAO,KAAK,IAAI,iBAAiB,QAAQ,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,YAAY,CAAC,EAAE,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,KAAK,GAAG;AACpL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectTooltipSettings.js"], "sourcesContent": ["export var selectTooltipSettings = state => state.tooltip.settings;"], "names": [], "mappings": ";;;AAAO,IAAI,wBAAwB,CAAA,QAAS,MAAM,OAAO,CAAC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineTooltipInteractionState.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { noInteraction } from '../../tooltipSlice';\nfunction chooseAppropriateMouseInteraction(tooltipState, tooltipEventType, trigger) {\n  if (tooltipEventType === 'axis') {\n    if (trigger === 'click') {\n      return tooltipState.axisInteraction.click;\n    }\n    return tooltipState.axisInteraction.hover;\n  }\n  if (trigger === 'click') {\n    return tooltipState.itemInteraction.click;\n  }\n  return tooltipState.itemInteraction.hover;\n}\nfunction hasBeenActivePreviously(tooltipInteractionState) {\n  return tooltipInteractionState.index != null;\n}\nexport var combineTooltipInteractionState = (tooltipState, tooltipEventType, trigger, defaultIndex) => {\n  if (tooltipEventType == null) {\n    return noInteraction;\n  }\n  var appropriateMouseInteraction = chooseAppropriateMouseInteraction(tooltipState, tooltipEventType, trigger);\n  if (appropriateMouseInteraction == null) {\n    return noInteraction;\n  }\n  if (appropriateMouseInteraction.active) {\n    return appropriateMouseInteraction;\n  }\n  if (tooltipState.keyboardInteraction.active) {\n    return tooltipState.keyboardInteraction;\n  }\n  if (tooltipState.syncInteraction.active && tooltipState.syncInteraction.index != null) {\n    return tooltipState.syncInteraction;\n  }\n  var activeFromProps = tooltipState.settings.active === true;\n  if (hasBeenActivePreviously(appropriateMouseInteraction)) {\n    if (activeFromProps) {\n      return _objectSpread(_objectSpread({}, appropriateMouseInteraction), {}, {\n        active: true\n      });\n    }\n  } else if (defaultIndex != null) {\n    return {\n      active: true,\n      coordinate: undefined,\n      dataKey: undefined,\n      index: defaultIndex\n    };\n  }\n  return _objectSpread(_objectSpread({}, noInteraction), {}, {\n    coordinate: appropriateMouseInteraction.coordinate\n  });\n};"], "names": [], "mappings": ";;;AAKA;AALA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;AAEvT,SAAS,kCAAkC,YAAY,EAAE,gBAAgB,EAAE,OAAO;IAChF,IAAI,qBAAqB,QAAQ;QAC/B,IAAI,YAAY,SAAS;YACvB,OAAO,aAAa,eAAe,CAAC,KAAK;QAC3C;QACA,OAAO,aAAa,eAAe,CAAC,KAAK;IAC3C;IACA,IAAI,YAAY,SAAS;QACvB,OAAO,aAAa,eAAe,CAAC,KAAK;IAC3C;IACA,OAAO,aAAa,eAAe,CAAC,KAAK;AAC3C;AACA,SAAS,wBAAwB,uBAAuB;IACtD,OAAO,wBAAwB,KAAK,IAAI;AAC1C;AACO,IAAI,iCAAiC,CAAC,cAAc,kBAAkB,SAAS;IACpF,IAAI,oBAAoB,MAAM;QAC5B,OAAO,wJAAA,CAAA,gBAAa;IACtB;IACA,IAAI,8BAA8B,kCAAkC,cAAc,kBAAkB;IACpG,IAAI,+BAA+B,MAAM;QACvC,OAAO,wJAAA,CAAA,gBAAa;IACtB;IACA,IAAI,4BAA4B,MAAM,EAAE;QACtC,OAAO;IACT;IACA,IAAI,aAAa,mBAAmB,CAAC,MAAM,EAAE;QAC3C,OAAO,aAAa,mBAAmB;IACzC;IACA,IAAI,aAAa,eAAe,CAAC,MAAM,IAAI,aAAa,eAAe,CAAC,KAAK,IAAI,MAAM;QACrF,OAAO,aAAa,eAAe;IACrC;IACA,IAAI,kBAAkB,aAAa,QAAQ,CAAC,MAAM,KAAK;IACvD,IAAI,wBAAwB,8BAA8B;QACxD,IAAI,iBAAiB;YACnB,OAAO,cAAc,cAAc,CAAC,GAAG,8BAA8B,CAAC,GAAG;gBACvE,QAAQ;YACV;QACF;IACF,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,OAAO;QACT;IACF;IACA,OAAO,cAAc,cAAc,CAAC,GAAG,wJAAA,CAAA,gBAAa,GAAG,CAAC,GAAG;QACzD,YAAY,4BAA4B,UAAU;IACpD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineActiveTooltipIndex.js"], "sourcesContent": ["import { isWellBehavedNumber } from '../../../util/isWellBehavedNumber';\nexport var combineActiveTooltipIndex = (tooltipInteraction, chartData) => {\n  var desiredIndex = tooltipInteraction === null || tooltipInteraction === void 0 ? void 0 : tooltipInteraction.index;\n  if (desiredIndex == null) {\n    return null;\n  }\n  var indexAsNumber = Number(desiredIndex);\n  if (!isWellBehavedNumber(indexAsNumber)) {\n    // this is for charts like Sankey and Treemap that do not support numerical indexes. We need a proper solution for this before we can start supporting keyboard events on these charts.\n    return desiredIndex;\n  }\n\n  /*\n   * Zero is a trivial limit for single-dimensional charts like Line and Area,\n   * but this also needs a support for multidimensional charts like Sankey and Treemap! TODO\n   */\n  var lowerLimit = 0;\n  var upperLimit = +Infinity;\n  if (chartData.length > 0) {\n    upperLimit = chartData.length - 1;\n  }\n\n  // now let's clamp the desiredIndex between the limits\n  return String(Math.max(lowerLimit, Math.min(indexAsNumber, upperLimit)));\n};"], "names": [], "mappings": ";;;AAAA;;AACO,IAAI,4BAA4B,CAAC,oBAAoB;IAC1D,IAAI,eAAe,uBAAuB,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,KAAK;IACnH,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB;QACvC,uLAAuL;QACvL,OAAO;IACT;IAEA;;;GAGC,GACD,IAAI,aAAa;IACjB,IAAI,aAAa,CAAC;IAClB,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,aAAa,UAAU,MAAM,GAAG;IAClC;IAEA,sDAAsD;IACtD,OAAO,OAAO,KAAK,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC,eAAe;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineCoordinateForDefaultIndex.js"], "sourcesContent": ["export var combineCoordinateForDefaultIndex = (width, height, layout, offset, tooltipTicks, defaultIndex, tooltipConfigurations, tooltipPayloadSearcher) => {\n  if (defaultIndex == null || tooltipPayloadSearcher == null) {\n    return undefined;\n  }\n  // With defaultIndex alone, we don't have enough information to decide _which_ of the multiple tooltips to display. So we choose the first one.\n  var firstConfiguration = tooltipConfigurations[0];\n  // @ts-expect-error we need to rethink the tooltipPayloadSearcher type\n  var maybePosition = firstConfiguration == null ? undefined : tooltipPayloadSearcher(firstConfiguration.positions, defaultIndex);\n  if (maybePosition != null) {\n    return maybePosition;\n  }\n  var tick = tooltipTicks === null || tooltipTicks === void 0 ? void 0 : tooltipTicks[Number(defaultIndex)];\n  if (!tick) {\n    return undefined;\n  }\n  switch (layout) {\n    case 'horizontal':\n      {\n        return {\n          x: tick.coordinate,\n          y: (offset.top + height) / 2\n        };\n      }\n    default:\n      {\n        // This logic is not super sound - it conflates vertical, radial, centric layouts into just one. TODO improve!\n        return {\n          x: (offset.left + width) / 2,\n          y: tick.coordinate\n        };\n      }\n  }\n};"], "names": [], "mappings": ";;;AAAO,IAAI,mCAAmC,CAAC,OAAO,QAAQ,QAAQ,QAAQ,cAAc,cAAc,uBAAuB;IAC/H,IAAI,gBAAgB,QAAQ,0BAA0B,MAAM;QAC1D,OAAO;IACT;IACA,+IAA+I;IAC/I,IAAI,qBAAqB,qBAAqB,CAAC,EAAE;IACjD,sEAAsE;IACtE,IAAI,gBAAgB,sBAAsB,OAAO,YAAY,uBAAuB,mBAAmB,SAAS,EAAE;IAClH,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,IAAI,OAAO,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,YAAY,CAAC,OAAO,cAAc;IACzG,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAQ;QACN,KAAK;YACH;gBACE,OAAO;oBACL,GAAG,KAAK,UAAU;oBAClB,GAAG,CAAC,OAAO,GAAG,GAAG,MAAM,IAAI;gBAC7B;YACF;QACF;YACE;gBACE,8GAA8G;gBAC9G,OAAO;oBACL,GAAG,CAAC,OAAO,IAAI,GAAG,KAAK,IAAI;oBAC3B,GAAG,KAAK,UAAU;gBACpB;YACF;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2433, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineTooltipPayloadConfigurations.js"], "sourcesContent": ["export var combineTooltipPayloadConfigurations = (tooltipState, tooltipEventType, trigger, defaultIndex) => {\n  // if tooltip reacts to axis interaction, then we display all items at the same time.\n  if (tooltipEventType === 'axis') {\n    return tooltipState.tooltipItemPayloads;\n  }\n  /*\n   * By now we already know that tooltipEventType is 'item', so we can only search in itemInteractions.\n   * item means that only the hovered or clicked item will be present in the tooltip.\n   */\n  if (tooltipState.tooltipItemPayloads.length === 0) {\n    // No point filtering if the payload is empty\n    return [];\n  }\n  var filterByDataKey;\n  if (trigger === 'hover') {\n    filterByDataKey = tooltipState.itemInteraction.hover.dataKey;\n  } else {\n    filterByDataKey = tooltipState.itemInteraction.click.dataKey;\n  }\n  if (filterByDataKey == null && defaultIndex != null) {\n    /*\n     * So when we use `defaultIndex` - we don't have a dataKey to filter by because user did not hover over anything yet.\n     * In that case let's display the first item in the tooltip; after all, this is `item` interaction case,\n     * so we should display only one item at a time instead of all.\n     */\n    return [tooltipState.tooltipItemPayloads[0]];\n  }\n  return tooltipState.tooltipItemPayloads.filter(tpc => {\n    var _tpc$settings;\n    return ((_tpc$settings = tpc.settings) === null || _tpc$settings === void 0 ? void 0 : _tpc$settings.dataKey) === filterByDataKey;\n  });\n};"], "names": [], "mappings": ";;;AAAO,IAAI,sCAAsC,CAAC,cAAc,kBAAkB,SAAS;IACzF,qFAAqF;IACrF,IAAI,qBAAqB,QAAQ;QAC/B,OAAO,aAAa,mBAAmB;IACzC;IACA;;;GAGC,GACD,IAAI,aAAa,mBAAmB,CAAC,MAAM,KAAK,GAAG;QACjD,6CAA6C;QAC7C,OAAO,EAAE;IACX;IACA,IAAI;IACJ,IAAI,YAAY,SAAS;QACvB,kBAAkB,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;IAC9D,OAAO;QACL,kBAAkB,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;IAC9D;IACA,IAAI,mBAAmB,QAAQ,gBAAgB,MAAM;QACnD;;;;KAIC,GACD,OAAO;YAAC,aAAa,mBAAmB,CAAC,EAAE;SAAC;IAC9C;IACA,OAAO,aAAa,mBAAmB,CAAC,MAAM,CAAC,CAAA;QAC7C,IAAI;QACJ,OAAO,CAAC,CAAC,gBAAgB,IAAI,QAAQ,MAAM,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,cAAc,OAAO,MAAM;IACpH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2472, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectTooltipPayloadSearcher.js"], "sourcesContent": ["export var selectTooltipPayloadSearcher = state => state.options.tooltipPayloadSearcher;"], "names": [], "mappings": ";;;AAAO,IAAI,+BAA+B,CAAA,QAAS,MAAM,OAAO,CAAC,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2480, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectTooltipState.js"], "sourcesContent": ["export var selectTooltipState = state => state.tooltip;"], "names": [], "mappings": ";;;AAAO,IAAI,qBAAqB,CAAA,QAAS,MAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2488, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/combiners/combineTooltipPayload.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { findEntryInArray } from '../../../util/DataUtils';\nimport { getTooltipEntry, getValueByDataKey } from '../../../util/ChartUtils';\nfunction getSliced(arr, startIndex, endIndex) {\n  if (!Array.isArray(arr)) {\n    return arr;\n  }\n  if (arr && startIndex + endIndex !== 0) {\n    return arr.slice(startIndex, endIndex + 1);\n  }\n  return arr;\n}\nfunction selectFinalData(dataDefinedOnItem, dataDefinedOnChart) {\n  /*\n   * If a payload has data specified directly from the graphical item, prefer that.\n   * Otherwise, fill in data from the chart level, using the same index.\n   */\n  if (dataDefinedOnItem != null) {\n    return dataDefinedOnItem;\n  }\n  return dataDefinedOnChart;\n}\nexport var combineTooltipPayload = (tooltipPayloadConfigurations, activeIndex, chartDataState, tooltipAxis, activeLabel, tooltipPayloadSearcher, tooltipEventType) => {\n  if (activeIndex == null || tooltipPayloadSearcher == null) {\n    return undefined;\n  }\n  var {\n    chartData,\n    computedData,\n    dataStartIndex,\n    dataEndIndex\n  } = chartDataState;\n  var init = [];\n  return tooltipPayloadConfigurations.reduce((agg, _ref) => {\n    var _settings$dataKey;\n    var {\n      dataDefinedOnItem,\n      settings\n    } = _ref;\n    var finalData = selectFinalData(dataDefinedOnItem, chartData);\n    var sliced = getSliced(finalData, dataStartIndex, dataEndIndex);\n    var finalDataKey = (_settings$dataKey = settings === null || settings === void 0 ? void 0 : settings.dataKey) !== null && _settings$dataKey !== void 0 ? _settings$dataKey : tooltipAxis === null || tooltipAxis === void 0 ? void 0 : tooltipAxis.dataKey;\n    // BaseAxisProps does not support nameKey but it could!\n    var finalNameKey = settings === null || settings === void 0 ? void 0 : settings.nameKey; // ?? tooltipAxis?.nameKey;\n    var tooltipPayload;\n    if (tooltipAxis !== null && tooltipAxis !== void 0 && tooltipAxis.dataKey && Array.isArray(sliced) &&\n    /*\n     * findEntryInArray won't work for Scatter because Scatter provides an array of arrays\n     * as tooltip payloads and findEntryInArray is not prepared to handle that.\n     * Sad but also ScatterChart only allows 'item' tooltipEventType\n     * and also this is only a problem if there are multiple Scatters and each has its own data array\n     * so let's fix that some other time.\n     */\n    !Array.isArray(sliced[0]) &&\n    /*\n     * If the tooltipEventType is 'axis', we should search for the dataKey in the sliced data\n     * because thanks to allowDuplicatedCategory=false, the order of elements in the array\n     * no longer matches the order of elements in the original data\n     * and so we need to search by the active dataKey + label rather than by index.\n     *\n     * The same happens if multiple graphical items are present in the chart\n     * and each of them has its own data array. Those arrays get concatenated\n     * and again the tooltip index no longer matches the original data.\n     *\n     * On the other hand the tooltipEventType 'item' should always search by index\n     * because we get the index from interacting over the individual elements\n     * which is always accurate, irrespective of the allowDuplicatedCategory setting.\n     */\n    tooltipEventType === 'axis') {\n      tooltipPayload = findEntryInArray(sliced, tooltipAxis.dataKey, activeLabel);\n    } else {\n      /*\n       * This is a problem because it assumes that the index is pointing to the displayed data\n       * which it isn't because the index is pointing to the tooltip ticks array.\n       * The above approach (with findEntryInArray) is the correct one, but it only works\n       * if the axis dataKey is defined explicitly, and if the data is an array of objects.\n       */\n      tooltipPayload = tooltipPayloadSearcher(sliced, activeIndex, computedData, finalNameKey);\n    }\n    if (Array.isArray(tooltipPayload)) {\n      tooltipPayload.forEach(item => {\n        var newSettings = _objectSpread(_objectSpread({}, settings), {}, {\n          name: item.name,\n          unit: item.unit,\n          // color and fill are erased to keep 100% the identical behaviour to recharts 2.x - but there's nothing stopping us from returning them here. It's technically a breaking change.\n          color: undefined,\n          // color and fill are erased to keep 100% the identical behaviour to recharts 2.x - but there's nothing stopping us from returning them here. It's technically a breaking change.\n          fill: undefined\n        });\n        agg.push(getTooltipEntry({\n          tooltipEntrySettings: newSettings,\n          dataKey: item.dataKey,\n          payload: item.payload,\n          // @ts-expect-error getValueByDataKey does not validate the output type\n          value: getValueByDataKey(item.payload, item.dataKey),\n          name: item.name\n        }));\n      });\n    } else {\n      var _getValueByDataKey;\n      // I am not quite sure why these two branches (Array vs Array of Arrays) have to behave differently - I imagine we should unify these. 3.x breaking change?\n      agg.push(getTooltipEntry({\n        tooltipEntrySettings: settings,\n        dataKey: finalDataKey,\n        payload: tooltipPayload,\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: getValueByDataKey(tooltipPayload, finalDataKey),\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        name: (_getValueByDataKey = getValueByDataKey(tooltipPayload, finalNameKey)) !== null && _getValueByDataKey !== void 0 ? _getValueByDataKey : settings === null || settings === void 0 ? void 0 : settings.name\n      }));\n    }\n    return agg;\n  }, init);\n};"], "names": [], "mappings": ";;;AAKA;AACA;AANA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;AAGvT,SAAS,UAAU,GAAG,EAAE,UAAU,EAAE,QAAQ;IAC1C,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM;QACvB,OAAO;IACT;IACA,IAAI,OAAO,aAAa,aAAa,GAAG;QACtC,OAAO,IAAI,KAAK,CAAC,YAAY,WAAW;IAC1C;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,iBAAiB,EAAE,kBAAkB;IAC5D;;;GAGC,GACD,IAAI,qBAAqB,MAAM;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,wBAAwB,CAAC,8BAA8B,aAAa,gBAAgB,aAAa,aAAa,wBAAwB;IAC/I,IAAI,eAAe,QAAQ,0BAA0B,MAAM;QACzD,OAAO;IACT;IACA,IAAI,EACF,SAAS,EACT,YAAY,EACZ,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,OAAO,EAAE;IACb,OAAO,6BAA6B,MAAM,CAAC,CAAC,KAAK;QAC/C,IAAI;QACJ,IAAI,EACF,iBAAiB,EACjB,QAAQ,EACT,GAAG;QACJ,IAAI,YAAY,gBAAgB,mBAAmB;QACnD,IAAI,SAAS,UAAU,WAAW,gBAAgB;QAClD,IAAI,eAAe,CAAC,oBAAoB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO;QAC1P,uDAAuD;QACvD,IAAI,eAAe,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,EAAE,2BAA2B;QACpH,IAAI;QACJ,IAAI,gBAAgB,QAAQ,gBAAgB,KAAK,KAAK,YAAY,OAAO,IAAI,MAAM,OAAO,CAAC,WAC3F;;;;;;KAMC,GACD,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,KACxB;;;;;;;;;;;;;KAaC,GACD,qBAAqB,QAAQ;YAC3B,iBAAiB,CAAA,GAAA,oJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,YAAY,OAAO,EAAE;QACjE,OAAO;YACL;;;;;OAKC,GACD,iBAAiB,uBAAuB,QAAQ,aAAa,cAAc;QAC7E;QACA,IAAI,MAAM,OAAO,CAAC,iBAAiB;YACjC,eAAe,OAAO,CAAC,CAAA;gBACrB,IAAI,cAAc,cAAc,cAAc,CAAC,GAAG,WAAW,CAAC,GAAG;oBAC/D,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,iLAAiL;oBACjL,OAAO;oBACP,iLAAiL;oBACjL,MAAM;gBACR;gBACA,IAAI,IAAI,CAAC,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;oBACvB,sBAAsB;oBACtB,SAAS,KAAK,OAAO;oBACrB,SAAS,KAAK,OAAO;oBACrB,uEAAuE;oBACvE,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO;oBACnD,MAAM,KAAK,IAAI;gBACjB;YACF;QACF,OAAO;YACL,IAAI;YACJ,2JAA2J;YAC3J,IAAI,IAAI,CAAC,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;gBACvB,sBAAsB;gBACtB,SAAS;gBACT,SAAS;gBACT,uEAAuE;gBACvE,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;gBACzC,uEAAuE;gBACvE,MAAM,CAAC,qBAAqB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,aAAa,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,IAAI;YACjN;QACF;QACA,OAAO;IACT,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2639, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/tooltipSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { combineAppliedNumericalValuesIncludingErrorValues, combineAppliedValues, combineAreasDomain, combineAxisDomain, combineAxisDomainWithNiceTicks, combineCategoricalDomain, combineDisplayedData, combineDomainOfStackGroups, combineDotsDomain, combineDuplicateDomain, combineGraphicalItemsData, combineGraphicalItemsSettings, combineLinesDomain, combineNiceTicks, combineNumericalDomain, combineRealScaleType, combineScaleFunction, combineStackGroups, filterGraphicalNotStackedItems, filterReferenceElements, getDomainDefinition, itemAxisPredicate, mergeDomains, selectAxisRange, selectAxisSettings, selectHasBar, selectReferenceAreas, selectReferenceDots, selectReferenceLines } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { mathSign } from '../../util/DataUtils';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { combineTooltipEventType, selectDefaultTooltipEventType, selectValidateTooltipEventTypes } from './selectTooltipEventType';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { selectTooltipSettings } from './selectTooltipSettings';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nexport var selectTooltipAxisType = state => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return 'xAxis';\n  }\n  if (layout === 'vertical') {\n    return 'yAxis';\n  }\n  if (layout === 'centric') {\n    return 'angleAxis';\n  }\n  return 'radiusAxis';\n};\nexport var selectTooltipAxisId = state => state.tooltip.settings.axisId;\nexport var selectTooltipAxis = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  return selectAxisSettings(state, axisType, axisId);\n};\nexport var selectTooltipAxisRealScaleType = createSelector([selectTooltipAxis, selectChartLayout, selectHasBar, selectChartName, selectTooltipAxisType], combineRealScaleType);\nexport var selectAllUnfilteredGraphicalItems = createSelector([state => state.graphicalItems.cartesianItems, state => state.graphicalItems.polarItems], (cartesianItems, polarItems) => [...cartesianItems, ...polarItems]);\nvar selectTooltipAxisPredicate = createSelector([selectTooltipAxisType, selectTooltipAxisId], itemAxisPredicate);\nexport var selectAllGraphicalItemsSettings = createSelector([selectAllUnfilteredGraphicalItems, selectTooltipAxis, selectTooltipAxisPredicate], combineGraphicalItemsSettings);\nexport var selectTooltipGraphicalItemsData = createSelector([selectAllGraphicalItemsSettings], combineGraphicalItemsData);\n\n/**\n * Data for tooltip always use the data with indexes set by a Brush,\n * and never accept the isPanorama flag:\n * because Tooltip never displays inside the panorama anyway\n * so we don't need to worry what would happen there.\n */\nexport var selectTooltipDisplayedData = createSelector([selectTooltipGraphicalItemsData, selectChartDataWithIndexes], combineDisplayedData);\nvar selectAllTooltipAppliedValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectAllGraphicalItemsSettings], combineAppliedValues);\nvar selectTooltipAxisDomainDefinition = createSelector([selectTooltipAxis], getDomainDefinition);\nvar selectTooltipStackGroups = createSelector([selectTooltipDisplayedData, selectAllGraphicalItemsSettings, selectStackOffsetType], combineStackGroups);\nvar selectTooltipDomainOfStackGroups = createSelector([selectTooltipStackGroups, selectChartDataWithIndexes, selectTooltipAxisType], combineDomainOfStackGroups);\nvar selectTooltipItemsSettingsExceptStacked = createSelector([selectAllGraphicalItemsSettings], filterGraphicalNotStackedItems);\nvar selectTooltipAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectTooltipItemsSettingsExceptStacked, selectTooltipAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nvar selectReferenceDotsByTooltipAxis = createSelector([selectReferenceDots, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceDotsDomain = createSelector([selectReferenceDotsByTooltipAxis, selectTooltipAxisType], combineDotsDomain);\nvar selectReferenceAreasByTooltipAxis = createSelector([selectReferenceAreas, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceAreasDomain = createSelector([selectReferenceAreasByTooltipAxis, selectTooltipAxisType], combineAreasDomain);\nvar selectReferenceLinesByTooltipAxis = createSelector([selectReferenceLines, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceLinesDomain = createSelector([selectReferenceLinesByTooltipAxis, selectTooltipAxisType], combineLinesDomain);\nvar selectTooltipReferenceElementsDomain = createSelector([selectTooltipReferenceDotsDomain, selectTooltipReferenceLinesDomain, selectTooltipReferenceAreasDomain], mergeDomains);\nvar selectTooltipNumericalDomain = createSelector([selectTooltipAxis, selectTooltipAxisDomainDefinition, selectTooltipDomainOfStackGroups, selectTooltipAllAppliedNumericalValuesIncludingErrorValues, selectTooltipReferenceElementsDomain], combineNumericalDomain);\nexport var selectTooltipAxisDomain = createSelector([selectTooltipAxis, selectChartLayout, selectTooltipDisplayedData, selectAllTooltipAppliedValues, selectStackOffsetType, selectTooltipAxisType, selectTooltipNumericalDomain], combineAxisDomain);\nvar selectTooltipNiceTicks = createSelector([selectTooltipAxisDomain, selectTooltipAxis, selectTooltipAxisRealScaleType], combineNiceTicks);\nexport var selectTooltipAxisDomainIncludingNiceTicks = createSelector([selectTooltipAxis, selectTooltipAxisDomain, selectTooltipNiceTicks, selectTooltipAxisType], combineAxisDomainWithNiceTicks);\nvar selectTooltipAxisRange = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  var isPanorama = false; // Tooltip never displays in panorama so this is safe to assume\n  return selectAxisRange(state, axisType, axisId, isPanorama);\n};\nexport var selectTooltipAxisRangeWithReverse = createSelector([selectTooltipAxis, selectTooltipAxisRange], combineAxisRangeWithReverse);\nexport var selectTooltipAxisScale = createSelector([selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisDomainIncludingNiceTicks, selectTooltipAxisRangeWithReverse], combineScaleFunction);\nvar selectTooltipDuplicateDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineDuplicateDomain);\nexport var selectTooltipCategoricalDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineCategoricalDomain);\nvar combineTicksOfTooltipAxis = (layout, axis, realScaleType, scale, range, duplicateDomain, categoricalDomain, axisType) => {\n  if (!axis) {\n    return undefined;\n  }\n  var {\n    type\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (!scale) {\n    return undefined;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range != null && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTooltipAxisTicks = createSelector([selectChartLayout, selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisScale, selectTooltipAxisRange, selectTooltipDuplicateDomain, selectTooltipCategoricalDomain, selectTooltipAxisType], combineTicksOfTooltipAxis);\nvar selectTooltipEventType = createSelector([selectDefaultTooltipEventType, selectValidateTooltipEventTypes, selectTooltipSettings], (defaultTooltipEventType, validateTooltipEventType, settings) => combineTooltipEventType(settings.shared, defaultTooltipEventType, validateTooltipEventType));\nvar selectTooltipTrigger = state => state.tooltip.settings.trigger;\nvar selectDefaultIndex = state => state.tooltip.settings.defaultIndex;\nvar selectTooltipInteractionState = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveTooltipIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectActiveLabel = createSelector([selectTooltipAxisTicks, selectActiveTooltipIndex], combineActiveLabel);\nexport var selectActiveTooltipDataKey = createSelector([selectTooltipInteractionState], tooltipInteraction => {\n  if (!tooltipInteraction) {\n    return undefined;\n  }\n  return tooltipInteraction.dataKey;\n});\nvar selectTooltipPayloadConfigurations = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipPayloadConfigurations);\nvar selectTooltipCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, selectDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveTooltipCoordinate = createSelector([selectTooltipInteractionState, selectTooltipCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  if (tooltipInteractionState !== null && tooltipInteractionState !== void 0 && tooltipInteractionState.coordinate) {\n    return tooltipInteractionState.coordinate;\n  }\n  return defaultIndexCoordinate;\n});\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => tooltipInteractionState.active);\nexport var selectActiveTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveTooltipIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, selectTooltipEventType], combineTooltipPayload);\nexport var selectActiveTooltipDataPoints = createSelector([selectActiveTooltipPayload], payload => {\n  if (payload == null) {\n    return undefined;\n  }\n  var dataPoints = payload.map(p => p.payload).filter(p => p != null);\n  return Array.from(new Set(dataPoints));\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AACO,IAAI,wBAAwB,CAAA;IACjC,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,WAAW,cAAc;QAC3B,OAAO;IACT;IACA,IAAI,WAAW,YAAY;QACzB,OAAO;IACT;IACA,IAAI,WAAW,WAAW;QACxB,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,CAAA,QAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;AAChE,IAAI,oBAAoB,CAAA;IAC7B,IAAI,WAAW,sBAAsB;IACrC,IAAI,SAAS,oBAAoB;IACjC,OAAO,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,UAAU;AAC7C;AACO,IAAI,iCAAiC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB,gKAAA,CAAA,oBAAiB;IAAE,sKAAA,CAAA,eAAY;IAAE,2KAAA,CAAA,kBAAe;IAAE;CAAsB,EAAE,sKAAA,CAAA,uBAAoB;AACtK,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,CAAA,QAAS,MAAM,cAAc,CAAC,cAAc;IAAE,CAAA,QAAS,MAAM,cAAc,CAAC,UAAU;CAAC,EAAE,CAAC,gBAAgB,aAAe;WAAI;WAAmB;KAAW;AAC1N,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAuB;CAAoB,EAAE,sKAAA,CAAA,oBAAiB;AACxG,IAAI,kCAAkC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmC;IAAmB;CAA2B,EAAE,sKAAA,CAAA,gCAA6B;AACtK,IAAI,kCAAkC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAgC,EAAE,sKAAA,CAAA,4BAAyB;AAQjH,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAiC,sKAAA,CAAA,6BAA0B;CAAC,EAAE,sKAAA,CAAA,uBAAoB;AAC1I,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA4B;IAAmB;CAAgC,EAAE,sKAAA,CAAA,uBAAoB;AACzJ,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAkB,EAAE,sKAAA,CAAA,sBAAmB;AAC/F,IAAI,2BAA2B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA4B;IAAiC,2KAAA,CAAA,wBAAqB;CAAC,EAAE,sKAAA,CAAA,qBAAkB;AACtJ,IAAI,mCAAmC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA0B,sKAAA,CAAA,6BAA0B;IAAE;CAAsB,EAAE,sKAAA,CAAA,6BAA0B;AAC/J,IAAI,0CAA0C,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAgC,EAAE,sKAAA,CAAA,iCAA8B;AAC9H,IAAI,6DAA6D,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA4B;IAAmB;IAAyC;CAAsB,EAAE,sKAAA,CAAA,oDAAiD;AAClP,IAAI,mCAAmC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,sBAAmB;IAAE;IAAuB;CAAoB,EAAE,sKAAA,CAAA,0BAAuB;AAChJ,IAAI,mCAAmC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAkC;CAAsB,EAAE,sKAAA,CAAA,oBAAiB;AAClI,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,uBAAoB;IAAE;IAAuB;CAAoB,EAAE,sKAAA,CAAA,0BAAuB;AAClJ,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmC;CAAsB,EAAE,sKAAA,CAAA,qBAAkB;AACrI,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,uBAAoB;IAAE;IAAuB;CAAoB,EAAE,sKAAA,CAAA,0BAAuB;AAClJ,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmC;CAAsB,EAAE,sKAAA,CAAA,qBAAkB;AACrI,IAAI,uCAAuC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAkC;IAAmC;CAAkC,EAAE,sKAAA,CAAA,eAAY;AAChL,IAAI,+BAA+B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB;IAAmC;IAAkC;IAA4D;CAAqC,EAAE,sKAAA,CAAA,yBAAsB;AAC7P,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB,gKAAA,CAAA,oBAAiB;IAAE;IAA4B;IAA+B,2KAAA,CAAA,wBAAqB;IAAE;IAAuB;CAA6B,EAAE,sKAAA,CAAA,oBAAiB;AACpP,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAyB;IAAmB;CAA+B,EAAE,sKAAA,CAAA,mBAAgB;AACnI,IAAI,4CAA4C,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB;IAAyB;IAAwB;CAAsB,EAAE,sKAAA,CAAA,iCAA8B;AACjM,IAAI,yBAAyB,CAAA;IAC3B,IAAI,WAAW,sBAAsB;IACrC,IAAI,SAAS,oBAAoB;IACjC,IAAI,aAAa,OAAO,+DAA+D;IACvF,OAAO,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU,QAAQ;AAClD;AACO,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB;CAAuB,EAAE,iMAAA,CAAA,8BAA2B;AAC/H,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB;IAAgC;IAA2C;CAAkC,EAAE,sKAAA,CAAA,uBAAoB;AAC1M,IAAI,+BAA+B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAA+B;IAAmB;CAAsB,EAAE,sKAAA,CAAA,yBAAsB;AAC/J,IAAI,iCAAiC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAA+B;IAAmB;CAAsB,EAAE,sKAAA,CAAA,2BAAwB;AACjL,IAAI,4BAA4B,CAAC,QAAQ,MAAM,eAAe,OAAO,OAAO,iBAAiB,mBAAmB;IAC9G,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI,EACF,IAAI,EACL,GAAG;IACJ,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAC9C,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,gBAAgB,kBAAkB,eAAe,MAAM,SAAS,GAAG,MAAM,SAAS,KAAK,IAAI;IAC/F,IAAI,SAAS,SAAS,cAAc,MAAM,SAAS,GAAG,MAAM,SAAS,KAAK,gBAAgB;IAC1F,SAAS,aAAa,eAAe,SAAS,QAAQ,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,KAAK,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,IAAI,SAAS;IAEvK,qGAAqG;IACrG,IAAI,iBAAiB,mBAAmB;QACtC,OAAO,kBAAkB,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBAC9C,YAAY,MAAM,SAAS;gBAC3B,OAAO;gBACP;gBACA;YACF,CAAC;IACH;IAEA,2EAA2E;IAC3E,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC3C,YAAY,MAAM,SAAS;YAC3B,OAAO,kBAAkB,eAAe,CAAC,MAAM,GAAG;YAClD;YACA;QACF,CAAC;AACH;AACO,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAmB;IAAgC;IAAwB;IAAwB;IAA8B;IAAgC;CAAsB,EAAE;AAChQ,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,+KAAA,CAAA,gCAA6B;IAAE,+KAAA,CAAA,kCAA+B;IAAE,8KAAA,CAAA,wBAAqB;CAAC,EAAE,CAAC,yBAAyB,0BAA0B,WAAa,CAAA,GAAA,+KAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,MAAM,EAAE,yBAAyB;AACxQ,IAAI,uBAAuB,CAAA,QAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO;AAClE,IAAI,qBAAqB,CAAA,QAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;AACrE,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,qBAAkB;IAAE;IAAwB;IAAsB;CAAmB,EAAE,oMAAA,CAAA,iCAA8B;AAClK,IAAI,2BAA2B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B;CAA2B,EAAE,+LAAA,CAAA,4BAAyB;AACpI,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAwB;CAAyB,EAAE,wLAAA,CAAA,qBAAkB;AAC7G,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA8B,EAAE,CAAA;IACtF,IAAI,CAAC,oBAAoB;QACvB,OAAO;IACT;IACA,OAAO,mBAAmB,OAAO;AACnC;AACA,IAAI,qCAAqC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,qBAAkB;IAAE;IAAwB;IAAsB;CAAmB,EAAE,yMAAA,CAAA,sCAAmC;AACnL,IAAI,yCAAyC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;IAAE,gKAAA,CAAA,oBAAiB;IAAE,kLAAA,CAAA,4BAAyB;IAAE;IAAwB;IAAoB;IAAoC,qLAAA,CAAA,+BAA4B;CAAC,EAAE,sMAAA,CAAA,mCAAgC;AACxR,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B;CAAuC,EAAE,CAAC,yBAAyB;IAC3J,IAAI,4BAA4B,QAAQ,4BAA4B,KAAK,KAAK,wBAAwB,UAAU,EAAE;QAChH,OAAO,wBAAwB,UAAU;IAC3C;IACA,OAAO;AACT;AACO,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA8B,EAAE,CAAA,0BAA2B,wBAAwB,MAAM;AACrI,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAoC;IAA0B,sKAAA,CAAA,6BAA0B;IAAE;IAAmB;IAAmB,qLAAA,CAAA,+BAA4B;IAAE;CAAuB,EAAE,2LAAA,CAAA,wBAAqB;AAC7P,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA2B,EAAE,CAAA;IACtF,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IACA,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA,IAAK,KAAK;IAC9D,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { useAppSelector } from '../hooks';\nimport { calculateActiveTickIndex, calculateTooltipPos, getActiveCoordinate, inRange } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectTooltipAxis, selectTooltipAxisTicks, selectTooltipDisplayedData } from './tooltipSelectors';\nimport { selectChartName } from './rootPropsSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nexport var useChartName = () => {\n  return useAppSelector(selectChartName);\n};\nvar pickTooltipEventType = (_state, tooltipEventType) => tooltipEventType;\nvar pickTrigger = (_state, _tooltipEventType, trigger) => trigger;\nvar pickDefaultIndex = (_state, _tooltipEventType, _trigger, defaultIndex) => defaultIndex;\nexport var selectOrderedTooltipTicks = createSelector(selectTooltipAxisTicks, ticks => sortBy(ticks, o => o.coordinate));\nexport var selectTooltipInteractionState = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectTooltipDataKey = (state, tooltipEventType, trigger) => {\n  if (tooltipEventType == null) {\n    return undefined;\n  }\n  var tooltipState = selectTooltipState(state);\n  if (tooltipEventType === 'axis') {\n    if (trigger === 'hover') {\n      return tooltipState.axisInteraction.hover.dataKey;\n    }\n    return tooltipState.axisInteraction.click.dataKey;\n  }\n  if (trigger === 'hover') {\n    return tooltipState.itemInteraction.hover.dataKey;\n  }\n  return tooltipState.itemInteraction.click.dataKey;\n};\nexport var selectTooltipPayloadConfigurations = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipPayloadConfigurations);\nexport var selectCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, pickDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveCoordinate = createSelector([selectTooltipInteractionState, selectCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  var _tooltipInteractionSt;\n  return (_tooltipInteractionSt = tooltipInteractionState.coordinate) !== null && _tooltipInteractionSt !== void 0 ? _tooltipInteractionSt : defaultIndexCoordinate;\n});\nexport var selectActiveLabel = createSelector(selectTooltipAxisTicks, selectActiveIndex, combineActiveLabel);\nexport var selectTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, pickTooltipEventType], combineTooltipPayload);\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => {\n  return {\n    isActive: tooltipInteractionState.active,\n    activeIndex: tooltipInteractionState.index\n  };\n});\nexport var combineActiveProps = (chartEvent, layout, polarViewBox, tooltipAxisType, tooltipAxisRange, tooltipTicks, orderedTooltipTicks, offset) => {\n  if (!chartEvent || !layout || !tooltipAxisType || !tooltipAxisRange || !tooltipTicks) {\n    return undefined;\n  }\n  var rangeObj = inRange(chartEvent.chartX, chartEvent.chartY, layout, polarViewBox, offset);\n  if (!rangeObj) {\n    return undefined;\n  }\n  var pos = calculateTooltipPos(rangeObj, layout);\n  var activeIndex = calculateActiveTickIndex(pos, orderedTooltipTicks, tooltipTicks, tooltipAxisType, tooltipAxisRange);\n  var activeCoordinate = getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj);\n  return {\n    activeIndex: String(activeIndex),\n    activeCoordinate\n  };\n};"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACO,IAAI,eAAe;IACxB,OAAO,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,2KAAA,CAAA,kBAAe;AACvC;AACA,IAAI,uBAAuB,CAAC,QAAQ,mBAAqB;AACzD,IAAI,cAAc,CAAC,QAAQ,mBAAmB,UAAY;AAC1D,IAAI,mBAAmB,CAAC,QAAQ,mBAAmB,UAAU,eAAiB;AACvE,IAAI,4BAA4B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,yBAAsB,EAAE,CAAA,QAAS,CAAA,GAAA,iJAAA,CAAA,UAAM,AAAD,EAAE,OAAO,CAAA,IAAK,EAAE,UAAU;AAC/G,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,qBAAkB;IAAE;IAAsB;IAAa;CAAiB,EAAE,oMAAA,CAAA,iCAA8B;AAC5J,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B,yKAAA,CAAA,6BAA0B;CAAC,EAAE,+LAAA,CAAA,4BAAyB;AAC7H,IAAI,uBAAuB,CAAC,OAAO,kBAAkB;IAC1D,IAAI,oBAAoB,MAAM;QAC5B,OAAO;IACT;IACA,IAAI,eAAe,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE;IACtC,IAAI,qBAAqB,QAAQ;QAC/B,IAAI,YAAY,SAAS;YACvB,OAAO,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;QACnD;QACA,OAAO,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;IACnD;IACA,IAAI,YAAY,SAAS;QACvB,OAAO,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;IACnD;IACA,OAAO,aAAa,eAAe,CAAC,KAAK,CAAC,OAAO;AACnD;AACO,IAAI,qCAAqC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,qBAAkB;IAAE;IAAsB;IAAa;CAAiB,EAAE,yMAAA,CAAA,sCAAmC;AACtK,IAAI,kCAAkC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;IAAE,gKAAA,CAAA,oBAAiB;IAAE,kLAAA,CAAA,4BAAyB;IAAE,yKAAA,CAAA,yBAAsB;IAAE;IAAkB;IAAoC,qLAAA,CAAA,+BAA4B;CAAC,EAAE,sMAAA,CAAA,mCAAgC;AACtR,IAAI,yBAAyB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B;CAAgC,EAAE,CAAC,yBAAyB;IAC7I,IAAI;IACJ,OAAO,CAAC,wBAAwB,wBAAwB,UAAU,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;AAC7I;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,yKAAA,CAAA,yBAAsB,EAAE,mBAAmB,wLAAA,CAAA,qBAAkB;AACpG,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAoC;IAAmB,sKAAA,CAAA,6BAA0B;IAAE,yKAAA,CAAA,oBAAiB;IAAE;IAAmB,qLAAA,CAAA,+BAA4B;IAAE;CAAqB,EAAE,2LAAA,CAAA,wBAAqB;AAC9O,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAA8B,EAAE,CAAA;IACjF,OAAO;QACL,UAAU,wBAAwB,MAAM;QACxC,aAAa,wBAAwB,KAAK;IAC5C;AACF;AACO,IAAI,qBAAqB,CAAC,YAAY,QAAQ,cAAc,iBAAiB,kBAAkB,cAAc,qBAAqB;IACvI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,cAAc;QACpF,OAAO;IACT;IACA,IAAI,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,WAAW,MAAM,EAAE,WAAW,MAAM,EAAE,QAAQ,cAAc;IACnF,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,IAAI,MAAM,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU;IACxC,IAAI,cAAc,CAAA,GAAA,qJAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,qBAAqB,cAAc,iBAAiB;IACpG,IAAI,mBAAmB,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,cAAc,aAAa;IAC9E,OAAO;QACL,aAAa,OAAO;QACpB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3127, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectActivePropsFromChartPointer.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks, selectTooltipAxisType } from './tooltipSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineActiveProps, selectOrderedTooltipTicks } from './selectors';\nimport { selectPolarViewBox } from './polarAxisSelectors';\nvar pickChartPointer = (_state, chartPointer) => chartPointer;\nexport var selectActivePropsFromChartPointer = createSelector([pickChartPointer, selectChartLayout, selectPolarViewBox, selectTooltipAxisType, selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks, selectOrderedTooltipTicks, selectChartOffsetInternal], combineActiveProps);"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,mBAAmB,CAAC,QAAQ,eAAiB;AAC1C,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAkB,gKAAA,CAAA,oBAAiB;IAAE,2KAAA,CAAA,qBAAkB;IAAE,yKAAA,CAAA,wBAAqB;IAAE,yKAAA,CAAA,oCAAiC;IAAE,yKAAA,CAAA,yBAAsB;IAAE,kKAAA,CAAA,4BAAyB;IAAE,kLAAA,CAAA,4BAAyB;CAAC,EAAE,kKAAA,CAAA,qBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/mouseEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { mouseLeaveChart, setMouseClickAxisIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { getChartPointer } from '../util/getChartPointer';\nexport var mouseClickAction = createAction('mouseClick');\nexport var mouseClickMiddleware = createListenerMiddleware();\n\n// TODO: there's a bug here when you click the chart the activeIndex resets to zero\nmouseClickMiddleware.startListening({\n  actionCreator: mouseClickAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var activeProps = selectActivePropsFromChartPointer(listenerApi.getState(), getChartPointer(mousePointer));\n    if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n      listenerApi.dispatch(setMouseClickAxisIndex({\n        activeIndex: activeProps.activeIndex,\n        activeDataKey: undefined,\n        activeCoordinate: activeProps.activeCoordinate\n      }));\n    }\n  }\n});\nexport var mouseMoveAction = createAction('mouseMove');\nexport var mouseMoveMiddleware = createListenerMiddleware();\nmouseMoveMiddleware.startListening({\n  actionCreator: mouseMoveAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    var activeProps = selectActivePropsFromChartPointer(state, getChartPointer(mousePointer));\n\n    // this functionality only applies to charts that have axes\n    if (tooltipEventType === 'axis') {\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      } else {\n        // this is needed to clear tooltip state when the mouse moves out of the inRange (svg - offset) function, but not yet out of the svg\n        listenerApi.dispatch(mouseLeaveChart());\n      }\n    }\n  }\n});"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,IAAI,mBAAmB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AACpC,IAAI,uBAAuB,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD;AAEzD,mFAAmF;AACnF,qBAAqB,cAAc,CAAC;IAClC,eAAe;IACf,QAAQ,CAAC,QAAQ;QACf,IAAI,eAAe,OAAO,OAAO;QACjC,IAAI,cAAc,CAAA,GAAA,0LAAA,CAAA,oCAAiC,AAAD,EAAE,YAAY,QAAQ,IAAI,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;QAC5F,IAAI,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW,KAAK,MAAM;YAC/F,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC1C,aAAa,YAAY,WAAW;gBACpC,eAAe;gBACf,kBAAkB,YAAY,gBAAgB;YAChD;QACF;IACF;AACF;AACO,IAAI,kBAAkB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AACnC,IAAI,sBAAsB,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD;AACxD,oBAAoB,cAAc,CAAC;IACjC,eAAe;IACf,QAAQ,CAAC,QAAQ;QACf,IAAI,eAAe,OAAO,OAAO;QACjC,IAAI,QAAQ,YAAY,QAAQ;QAChC,IAAI,mBAAmB,CAAA,GAAA,+KAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;QAClF,IAAI,cAAc,CAAA,GAAA,0LAAA,CAAA,oCAAiC,AAAD,EAAE,OAAO,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;QAE3E,2DAA2D;QAC3D,IAAI,qBAAqB,QAAQ;YAC/B,IAAI,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW,KAAK,MAAM;gBAC/F,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD,EAAE;oBACzC,aAAa,YAAY,WAAW;oBACpC,eAAe;oBACf,kBAAkB,YAAY,gBAAgB;gBAChD;YACF,OAAO;gBACL,oIAAoI;gBACpI,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD;YACrC;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3218, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/reduxDevtoolsJsonStringifyReplacer.js"], "sourcesContent": ["export function reduxDevtoolsJsonStringifyReplacer(_key, value) {\n  if (value instanceof HTMLElement) {\n    return \"HTMLElement <\".concat(value.tagName, \" class=\\\"\").concat(value.className, \"\\\">\");\n  }\n  if (value === window) {\n    return 'global.window';\n  }\n  return value;\n}"], "names": [], "mappings": ";;;AAAO,SAAS,mCAAmC,IAAI,EAAE,KAAK;IAC5D,IAAI,iBAAiB,aAAa;QAChC,OAAO,gBAAgB,MAAM,CAAC,MAAM,OAAO,EAAE,aAAa,MAAM,CAAC,MAAM,SAAS,EAAE;IACpF;IACA,IAAI,UAAU,QAAQ;QACpB,OAAO;IACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3234, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/cartesianAxisSlice.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSlice } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * Properties shared in X, Y, and Z axes\n */\n\n/**\n * These are the external props, visible for users as they set them using our public API.\n * There is all sorts of internal computed things based on these, but they will come through selectors.\n *\n * Properties shared between X and Y axes\n */\n\n/**\n * Z axis is special because it's never displayed. It controls the size of Scatter dots,\n * but it never displays ticks anywhere.\n */\n\nvar initialState = {\n  xAxis: {},\n  yAxis: {},\n  zAxis: {}\n};\n\n/**\n * This is the slice where each individual Axis element pushes its own configuration.\n * Prefer to use this one instead of axisSlice.\n */\nvar cartesianAxisSlice = createSlice({\n  name: 'cartesianAxis',\n  initialState,\n  reducers: {\n    addXAxis(state, action) {\n      state.xAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeXAxis(state, action) {\n      delete state.xAxis[action.payload.id];\n    },\n    addYAxis(state, action) {\n      state.yAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeYAxis(state, action) {\n      delete state.yAxis[action.payload.id];\n    },\n    addZAxis(state, action) {\n      state.zAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeZAxis(state, action) {\n      delete state.zAxis[action.payload.id];\n    },\n    updateYAxisWidth(state, action) {\n      var {\n        id,\n        width\n      } = action.payload;\n      if (state.yAxis[id]) {\n        state.yAxis[id] = _objectSpread(_objectSpread({}, state.yAxis[id]), {}, {\n          width\n        });\n      }\n    }\n  }\n});\nexport var {\n  addXAxis,\n  removeXAxis,\n  addYAxis,\n  removeYAxis,\n  addZAxis,\n  removeZAxis,\n  updateYAxisWidth\n} = cartesianAxisSlice.actions;\nexport var cartesianAxisReducer = cartesianAxisSlice.reducer;"], "names": [], "mappings": ";;;;;;;;;;AAKA;AACA;AANA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;AAIvT;;CAEC,GAED;;;;;CAKC,GAED;;;CAGC,GAED,IAAI,eAAe;IACjB,OAAO,CAAC;IACR,OAAO,CAAC;IACR,OAAO,CAAC;AACV;AAEA;;;CAGC,GACD,IAAI,qBAAqB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,UAAS,KAAK,EAAE,MAAM;YACpB,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAC3D;QACA,aAAY,KAAK,EAAE,MAAM;YACvB,OAAO,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC;QACvC;QACA,UAAS,KAAK,EAAE,MAAM;YACpB,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAC3D;QACA,aAAY,KAAK,EAAE,MAAM;YACvB,OAAO,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC;QACvC;QACA,UAAS,KAAK,EAAE,MAAM;YACpB,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAC3D;QACA,aAAY,KAAK,EAAE,MAAM;YACvB,OAAO,MAAM,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC;QACvC;QACA,kBAAiB,KAAK,EAAE,MAAM;YAC5B,IAAI,EACF,EAAE,EACF,KAAK,EACN,GAAG,OAAO,OAAO;YAClB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE;gBACnB,MAAM,KAAK,CAAC,GAAG,GAAG,cAAc,cAAc,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG;oBACtE;gBACF;YACF;QACF;IACF;AACF;AACO,IAAI,EACT,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,gBAAgB,EACjB,GAAG,mBAAmB,OAAO;AACvB,IAAI,uBAAuB,mBAAmB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3347, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/graphicalItemsSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * ErrorBars have lot more settings but all the others are scoped to the component itself.\n * Only some of them required to be reported to the global store because <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> need to know\n * if the error bar is contributing to extending the axis domain.\n */\n\nvar initialState = {\n  countOfBars: 0,\n  cartesianItems: [],\n  polarItems: []\n};\nvar graphicalItemsSlice = createSlice({\n  name: 'graphicalItems',\n  initialState,\n  reducers: {\n    addBar(state) {\n      state.countOfBars += 1;\n    },\n    removeBar(state) {\n      state.countOfBars -= 1;\n    },\n    addCartesianGraphicalItem(state, action) {\n      state.cartesianItems.push(castDraft(action.payload));\n    },\n    replaceCartesianGraphicalItem(state, action) {\n      var {\n        prev,\n        next\n      } = action.payload;\n      var index = current(state).cartesianItems.indexOf(castDraft(prev));\n      if (index > -1) {\n        state.cartesianItems[index] = castDraft(next);\n      }\n    },\n    removeCartesianGraphicalItem(state, action) {\n      var index = current(state).cartesianItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.cartesianItems.splice(index, 1);\n      }\n    },\n    addPolarGraphicalItem(state, action) {\n      state.polarItems.push(castDraft(action.payload));\n    },\n    removePolarGraphicalItem(state, action) {\n      var index = current(state).polarItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.polarItems.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addBar,\n  removeBar,\n  addCartesianGraphicalItem,\n  replaceCartesianGraphicalItem,\n  removeCartesianGraphicalItem,\n  addPolarGraphicalItem,\n  removePolarGraphicalItem\n} = graphicalItemsSlice.actions;\nexport var graphicalItemsReducer = graphicalItemsSlice.reducer;"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;;AAGA;;;;CAIC,GAED,IAAI,eAAe;IACjB,aAAa;IACb,gBAAgB,EAAE;IAClB,YAAY,EAAE;AAChB;AACA,IAAI,sBAAsB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACpC,MAAM;IACN;IACA,UAAU;QACR,QAAO,KAAK;YACV,MAAM,WAAW,IAAI;QACvB;QACA,WAAU,KAAK;YACb,MAAM,WAAW,IAAI;QACvB;QACA,2BAA0B,KAAK,EAAE,MAAM;YACrC,MAAM,cAAc,CAAC,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QACpD;QACA,+BAA8B,KAAK,EAAE,MAAM;YACzC,IAAI,EACF,IAAI,EACJ,IAAI,EACL,GAAG,OAAO,OAAO;YAClB,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,cAAc,CAAC,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE;YAC5D,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,cAAc,CAAC,MAAM,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE;YAC1C;QACF;QACA,8BAA6B,KAAK,EAAE,MAAM;YACxC,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,cAAc,CAAC,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;YAC1E,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,cAAc,CAAC,MAAM,CAAC,OAAO;YACrC;QACF;QACA,uBAAsB,KAAK,EAAE,MAAM;YACjC,MAAM,UAAU,CAAC,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAChD;QACA,0BAAyB,KAAK,EAAE,MAAM;YACpC,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;YACtE,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO;YACjC;QACF;IACF;AACF;AACO,IAAI,EACT,MAAM,EACN,SAAS,EACT,yBAAyB,EACzB,6BAA6B,EAC7B,4BAA4B,EAC5B,qBAAqB,EACrB,wBAAwB,EACzB,GAAG,oBAAoB,OAAO;AACxB,IAAI,wBAAwB,oBAAoB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3413, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/referenceElementsSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nvar initialState = {\n  dots: [],\n  areas: [],\n  lines: []\n};\nexport var referenceElementsSlice = createSlice({\n  name: 'referenceElements',\n  initialState,\n  reducers: {\n    addDot: (state, action) => {\n      state.dots.push(action.payload);\n    },\n    removeDot: (state, action) => {\n      var index = current(state).dots.findIndex(dot => dot === action.payload);\n      if (index !== -1) {\n        state.dots.splice(index, 1);\n      }\n    },\n    addArea: (state, action) => {\n      state.areas.push(action.payload);\n    },\n    removeArea: (state, action) => {\n      var index = current(state).areas.findIndex(area => area === action.payload);\n      if (index !== -1) {\n        state.areas.splice(index, 1);\n      }\n    },\n    addLine: (state, action) => {\n      state.lines.push(action.payload);\n    },\n    removeLine: (state, action) => {\n      var index = current(state).lines.findIndex(line => line === action.payload);\n      if (index !== -1) {\n        state.lines.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addDot,\n  removeDot,\n  addArea,\n  removeArea,\n  addLine,\n  removeLine\n} = referenceElementsSlice.actions;\nexport var referenceElementsReducer = referenceElementsSlice.reducer;"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AACA,IAAI,eAAe;IACjB,MAAM,EAAE;IACR,OAAO,EAAE;IACT,OAAO,EAAE;AACX;AACO,IAAI,yBAAyB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC9C,MAAM;IACN;IACA,UAAU;QACR,QAAQ,CAAC,OAAO;YACd,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO;QAChC;QACA,WAAW,CAAC,OAAO;YACjB,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAA,MAAO,QAAQ,OAAO,OAAO;YACvE,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO;YAC3B;QACF;QACA,SAAS,CAAC,OAAO;YACf,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO;QACjC;QACA,YAAY,CAAC,OAAO;YAClB,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,SAAS,OAAO,OAAO;YAC1E,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;YAC5B;QACF;QACA,SAAS,CAAC,OAAO;YACf,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO;QACjC;QACA,YAAY,CAAC,OAAO;YAClB,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,SAAS,OAAO,OAAO;YAC1E,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;YAC5B;QACF;IACF;AACF;AACO,IAAI,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACX,GAAG,uBAAuB,OAAO;AAC3B,IAAI,2BAA2B,uBAAuB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3470, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/brushSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * From all Brush properties, only height has a default value and will always be defined.\n * Other properties are nullable and will be computed from offsets and margins if they are not set.\n */\n\nvar initialState = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  padding: {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }\n};\nexport var brushSlice = createSlice({\n  name: 'brush',\n  initialState,\n  reducers: {\n    setBrushSettings(_state, action) {\n      if (action.payload == null) {\n        return initialState;\n      }\n      return action.payload;\n    }\n  }\n});\nexport var {\n  setBrushSettings\n} = brushSlice.actions;\nexport var brushReducer = brushSlice.reducer;"], "names": [], "mappings": ";;;;;AAAA;;AAEA;;;CAGC,GAED,IAAI,eAAe;IACjB,GAAG;IACH,GAAG;IACH,OAAO;IACP,QAAQ;IACR,SAAS;QACP,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AACO,IAAI,aAAa,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAClC,MAAM;IACN;IACA,UAAU;QACR,kBAAiB,MAAM,EAAE,MAAM;YAC7B,IAAI,OAAO,OAAO,IAAI,MAAM;gBAC1B,OAAO;YACT;YACA,OAAO,OAAO,OAAO;QACvB;IACF;AACF;AACO,IAAI,EACT,gBAAgB,EACjB,GAAG,WAAW,OAAO;AACf,IAAI,eAAe,WAAW,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3510, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/legendSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * The properties inside this state update independently of each other and quite often.\n * When selecting, never select the whole state because you are going to get\n * unnecessary re-renders. Select only the properties you need.\n *\n * This is why this state type is not exported - don't use it directly.\n */\n\nvar initialState = {\n  settings: {\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle',\n    itemSorter: 'value'\n  },\n  size: {\n    width: 0,\n    height: 0\n  },\n  payload: []\n};\nvar legendSlice = createSlice({\n  name: 'legend',\n  initialState,\n  reducers: {\n    setLegendSize(state, action) {\n      state.size.width = action.payload.width;\n      state.size.height = action.payload.height;\n    },\n    setLegendSettings(state, action) {\n      state.settings.align = action.payload.align;\n      state.settings.layout = action.payload.layout;\n      state.settings.verticalAlign = action.payload.verticalAlign;\n      state.settings.itemSorter = action.payload.itemSorter;\n    },\n    addLegendPayload(state, action) {\n      state.payload.push(castDraft(action.payload));\n    },\n    removeLegendPayload(state, action) {\n      var index = current(state).payload.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.payload.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  setLegendSize,\n  setLegendSettings,\n  addLegendPayload,\n  removeLegendPayload\n} = legendSlice.actions;\nexport var legendReducer = legendSlice.reducer;"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;;AAGA;;;;;;CAMC,GAED,IAAI,eAAe;IACjB,UAAU;QACR,QAAQ;QACR,OAAO;QACP,eAAe;QACf,YAAY;IACd;IACA,MAAM;QACJ,OAAO;QACP,QAAQ;IACV;IACA,SAAS,EAAE;AACb;AACA,IAAI,cAAc,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,eAAc,KAAK,EAAE,MAAM;YACzB,MAAM,IAAI,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YACvC,MAAM,IAAI,CAAC,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;QAC3C;QACA,mBAAkB,KAAK,EAAE,MAAM;YAC7B,MAAM,QAAQ,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAC3C,MAAM,QAAQ,CAAC,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YAC7C,MAAM,QAAQ,CAAC,aAAa,GAAG,OAAO,OAAO,CAAC,aAAa;YAC3D,MAAM,QAAQ,CAAC,UAAU,GAAG,OAAO,OAAO,CAAC,UAAU;QACvD;QACA,kBAAiB,KAAK,EAAE,MAAM;YAC5B,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAC7C;QACA,qBAAoB,KAAK,EAAE,MAAM;YAC/B,IAAI,QAAQ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;YACnE,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO;YAC9B;QACF;IACF;AACF;AACO,IAAI,EACT,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACpB,GAAG,YAAY,OAAO;AAChB,IAAI,gBAAgB,YAAY,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3571, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/rootPropsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * These are chart options that users can choose - which means they can also\n * choose to change them which should trigger a re-render.\n */\n\nexport var initialState = {\n  accessibilityLayer: true,\n  barCategoryGap: '10%',\n  barGap: 4,\n  barSize: undefined,\n  className: undefined,\n  maxBarSize: undefined,\n  stackOffset: 'none',\n  syncId: undefined,\n  syncMethod: 'index'\n};\nvar rootPropsSlice = createSlice({\n  name: 'rootProps',\n  initialState,\n  reducers: {\n    updateOptions: (state, action) => {\n      var _action$payload$barGa;\n      state.accessibilityLayer = action.payload.accessibilityLayer;\n      state.barCategoryGap = action.payload.barCategoryGap;\n      state.barGap = (_action$payload$barGa = action.payload.barGap) !== null && _action$payload$barGa !== void 0 ? _action$payload$barGa : initialState.barGap;\n      state.barSize = action.payload.barSize;\n      state.maxBarSize = action.payload.maxBarSize;\n      state.stackOffset = action.payload.stackOffset;\n      state.syncId = action.payload.syncId;\n      state.syncMethod = action.payload.syncMethod;\n      state.className = action.payload.className;\n    }\n  }\n});\nexport var rootPropsReducer = rootPropsSlice.reducer;\nexport var {\n  updateOptions\n} = rootPropsSlice.actions;"], "names": [], "mappings": ";;;;;AAAA;;AAOO,IAAI,eAAe;IACxB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,SAAS;IACT,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,YAAY;AACd;AACA,IAAI,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC/B,MAAM;IACN;IACA,UAAU;QACR,eAAe,CAAC,OAAO;YACrB,IAAI;YACJ,MAAM,kBAAkB,GAAG,OAAO,OAAO,CAAC,kBAAkB;YAC5D,MAAM,cAAc,GAAG,OAAO,OAAO,CAAC,cAAc;YACpD,MAAM,MAAM,GAAG,CAAC,wBAAwB,OAAO,OAAO,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,aAAa,MAAM;YACzJ,MAAM,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO;YACtC,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,UAAU;YAC5C,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9C,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YACpC,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,UAAU;YAC5C,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS;QAC5C;IACF;AACF;AACO,IAAI,mBAAmB,eAAe,OAAO;AAC7C,IAAI,EACT,aAAa,EACd,GAAG,eAAe,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3613, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/polarAxisSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\nvar initialState = {\n  radiusAxis: {},\n  angleAxis: {}\n};\nvar polarAxisSlice = createSlice({\n  name: 'polarAxis',\n  initialState,\n  reducers: {\n    addRadiusAxis(state, action) {\n      state.radiusAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeRadiusAxis(state, action) {\n      delete state.radiusAxis[action.payload.id];\n    },\n    addAngleAxis(state, action) {\n      state.angleAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeAngleAxis(state, action) {\n      delete state.angleAxis[action.payload.id];\n    }\n  }\n});\nexport var {\n  addRadiusAxis,\n  removeRadiusAxis,\n  addAngleAxis,\n  removeAngleAxis\n} = polarAxisSlice.actions;\nexport var polarAxisReducer = polarAxisSlice.reducer;"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AACA,IAAI,eAAe;IACjB,YAAY,CAAC;IACb,WAAW,CAAC;AACd;AACA,IAAI,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC/B,MAAM;IACN;IACA,UAAU;QACR,eAAc,KAAK,EAAE,MAAM;YACzB,MAAM,UAAU,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAChE;QACA,kBAAiB,KAAK,EAAE,MAAM;YAC5B,OAAO,MAAM,UAAU,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC;QAC5C;QACA,cAAa,KAAK,EAAE,MAAM;YACxB,MAAM,SAAS,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QAC/D;QACA,iBAAgB,KAAK,EAAE,MAAM;YAC3B,OAAO,MAAM,SAAS,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC;QAC3C;IACF;AACF;AACO,IAAI,EACT,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EAChB,GAAG,eAAe,OAAO;AACnB,IAAI,mBAAmB,eAAe,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3652, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/polarOptionsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nvar polarOptionsSlice = createSlice({\n  name: 'polarOptions',\n  initialState: null,\n  reducers: {\n    updatePolarOptions: (_state, action) => {\n      return action.payload;\n    }\n  }\n});\nexport var {\n  updatePolarOptions\n} = polarOptionsSlice.actions;\nexport var polarOptionsReducer = polarOptionsSlice.reducer;"], "names": [], "mappings": ";;;;AAAA;;AACA,IAAI,oBAAoB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAClC,MAAM;IACN,cAAc;IACd,UAAU;QACR,oBAAoB,CAAC,QAAQ;YAC3B,OAAO,OAAO,OAAO;QACvB;IACF;AACF;AACO,IAAI,EACT,kBAAkB,EACnB,GAAG,kBAAkB,OAAO;AACtB,IAAI,sBAAsB,kBAAkB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/keyboardEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setKeyboardInteraction } from './tooltipSlice';\nimport { selectTooltipAxisTicks, selectTooltipDisplayedData } from './selectors/tooltipSelectors';\nimport { selectCoordinateForDefaultIndex } from './selectors/selectors';\nimport { selectChartDirection } from './selectors/axisSelectors';\nimport { combineActiveTooltipIndex } from './selectors/combiners/combineActiveTooltipIndex';\nexport var keyDownAction = createAction('keyDown');\nexport var focusAction = createAction('focus');\nexport var keyboardEventsMiddleware = createListenerMiddleware();\nkeyboardEventsMiddleware.startListening({\n  actionCreator: keyDownAction,\n  effect: (action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    var key = action.payload;\n    if (key !== 'ArrowRight' && key !== 'ArrowLeft' && key !== 'Enter') {\n      return;\n    }\n\n    // TODO this is lacking index for charts that do not support numeric indexes\n    var currentIndex = Number(combineActiveTooltipIndex(keyboardInteraction, selectTooltipDisplayedData(state)));\n    var tooltipTicks = selectTooltipAxisTicks(state);\n    if (key === 'Enter') {\n      var _coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(keyboardInteraction.index));\n      listenerApi.dispatch(setKeyboardInteraction({\n        active: !keyboardInteraction.active,\n        activeIndex: keyboardInteraction.index,\n        activeDataKey: keyboardInteraction.dataKey,\n        activeCoordinate: _coordinate\n      }));\n      return;\n    }\n    var direction = selectChartDirection(state);\n    var directionMultiplier = direction === 'left-to-right' ? 1 : -1;\n    var movement = key === 'ArrowRight' ? 1 : -1;\n    var nextIndex = currentIndex + movement * directionMultiplier;\n    if (tooltipTicks == null || nextIndex >= tooltipTicks.length || nextIndex < 0) {\n      return;\n    }\n    var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n    listenerApi.dispatch(setKeyboardInteraction({\n      active: true,\n      activeIndex: nextIndex.toString(),\n      activeDataKey: undefined,\n      activeCoordinate: coordinate\n    }));\n  }\n});\nkeyboardEventsMiddleware.startListening({\n  actionCreator: focusAction,\n  effect: (_action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    if (keyboardInteraction.active) {\n      return;\n    }\n    if (keyboardInteraction.index == null) {\n      var nextIndex = '0';\n      var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n      listenerApi.dispatch(setKeyboardInteraction({\n        activeDataKey: undefined,\n        active: true,\n        activeIndex: nextIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,IAAI,gBAAgB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AACjC,IAAI,cAAc,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AAC/B,IAAI,2BAA2B,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD;AAC7D,yBAAyB,cAAc,CAAC;IACtC,eAAe;IACf,QAAQ,CAAC,QAAQ;QACf,IAAI,QAAQ,YAAY,QAAQ;QAChC,IAAI,6BAA6B,MAAM,SAAS,CAAC,kBAAkB,KAAK;QACxE,IAAI,CAAC,4BAA4B;YAC/B;QACF;QACA,IAAI,EACF,mBAAmB,EACpB,GAAG,MAAM,OAAO;QACjB,IAAI,MAAM,OAAO,OAAO;QACxB,IAAI,QAAQ,gBAAgB,QAAQ,eAAe,QAAQ,SAAS;YAClE;QACF;QAEA,4EAA4E;QAC5E,IAAI,eAAe,OAAO,CAAA,GAAA,+LAAA,CAAA,4BAAyB,AAAD,EAAE,qBAAqB,CAAA,GAAA,yKAAA,CAAA,6BAA0B,AAAD,EAAE;QACpG,IAAI,eAAe,CAAA,GAAA,yKAAA,CAAA,yBAAsB,AAAD,EAAE;QAC1C,IAAI,QAAQ,SAAS;YACnB,IAAI,cAAc,CAAA,GAAA,kKAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO,QAAQ,SAAS,OAAO,oBAAoB,KAAK;YAC1G,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC1C,QAAQ,CAAC,oBAAoB,MAAM;gBACnC,aAAa,oBAAoB,KAAK;gBACtC,eAAe,oBAAoB,OAAO;gBAC1C,kBAAkB;YACpB;YACA;QACF;QACA,IAAI,YAAY,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE;QACrC,IAAI,sBAAsB,cAAc,kBAAkB,IAAI,CAAC;QAC/D,IAAI,WAAW,QAAQ,eAAe,IAAI,CAAC;QAC3C,IAAI,YAAY,eAAe,WAAW;QAC1C,IAAI,gBAAgB,QAAQ,aAAa,aAAa,MAAM,IAAI,YAAY,GAAG;YAC7E;QACF;QACA,IAAI,aAAa,CAAA,GAAA,kKAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO,QAAQ,SAAS,OAAO;QAChF,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE;YAC1C,QAAQ;YACR,aAAa,UAAU,QAAQ;YAC/B,eAAe;YACf,kBAAkB;QACpB;IACF;AACF;AACA,yBAAyB,cAAc,CAAC;IACtC,eAAe;IACf,QAAQ,CAAC,SAAS;QAChB,IAAI,QAAQ,YAAY,QAAQ;QAChC,IAAI,6BAA6B,MAAM,SAAS,CAAC,kBAAkB,KAAK;QACxE,IAAI,CAAC,4BAA4B;YAC/B;QACF;QACA,IAAI,EACF,mBAAmB,EACpB,GAAG,MAAM,OAAO;QACjB,IAAI,oBAAoB,MAAM,EAAE;YAC9B;QACF;QACA,IAAI,oBAAoB,KAAK,IAAI,MAAM;YACrC,IAAI,YAAY;YAChB,IAAI,aAAa,CAAA,GAAA,kKAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO,QAAQ,SAAS,OAAO;YAChF,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC1C,eAAe;gBACf,QAAQ;gBACR,aAAa;gBACb,kBAAkB;YACpB;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3763, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/externalEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { selectActiveLabel, selectActiveTooltipCoordinate, selectActiveTooltipDataKey, selectActiveTooltipIndex, selectIsTooltipActive } from './selectors/tooltipSelectors';\nexport var externalEventAction = createAction('externalEvent');\nexport var externalEventsMiddleware = createListenerMiddleware();\nexternalEventsMiddleware.startListening({\n  actionCreator: externalEventAction,\n  effect: (action, listenerApi) => {\n    if (action.payload.handler == null) {\n      return;\n    }\n    var state = listenerApi.getState();\n    var nextState = {\n      activeCoordinate: selectActiveTooltipCoordinate(state),\n      activeDataKey: selectActiveTooltipDataKey(state),\n      activeIndex: selectActiveTooltipIndex(state),\n      activeLabel: selectActiveLabel(state),\n      activeTooltipIndex: selectActiveTooltipIndex(state),\n      isTooltipActive: selectIsTooltipActive(state)\n    };\n    action.payload.handler(nextState, action.payload.reactEvent);\n  }\n});"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,IAAI,sBAAsB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AACvC,IAAI,2BAA2B,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD;AAC7D,yBAAyB,cAAc,CAAC;IACtC,eAAe;IACf,QAAQ,CAAC,QAAQ;QACf,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI,MAAM;YAClC;QACF;QACA,IAAI,QAAQ,YAAY,QAAQ;QAChC,IAAI,YAAY;YACd,kBAAkB,CAAA,GAAA,yKAAA,CAAA,gCAA6B,AAAD,EAAE;YAChD,eAAe,CAAA,GAAA,yKAAA,CAAA,6BAA0B,AAAD,EAAE;YAC1C,aAAa,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE;YACtC,aAAa,CAAA,GAAA,yKAAA,CAAA,oBAAiB,AAAD,EAAE;YAC/B,oBAAoB,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE;YAC7C,iBAAiB,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EAAE;QACzC;QACA,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,OAAO,OAAO,CAAC,UAAU;IAC7D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3795, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/touchSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nvar selectAllTooltipPayloadConfiguration = createSelector([selectTooltipState], tooltipState => tooltipState.tooltipItemPayloads);\nexport var selectTooltipCoordinate = createSelector([selectAllTooltipPayloadConfiguration, selectTooltipPayloadSearcher, (_state, tooltipIndex, _dataKey) => tooltipIndex, (_state, _tooltipIndex, dataKey) => dataKey], (allTooltipConfigurations, tooltipPayloadSearcher, tooltipIndex, dataKey) => {\n  var mostRelevantTooltipConfiguration = allTooltipConfigurations.find(tooltipConfiguration => {\n    return tooltipConfiguration.settings.dataKey === dataKey;\n  });\n  if (mostRelevantTooltipConfiguration == null) {\n    return undefined;\n  }\n  var {\n    positions\n  } = mostRelevantTooltipConfiguration;\n  if (positions == null) {\n    return undefined;\n  }\n  // @ts-expect-error tooltipPayloadSearcher is not typed well\n  var maybePosition = tooltipPayloadSearcher(positions, tooltipIndex);\n  return maybePosition;\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,uCAAuC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,2KAAA,CAAA,qBAAkB;CAAC,EAAE,CAAA,eAAgB,aAAa,mBAAmB;AACzH,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAsC,qLAAA,CAAA,+BAA4B;IAAE,CAAC,QAAQ,cAAc,WAAa;IAAc,CAAC,QAAQ,eAAe,UAAY;CAAQ,EAAE,CAAC,0BAA0B,wBAAwB,cAAc;IACxR,IAAI,mCAAmC,yBAAyB,IAAI,CAAC,CAAA;QACnE,OAAO,qBAAqB,QAAQ,CAAC,OAAO,KAAK;IACnD;IACA,IAAI,oCAAoC,MAAM;QAC5C,OAAO;IACT;IACA,IAAI,EACF,SAAS,EACV,GAAG;IACJ,IAAI,aAAa,MAAM;QACrB,OAAO;IACT;IACA,4DAA4D;IAC5D,IAAI,gBAAgB,uBAAuB,WAAW;IACtD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3831, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/touchEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setActiveMouseOverItemIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { getChartPointer } from '../util/getChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { selectTooltipCoordinate } from './selectors/touchSelectors';\nexport var touchEventAction = createAction('touchMove');\nexport var touchEventMiddleware = createListenerMiddleware();\ntouchEventMiddleware.startListening({\n  actionCreator: touchEventAction,\n  effect: (action, listenerApi) => {\n    var touchEvent = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    if (tooltipEventType === 'axis') {\n      var activeProps = selectActivePropsFromChartPointer(state, getChartPointer({\n        clientX: touchEvent.touches[0].clientX,\n        clientY: touchEvent.touches[0].clientY,\n        currentTarget: touchEvent.currentTarget\n      }));\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      }\n    } else if (tooltipEventType === 'item') {\n      var _target$getAttribute;\n      var touch = touchEvent.touches[0];\n      var target = document.elementFromPoint(touch.clientX, touch.clientY);\n      if (!target || !target.getAttribute) {\n        return;\n      }\n      var itemIndex = target.getAttribute(DATA_ITEM_INDEX_ATTRIBUTE_NAME);\n      var dataKey = (_target$getAttribute = target.getAttribute(DATA_ITEM_DATAKEY_ATTRIBUTE_NAME)) !== null && _target$getAttribute !== void 0 ? _target$getAttribute : undefined;\n      var coordinate = selectTooltipCoordinate(listenerApi.getState(), itemIndex, dataKey);\n      listenerApi.dispatch(setActiveMouseOverItemIndex({\n        activeDataKey: dataKey,\n        activeIndex: itemIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,IAAI,mBAAmB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE;AACpC,IAAI,uBAAuB,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD;AACzD,qBAAqB,cAAc,CAAC;IAClC,eAAe;IACf,QAAQ,CAAC,QAAQ;QACf,IAAI,aAAa,OAAO,OAAO;QAC/B,IAAI,QAAQ,YAAY,QAAQ;QAChC,IAAI,mBAAmB,CAAA,GAAA,+KAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;QAClF,IAAI,qBAAqB,QAAQ;YAC/B,IAAI,cAAc,CAAA,GAAA,0LAAA,CAAA,oCAAiC,AAAD,EAAE,OAAO,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;gBACzE,SAAS,WAAW,OAAO,CAAC,EAAE,CAAC,OAAO;gBACtC,SAAS,WAAW,OAAO,CAAC,EAAE,CAAC,OAAO;gBACtC,eAAe,WAAW,aAAa;YACzC;YACA,IAAI,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW,KAAK,MAAM;gBAC/F,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD,EAAE;oBACzC,aAAa,YAAY,WAAW;oBACpC,eAAe;oBACf,kBAAkB,YAAY,gBAAgB;gBAChD;YACF;QACF,OAAO,IAAI,qBAAqB,QAAQ;YACtC,IAAI;YACJ,IAAI,QAAQ,WAAW,OAAO,CAAC,EAAE;YACjC,IAAI,SAAS,SAAS,gBAAgB,CAAC,MAAM,OAAO,EAAE,MAAM,OAAO;YACnE,IAAI,CAAC,UAAU,CAAC,OAAO,YAAY,EAAE;gBACnC;YACF;YACA,IAAI,YAAY,OAAO,YAAY,CAAC,oJAAA,CAAA,iCAA8B;YAClE,IAAI,UAAU,CAAC,uBAAuB,OAAO,YAAY,CAAC,oJAAA,CAAA,mCAAgC,CAAC,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB;YAClK,IAAI,aAAa,CAAA,GAAA,uKAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY,QAAQ,IAAI,WAAW;YAC5E,YAAY,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,8BAA2B,AAAD,EAAE;gBAC/C,eAAe;gBACf,aAAa;gBACb,kBAAkB;YACpB;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3892, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/store.js"], "sourcesContent": ["import { combineReducers, configureStore } from '@reduxjs/toolkit';\nimport { optionsReducer } from './optionsSlice';\nimport { tooltipReducer } from './tooltipSlice';\nimport { chartDataReducer } from './chartDataSlice';\nimport { chartLayoutReducer } from './layoutSlice';\nimport { mouseClickMiddleware, mouseMoveMiddleware } from './mouseEventsMiddleware';\nimport { reduxDevtoolsJsonStringifyReplacer } from './reduxDevtoolsJsonStringifyReplacer';\nimport { cartesianAxisReducer } from './cartesianAxisSlice';\nimport { graphicalItemsReducer } from './graphicalItemsSlice';\nimport { referenceElementsReducer } from './referenceElementsSlice';\nimport { brushReducer } from './brushSlice';\nimport { legendReducer } from './legendSlice';\nimport { rootPropsReducer } from './rootPropsSlice';\nimport { polarAxisReducer } from './polarAxisSlice';\nimport { polarOptionsReducer } from './polarOptionsSlice';\nimport { keyboardEventsMiddleware } from './keyboardEventsMiddleware';\nimport { externalEventsMiddleware } from './externalEventsMiddleware';\nimport { touchEventMiddleware } from './touchEventsMiddleware';\nvar rootReducer = combineReducers({\n  brush: brushReducer,\n  cartesianAxis: cartesianAxisReducer,\n  chartData: chartDataReducer,\n  graphicalItems: graphicalItemsReducer,\n  layout: chartLayoutReducer,\n  legend: legendReducer,\n  options: optionsReducer,\n  polarAxis: polarAxisReducer,\n  polarOptions: polarOptionsReducer,\n  referenceElements: referenceElementsReducer,\n  rootProps: rootPropsReducer,\n  tooltip: tooltipReducer\n});\nexport var createRechartsStore = function createRechartsStore(preloadedState) {\n  var chartName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Chart';\n  return configureStore({\n    reducer: rootReducer,\n    // redux-toolkit v1 types are unhappy with the preloadedState type. Remove the `as any` when bumping to v2\n    preloadedState: preloadedState,\n    // @ts-expect-error redux-toolkit v1 types are unhappy with the middleware array. Remove this comment when bumping to v2\n    middleware: getDefaultMiddleware => getDefaultMiddleware({\n      serializableCheck: false\n    }).concat([mouseClickMiddleware.middleware, mouseMoveMiddleware.middleware, keyboardEventsMiddleware.middleware, externalEventsMiddleware.middleware, touchEventMiddleware.middleware]),\n    devTools: {\n      serialize: {\n        replacer: reduxDevtoolsJsonStringifyReplacer\n      },\n      name: \"recharts-\".concat(chartName)\n    }\n  });\n};"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACA,IAAI,cAAc,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,OAAO,sJAAA,CAAA,eAAY;IACnB,eAAe,8JAAA,CAAA,uBAAoB;IACnC,WAAW,0JAAA,CAAA,mBAAgB;IAC3B,gBAAgB,+JAAA,CAAA,wBAAqB;IACrC,QAAQ,uJAAA,CAAA,qBAAkB;IAC1B,QAAQ,uJAAA,CAAA,gBAAa;IACrB,SAAS,wJAAA,CAAA,iBAAc;IACvB,WAAW,0JAAA,CAAA,mBAAgB;IAC3B,cAAc,6JAAA,CAAA,sBAAmB;IACjC,mBAAmB,kKAAA,CAAA,2BAAwB;IAC3C,WAAW,0JAAA,CAAA,mBAAgB;IAC3B,SAAS,wJAAA,CAAA,iBAAc;AACzB;AACO,IAAI,sBAAsB,SAAS,oBAAoB,cAAc;IAC1E,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACpF,OAAO,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;QACpB,SAAS;QACT,0GAA0G;QAC1G,gBAAgB;QAChB,wHAAwH;QACxH,YAAY,CAAA,uBAAwB,qBAAqB;gBACvD,mBAAmB;YACrB,GAAG,MAAM,CAAC;gBAAC,iKAAA,CAAA,uBAAoB,CAAC,UAAU;gBAAE,iKAAA,CAAA,sBAAmB,CAAC,UAAU;gBAAE,oKAAA,CAAA,2BAAwB,CAAC,UAAU;gBAAE,oKAAA,CAAA,2BAAwB,CAAC,UAAU;gBAAE,iKAAA,CAAA,uBAAoB,CAAC,UAAU;aAAC;QACtL,UAAU;YACR,WAAW;gBACT,UAAU,8KAAA,CAAA,qCAAkC;YAC9C;YACA,MAAM,YAAY,MAAM,CAAC;QAC3B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3974, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/RechartsStoreProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nimport { Provider } from 'react-redux';\nimport { createRechartsStore } from './store';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nexport function RechartsStoreProvider(_ref) {\n  var {\n    preloadedState,\n    children,\n    reduxStoreName\n  } = _ref;\n  var isPanorama = useIsPanorama();\n  /*\n   * Why the ref? Redux official documentation recommends to use store as a singleton,\n   * and reuse that everywhere: https://redux-toolkit.js.org/api/configureStore#basic-example\n   *\n   * Which is correct! Except that is considering deploying Redux in an app.\n   * Recharts as a library supports multiple charts on the same page.\n   * And each of these charts needs its own store independent of others!\n   *\n   * The alternative is to have everything in the store keyed by the chart id.\n   * Which would make working with everything a little bit more painful because we need the chart id everywhere.\n   */\n  var storeRef = useRef(null);\n\n  /*\n   * Panorama means that this chart is not its own chart, it's only a \"preview\"\n   * being rendered as a child of Brush.\n   * In such case, it should not have a store on its own - it should implicitly inherit\n   * whatever data is in the \"parent\" or \"root\" chart.\n   * Which here is represented by not having a Provider at all. All selectors will use the root store by default.\n   */\n  if (isPanorama) {\n    return children;\n  }\n  if (storeRef.current == null) {\n    storeRef.current = createRechartsStore(preloadedState, reduxStoreName);\n  }\n\n  // ts-expect-error React-Redux types demand that the context internal value is not null, but we have that as default.\n  var nonNullContext = RechartsReduxContext;\n  return /*#__PURE__*/React.createElement(Provider, {\n    context: nonNullContext,\n    store: storeRef.current\n  }, children);\n}"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;;;;;;;AACO,SAAS,sBAAsB,IAAI;IACxC,IAAI,EACF,cAAc,EACd,QAAQ,EACR,cAAc,EACf,GAAG;IACJ,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B;;;;;;;;;;GAUC,GACD,IAAI,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEtB;;;;;;GAMC,GACD,IAAI,YAAY;QACd,OAAO;IACT;IACA,IAAI,SAAS,OAAO,IAAI,MAAM;QAC5B,SAAS,OAAO,GAAG,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB;IACzD;IAEA,qHAAqH;IACrH,IAAI,iBAAiB,gKAAA,CAAA,uBAAoB;IACzC,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,yJAAA,CAAA,WAAQ,EAAE;QAChD,SAAS;QACT,OAAO,SAAS,OAAO;IACzB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4025, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/ReportMainChartProps.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { setChartSize, setLayout, setMargin } from './layoutSlice';\nimport { useAppDispatch } from './hooks';\n\n/**\n * \"Main\" props are props that are only accepted on the main chart,\n * as opposed to the small panorama chart inside a Brush.\n */\n\nexport function ReportMainChartProps(_ref) {\n  var {\n    layout,\n    width,\n    height,\n    margin\n  } = _ref;\n  var dispatch = useAppDispatch();\n\n  /*\n   * Skip dispatching properties in panorama chart for two reasons:\n   * 1. The root chart should be deciding on these properties, and\n   * 2. Brush reads these properties from redux store, and so they must remain stable\n   *      to avoid circular dependency and infinite re-rendering.\n   */\n  var isPanorama = useIsPanorama();\n  /*\n   * useEffect here is required to avoid the \"Cannot update a component while rendering a different component\" error.\n   * https://github.com/facebook/react/issues/18178\n   *\n   * Reported in https://github.com/recharts/recharts/issues/5514\n   */\n  useEffect(() => {\n    if (!isPanorama) {\n      dispatch(setLayout(layout));\n      dispatch(setChartSize({\n        width,\n        height\n      }));\n      dispatch(setMargin(margin));\n    }\n  }, [dispatch, isPanorama, layout, width, height, margin]);\n  return null;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAOO,SAAS,qBAAqB,IAAI;IACvC,IAAI,EACF,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACP,GAAG;IACJ,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAE5B;;;;;GAKC,GACD,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B;;;;;GAKC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY;YACf,SAAS,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;YACnB,SAAS,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE;gBACpB;gBACA;YACF;YACA,SAAS,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;QACrB;IACF,GAAG;QAAC;QAAU;QAAY;QAAQ;QAAO;QAAQ;KAAO;IACxD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4073, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/ReportChartProps.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { updateOptions } from './rootPropsSlice';\nimport { useAppDispatch } from './hooks';\nexport function ReportChartProps(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updateOptions(props));\n  }, [dispatch, props]);\n  return null;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,iBAAiB,KAAK;IACpC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE;IACzB,GAAG;QAAC;QAAU;KAAM;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4096, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectChartOffset.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nexport var selectChartOffset = createSelector([selectChartOffsetInternal], offsetInternal => {\n  if (!offsetInternal) {\n    return undefined;\n  }\n  return {\n    top: offsetInternal.top,\n    bottom: offsetInternal.bottom,\n    left: offsetInternal.left,\n    right: offsetInternal.right\n  };\n});"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,kLAAA,CAAA,4BAAyB;CAAC,EAAE,CAAA;IACzE,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IACA,OAAO;QACL,KAAK,eAAe,GAAG;QACvB,QAAQ,eAAe,MAAM;QAC7B,MAAM,eAAe,IAAI;QACzB,OAAO,eAAe,KAAK;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4120, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/selectPlotArea.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffset } from './selectChartOffset';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nexport var selectPlotArea = createSelector([selectChartOffset, selectChartWidth, selectChartHeight], (offset, chartWidth, chartHeight) => {\n  if (!offset || chartWidth == null || chartHeight == null) {\n    return undefined;\n  }\n  return {\n    x: offset.left,\n    y: offset.top,\n    width: Math.max(0, chartWidth - offset.left - offset.right),\n    height: Math.max(0, chartHeight - offset.top - offset.bottom)\n  };\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,iBAAiB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,0KAAA,CAAA,oBAAiB;IAAE,2KAAA,CAAA,mBAAgB;IAAE,2KAAA,CAAA,oBAAiB;CAAC,EAAE,CAAC,QAAQ,YAAY;IACxH,IAAI,CAAC,UAAU,cAAc,QAAQ,eAAe,MAAM;QACxD,OAAO;IACT;IACA,OAAO;QACL,GAAG,OAAO,IAAI;QACd,GAAG,OAAO,GAAG;QACb,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,OAAO,IAAI,GAAG,OAAO,KAAK;QAC1D,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,OAAO,GAAG,GAAG,OAAO,MAAM;IAC9D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/SetGraphicalItem.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { useEffect, useRef } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem, replaceCartesianGraphicalItem } from './graphicalItemsSlice';\nimport { getNormalizedStackId } from '../util/ChartUtils';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  var prevPropsRef = useRef(null);\n  useEffect(() => {\n    var settings = _objectSpread(_objectSpread({}, props), {}, {\n      stackId: getNormalizedStackId(props.stackId)\n    });\n    if (prevPropsRef.current === null) {\n      dispatch(addCartesianGraphicalItem(settings));\n    } else if (prevPropsRef.current !== settings) {\n      dispatch(replaceCartesianGraphicalItem({\n        prev: prevPropsRef.current,\n        next: settings\n      }));\n    }\n    prevPropsRef.current = settings;\n  }, [dispatch, props]);\n  useEffect(() => {\n    return () => {\n      if (prevPropsRef.current) {\n        dispatch(removeCartesianGraphicalItem(prevPropsRef.current));\n        /*\n         * Here we have to reset the ref to null because in StrictMode, the effect will run twice,\n         * but it will keep the same ref value from the first render.\n         *\n         * In browser, React will clear the ref after the first effect cleanup,\n         * so that wouldn't be an issue.\n         *\n         * In StrictMode, however, the ref is kept,\n         * and in the hook above the code checks for `prevPropsRef.current === null`\n         * which would be false so it would not dispatch the `addCartesianGraphicalItem` action again.\n         *\n         * https://github.com/recharts/recharts/issues/6022\n         */\n        prevPropsRef.current = null;\n      }\n    };\n  }, [dispatch]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}"], "names": [], "mappings": ";;;;AAKA;AACA;AACA;AACA;AARA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;AAKhT,SAAS,0BAA0B,KAAK;IAC7C,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,cAAc,cAAc,CAAC,GAAG,QAAQ,CAAC,GAAG;YACzD,SAAS,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,OAAO;QAC7C;QACA,IAAI,aAAa,OAAO,KAAK,MAAM;YACjC,SAAS,CAAA,GAAA,+JAAA,CAAA,4BAAyB,AAAD,EAAE;QACrC,OAAO,IAAI,aAAa,OAAO,KAAK,UAAU;YAC5C,SAAS,CAAA,GAAA,+JAAA,CAAA,gCAA6B,AAAD,EAAE;gBACrC,MAAM,aAAa,OAAO;gBAC1B,MAAM;YACR;QACF;QACA,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;QAAU;KAAM;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,SAAS,CAAA,GAAA,+JAAA,CAAA,+BAA4B,AAAD,EAAE,aAAa,OAAO;gBAC1D;;;;;;;;;;;;SAYC,GACD,aAAa,OAAO,GAAG;YACzB;QACF;IACF,GAAG;QAAC;KAAS;IACb,OAAO;AACT;AACO,SAAS,sBAAsB,KAAK;IACzC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,+JAAA,CAAA,wBAAqB,AAAD,EAAE;QAC/B,OAAO;YACL,SAAS,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD,EAAE;QACpC;IACF,GAAG;QAAC;QAAU;KAAM;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4264, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/SetTooltipEntrySettings.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addTooltipEntrySettings, removeTooltipEntrySettings } from './tooltipSlice';\nimport { useIsPanorama } from '../context/PanoramaContext';\nexport function SetTooltipEntrySettings(_ref) {\n  var {\n    fn,\n    args\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      // Panorama graphical items should never contribute to Tooltip payload.\n      return undefined;\n    }\n    var tooltipEntrySettings = fn(args);\n    dispatch(addTooltipEntrySettings(tooltipEntrySettings));\n    return () => {\n      dispatch(removeTooltipEntrySettings(tooltipEntrySettings));\n    };\n  }, [fn, args, dispatch, isPanorama]);\n  return null;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,wBAAwB,IAAI;IAC1C,IAAI,EACF,EAAE,EACF,IAAI,EACL,GAAG;IACJ,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,uEAAuE;YACvE,OAAO;QACT;QACA,IAAI,uBAAuB,GAAG;QAC9B,SAAS,CAAA,GAAA,wJAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC,OAAO;YACL,SAAS,CAAA,GAAA,wJAAA,CAAA,6BAA0B,AAAD,EAAE;QACtC;IACF,GAAG;QAAC;QAAI;QAAM;QAAU;KAAW;IACnC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4301, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/ReportBar.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addBar, removeBar } from './graphicalItemsSlice';\nexport var ReportBar = () => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addBar());\n    return () => {\n      dispatch(removeBar());\n    };\n  });\n  return null;\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,YAAY;IACrB,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD;QACd,OAAO;YACL,SAAS,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD;QACnB;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4324, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/barSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarSettings = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings;\nvar pickMaxBarSize = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings.maxBarSize;\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _barSettings, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\n\n/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nfunction isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, barSettings) => {\n  var _ref2, _getBandSizeOfAxis;\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nvar selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, pickMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, pickBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  if (!stackGroups || (barSettings === null || barSettings === void 0 ? void 0 : barSettings.dataKey) == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  var stack = stackedData.find(sd => sd.key === barSettings.dataKey);\n  return stack;\n};\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarSettings], (graphicalItems, barSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'bar' && barSettingsFromProps.dataKey === cgis.dataKey && barSettingsFromProps.stackId === cgis.stackId &&\n  // barSettingsFromProps.data === cgis.data && // bar doesn't support data and one is undefined and another is null and this condition breaks\n  barSettingsFromProps.stackId === cgis.stackId)) {\n    return barSettingsFromProps;\n  }\n  return undefined;\n});\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, pickBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffsetInternal, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});"], "names": [], "mappings": ";;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;;;;;AAWvT,IAAI,cAAc,CAAC,QAAQ,UAAY;AACvC,IAAI,cAAc,CAAC,QAAQ,UAAU,UAAY;AACjD,IAAI,iBAAiB,CAAC,QAAQ,UAAU,UAAU,aAAe;AACjE,IAAI,kBAAkB,CAAC,QAAQ,UAAU,UAAU,aAAa,cAAgB;AAChF,IAAI,iBAAiB,CAAC,QAAQ,UAAU,UAAU,aAAa,cAAgB,YAAY,UAAU;AACrG,IAAI,YAAY,CAAC,QAAQ,UAAU,UAAU,aAAa,cAAc,QAAU;AAClF,IAAI,aAAa,CAAC,YAAY,WAAW;IACvC,IAAI,UAAU,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;IACpE,IAAI,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QACtB,OAAO;IACT;IACA,OAAO,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,WAAW;AAC7C;AACO,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE,sKAAA,CAAA,iCAA8B;IAAE;IAAa;IAAa;CAAe,EAAE,CAAC,QAAQ,UAAU,SAAS,SAAS,aAAe,SAAS,MAAM,CAAC,CAAA;QAClN,IAAI,WAAW,cAAc;YAC3B,OAAO,EAAE,OAAO,KAAK;QACvB;QACA,OAAO,EAAE,OAAO,KAAK;IACvB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;AACjG,IAAI,uBAAuB,CAAC,OAAO,SAAS,SAAS;IACnD,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,WAAW,cAAc;QAC3B,OAAO,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,SAAS,SAAS;IACpD;IACA,OAAO,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,SAAS,SAAS;AACpD;AACO,IAAI,6BAA6B,CAAC,OAAO,SAAS;IACvD,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,WAAW,cAAc;QAC3B,OAAO,CAAA,GAAA,sKAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,SAAS;IACjD;IACA,OAAO,CAAA,GAAA,sKAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,SAAS;AACjD;AAEA;;;CAGC,GAED;;;;CAIC,GAED,SAAS,UAAU,aAAa;IAC9B,OAAO,cAAc,OAAO,IAAI,QAAQ,cAAc,OAAO,IAAI;AACnE;AACO,IAAI,qBAAqB,CAAC,SAAS,YAAY;IACpD,IAAI,eAAe,CAAC;IACpB,IAAI,cAAc,QAAQ,MAAM,CAAC;IACjC,IAAI,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI;IACrD,IAAI,eAAe,YAAY,MAAM,CAAC,CAAC,KAAK;QAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,EAAE;YACrB,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE;QACvB;QACA,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG;IACH,IAAI,kBAAkB,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAA;QACrD,IAAI,CAAC,SAAS,KAAK,GAAG;QACtB,IAAI,WAAW,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QACtC,IAAI,UAAU,WAAW,YAAY,WAAW,IAAI,CAAC,EAAE,CAAC,OAAO;QAC/D,OAAO;YACL;YACA;YACA;QACF;IACF;IACA,IAAI,oBAAoB,cAAc,GAAG,CAAC,CAAA;QACxC,IAAI,WAAW;YAAC,EAAE,OAAO;SAAC,CAAC,MAAM,CAAC,CAAA,KAAM,MAAM;QAC9C,IAAI,UAAU,WAAW,YAAY,WAAW,EAAE,OAAO;QACzD,OAAO;YACL,SAAS;YACT;YACA;QACF;IACF;IACA,OAAO;WAAI;WAAoB;KAAkB;AACnD;AACO,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAsB,2KAAA,CAAA,oBAAiB;IAAE;CAA2B,EAAE;AAC9G,IAAI,oBAAoB,CAAC,OAAO,SAAS,SAAS,YAAY;IACnE,IAAI,OAAO;IACX,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,mBAAmB,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;IAC5C,IAAI,EACF,YAAY,eAAe,EAC5B,GAAG;IACJ,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,mBAAmB;IACjE,IAAI,MAAM;IACV,IAAI,WAAW,cAAc;QAC3B,OAAO,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;QACpD,QAAQ,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;IAC9D,OAAO;QACL,OAAO,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;QACpD,QAAQ,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;IAC9D;IACA,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,OAAO,KAAK,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB,UAAU,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ;AAClM;AACA,IAAI,qBAAqB,CAAC,OAAO,SAAS,SAAS;IACjD,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,MAAM;IACV,IAAI,WAAW,cAAc;QAC3B,OAAO,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;QACpD,QAAQ,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;IAC9D,OAAO;QACL,OAAO,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;QACpD,QAAQ,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;IAC9D;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;AACjC;AACA,SAAS,gBAAgB,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;IAC7E,IAAI,MAAM,SAAS,MAAM;IACzB,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IACA,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,UAAU,GAAG;IACtD,IAAI;IACJ,IAAI,eAAe,EAAE;IAErB,iCAAiC;IACjC,8GAA8G;IAC9G,IAAI,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,GAAG;QAC5C,IAAI,UAAU;QACd,IAAI,cAAc,WAAW;QAC7B,IAAI,MAAM,SAAS,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,OAAO,IAAI,CAAC,GAAG;QACtE,OAAO,CAAC,MAAM,CAAC,IAAI;QACnB,IAAI,OAAO,UAAU;YACnB,OAAO,CAAC,MAAM,CAAC,IAAI;YACnB,aAAa;QACf;QACA,IAAI,OAAO,YAAY,cAAc,GAAG;YACtC,UAAU;YACV,eAAe;YACf,MAAM,MAAM;QACd;QACA,IAAI,SAAS,CAAC,WAAW,GAAG,IAAI,KAAK;QACrC,IAAI,OAAO;YACT,QAAQ,SAAS;YACjB,MAAM;QACR;QACA,SAAS,SAAS,MAAM,CAAC,CAAC,KAAK;YAC7B,IAAI;YACJ,IAAI,cAAc;gBAChB,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,UAAU;oBACR,QAAQ,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG;oBAClC,MAAM,UAAU,cAAc,CAAC,iBAAiB,MAAM,OAAO,MAAM,QAAQ,mBAAmB,KAAK,IAAI,iBAAiB;gBAC1H;YACF;YACA,IAAI,SAAS;mBAAI;gBAAK;aAAY;YAClC,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,QAAQ;YACzC,OAAO;QACT,GAAG;IACL,OAAO;QACL,IAAI,UAAU,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,UAAU,GAAG;QAC3D,IAAI,WAAW,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,cAAc,GAAG;YACxD,aAAa;QACf;QACA,IAAI,eAAe,CAAC,WAAW,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,UAAU,IAAI;QACvE,IAAI,eAAe,GAAG;YACpB,iBAAiB;QACnB;QACA,IAAI,OAAO,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc,KAAK,GAAG,CAAC,cAAc,cAAc;QAClF,SAAS,SAAS,MAAM,CAAC,CAAC,KAAK,OAAO,IAAM;mBAAI;gBAAK;oBACnD,SAAS,MAAM,OAAO;oBACtB,UAAU,MAAM,QAAQ;oBACxB,UAAU;wBACR,QAAQ,UAAU,CAAC,eAAe,UAAU,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI;wBAC5E;oBACF;gBACF;aAAE,EAAE;IACN;IACA,OAAO;AACT;AACO,IAAI,yBAAyB,CAAC,UAAU,kBAAkB,QAAQ,gBAAgB,aAAa,UAAU;IAC9G,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,mBAAmB;IACjE,IAAI,kBAAkB,gBAAgB,QAAQ,gBAAgB,gBAAgB,WAAW,cAAc,UAAU,UAAU;IAC3H,IAAI,gBAAgB,YAAY,mBAAmB,MAAM;QACvD,kBAAkB,gBAAgB,GAAG,CAAC,CAAA,MAAO,cAAc,cAAc,CAAC,GAAG,MAAM,CAAC,GAAG;gBACrF,UAAU,cAAc,cAAc,CAAC,GAAG,IAAI,QAAQ,GAAG,CAAC,GAAG;oBAC3D,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,cAAc;gBAC9C;YACF;IACF;IACA,OAAO;AACT;AACO,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAmB,2KAAA,CAAA,uBAAoB;IAAE,2KAAA,CAAA,eAAY;IAAE,2KAAA,CAAA,uBAAoB;IAAE;IAAmB;IAAoB;CAAe,EAAE;AACxL,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,uBAAuB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,mBAAmB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACrH,IAAI,mBAAmB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AAC9G,IAAI,oBAAoB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAuB;CAAgB,EAAE,CAAC,iBAAiB;IACxG,IAAI,mBAAmB,MAAM;QAC3B,OAAO;IACT;IACA,IAAI,WAAW,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,YAAY,OAAO,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,YAAY,OAAO;IACrH,IAAI,YAAY,MAAM;QACpB,OAAO;IACT;IACA,OAAO,SAAS,QAAQ;AAC1B;AACO,IAAI,qBAAqB,CAAC,aAAa;IAC5C,IAAI,CAAC,eAAe,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO,KAAK,MAAM;QAC3G,OAAO;IACT;IACA,IAAI,EACF,OAAO,EACR,GAAG;IACJ,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IACA,IAAI,aAAa,WAAW,CAAC,QAAQ;IACrC,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IACA,IAAI,EACF,WAAW,EACZ,GAAG;IACJ,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,IAAI,QAAQ,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,GAAG,KAAK,YAAY,OAAO;IACjE,OAAO;AACT;AACA,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iCAA8B;IAAE;CAAgB,EAAE,CAAC,gBAAgB;IACrH,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,qBAAqB,OAAO,KAAK,KAAK,OAAO,IAAI,qBAAqB,OAAO,KAAK,KAAK,OAAO,IACrJ,4IAA4I;QAC5I,qBAAqB,OAAO,KAAK,KAAK,OAAO,GAAG;QAC9C,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,0BAA0B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAsB;CAAgB,EAAE;AAC/E,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,kLAAA,CAAA,4BAAyB;IAAE;IAAsB;IAAsB;IAAkB;IAAkB;IAAmB,gKAAA,CAAA,oBAAiB;IAAE,sKAAA,CAAA,4CAAyC;IAAE;IAAoB;IAAyB;IAA+B;CAAU,EAAE,CAAC,QAAQ,OAAO,OAAO,YAAY,YAAY,KAAK,QAAQ,OAAO,UAAU,aAAa,aAAa;IAC1a,IAAI,EACF,SAAS,EACT,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,eAAe,QAAQ,OAAO,QAAQ,WAAW,gBAAgB,WAAW,cAAc,SAAS,QAAQ,SAAS,QAAQ,cAAc,QAAQ,cAAc,QAAQ,YAAY,MAAM;QAC5L,OAAO;IACT;IACA,IAAI,EACF,IAAI,EACL,GAAG;IACJ,IAAI;IACJ,IAAI,QAAQ,QAAQ,KAAK,MAAM,GAAG,GAAG;QACnC,gBAAgB;IAClB,OAAO;QACL,gBAAgB,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC,gBAAgB,eAAe;IACvH;IACA,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,OAAO,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4706, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/SetLegendPayload.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectChartLayout } from '../context/chartLayoutContext';\nimport { useAppDispatch, useAppSelector } from './hooks';\nimport { addLegendPayload, removeLegendPayload } from './legendSlice';\nvar noop = () => {};\nexport function SetLegendPayload(_ref) {\n  var {\n    legendPayload\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, isPanorama, legendPayload]);\n  return null;\n}\nexport function SetPolarLegendPayload(_ref2) {\n  var {\n    legendPayload\n  } = _ref2;\n  var dispatch = useAppDispatch();\n  var layout = useAppSelector(selectChartLayout);\n  useEffect(() => {\n    if (layout !== 'centric' && layout !== 'radial') {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, layout, legendPayload]);\n  return null;\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,OAAO,KAAO;AACX,SAAS,iBAAiB,IAAI;IACnC,IAAI,EACF,aAAa,EACd,GAAG;IACJ,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,OAAO;QACT;QACA,SAAS,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B,OAAO;YACL,SAAS,CAAA,GAAA,uJAAA,CAAA,sBAAmB,AAAD,EAAE;QAC/B;IACF,GAAG;QAAC;QAAU;QAAY;KAAc;IACxC,OAAO;AACT;AACO,SAAS,sBAAsB,KAAK;IACzC,IAAI,EACF,aAAa,EACd,GAAG;IACJ,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,gKAAA,CAAA,oBAAiB;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,aAAa,WAAW,UAAU;YAC/C,OAAO;QACT;QACA,SAAS,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B,OAAO;YACL,SAAS,CAAA,GAAA,uJAAA,CAAA,sBAAmB,AAAD,EAAE;QAC/B;IACF,GAAG;QAAC;QAAU;QAAQ;KAAc;IACpC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4763, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/lineSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeLinePoints } from '../../cartesian/Line';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickLineSettings = (_state, _xAxisId, _yAxisId, _isPanorama, lineSettings) => lineSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedLineSettings = createSelector([selectUnfilteredCartesianItems, pickLineSettings], (graphicalItems, lineSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'line' && lineSettingsFromProps.dataKey === cgis.dataKey && lineSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the lines has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Lines with the same dataKey\n     * and the same stackId and the same data but still different lines, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the lineSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return lineSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectLinePoints = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectSynchronisedLineSettings, selectBandSize, selectChartDataWithIndexesIfNotInPanorama], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, lineSettings, bandSize, _ref) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (lineSettings == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    data\n  } = lineSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeLinePoints({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  });\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,mBAAmB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACrH,IAAI,uBAAuB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,mBAAmB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACrH,IAAI,iBAAiB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAsB;IAAsB;IAAkB;CAAiB,EAAE,CAAC,QAAQ,OAAO,OAAO,YAAY;IAC1K,IAAI,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,UAAU;QACtC,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;IAC9C;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;AAC9C;AACA,IAAI,mBAAmB,CAAC,QAAQ,UAAU,UAAU,aAAa,eAAiB;AAElF;;;;;;;;CAQC,GACD,IAAI,iCAAiC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iCAA8B;IAAE;CAAiB,EAAE,CAAC,gBAAgB;IACvH,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,UAAU,sBAAsB,OAAO,KAAK,KAAK,OAAO,IAAI,sBAAsB,IAAI,KAAK,KAAK,IAAI,GAAG;QACnJ;;;;;;;KAOC,GACD,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,mBAAmB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAsB;IAAsB;IAAkB;IAAkB;IAAgC;IAAgB,sKAAA,CAAA,4CAAyC;CAAC,EAAE,CAAC,QAAQ,OAAO,OAAO,YAAY,YAAY,cAAc,UAAU;IAClT,IAAI,EACF,SAAS,EACT,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,gBAAgB,QAAQ,SAAS,QAAQ,SAAS,QAAQ,cAAc,QAAQ,cAAc,QAAQ,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,YAAY,MAAM;QAChL,OAAO;IACT;IACA,IAAI,EACF,OAAO,EACP,IAAI,EACL,GAAG;IACJ,IAAI;IACJ,IAAI,QAAQ,QAAQ,KAAK,MAAM,GAAG,GAAG;QACnC,gBAAgB;IAClB,OAAO;QACL,gBAAgB,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC,gBAAgB,eAAe;IACvH;IACA,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,OAAO,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4858, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/ReportPolarOptions.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { updatePolarOptions } from './polarOptionsSlice';\nexport function ReportPolarOptions(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updatePolarOptions(props));\n  }, [dispatch, props]);\n  return null;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,mBAAmB,KAAK;IACtC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE;IAC9B,GAAG;QAAC;QAAU;KAAM;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4881, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/polarSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { combineAppliedValues, combineAxisDomain, combineAxisDomainWithNiceTicks, combineDisplayedData, combineGraphicalItemsData, combineGraphicalItemsSettings, combineNiceTicks, combineNumericalDomain, itemAxisPredicate, selectBaseAxis, selectDomainDefinition, selectRealScaleType } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { getValueByDataKey } from '../../util/ChartUtils';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { selectStackOffsetType } from './rootPropsSelectors';\nexport var selectUnfilteredPolarItems = state => state.graphicalItems.polarItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var selectPolarItemsSettings = createSelector([selectUnfilteredPolarItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nvar selectPolarGraphicalItemsData = createSelector([selectPolarItemsSettings], combineGraphicalItemsData);\nexport var selectPolarDisplayedData = createSelector([selectPolarGraphicalItemsData, selectChartDataAndAlwaysIgnoreIndexes], combineDisplayedData);\nexport var selectPolarAppliedValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], combineAppliedValues);\nexport var selectAllPolarAppliedNumericalValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], (data, axisSettings, items) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _axisSettings$dataKey;\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: [] // polar charts do not have error bars\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n});\nvar unsupportedInPolarChart = () => undefined;\nvar selectPolarNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, unsupportedInPolarChart, selectAllPolarAppliedNumericalValues, unsupportedInPolarChart], combineNumericalDomain);\nexport var selectPolarAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectPolarDisplayedData, selectPolarAppliedValues, selectStackOffsetType, pickAxisType, selectPolarNumericalDomain], combineAxisDomain);\nexport var selectPolarNiceTicks = createSelector([selectPolarAxisDomain, selectBaseAxis, selectRealScaleType], combineNiceTicks);\nexport var selectPolarAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectPolarAxisDomain, selectPolarNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,IAAI,6BAA6B,CAAA,QAAS,MAAM,cAAc,CAAC,UAAU;AAChF,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,qKAAA,CAAA,eAAY;IAAE,mKAAA,CAAA,aAAU;CAAC,EAAE,sKAAA,CAAA,oBAAiB;AAC/E,IAAI,2BAA2B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA4B,sKAAA,CAAA,iBAAc;IAAE;CAAoB,EAAE,sKAAA,CAAA,gCAA6B;AACrJ,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;CAAyB,EAAE,sKAAA,CAAA,4BAAyB;AACjG,IAAI,2BAA2B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA+B,sKAAA,CAAA,wCAAqC;CAAC,EAAE,sKAAA,CAAA,uBAAoB;AAC1I,IAAI,2BAA2B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA0B,sKAAA,CAAA,iBAAc;IAAE;CAAyB,EAAE,sKAAA,CAAA,uBAAoB;AACxI,IAAI,uCAAuC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA0B,sKAAA,CAAA,iBAAc;IAAE;CAAyB,EAAE,CAAC,MAAM,cAAc;IAC1J,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO,KAAK,OAAO,CAAC,CAAA;YAClB,OAAO,MAAM,OAAO,CAAC,CAAA;gBACnB,IAAI;gBACJ,IAAI,iBAAiB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,CAAC,wBAAwB,aAAa,OAAO,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,KAAK,OAAO;gBAChL,OAAO;oBACL,OAAO;oBACP,aAAa,EAAE,CAAC,sCAAsC;gBACxD;YACF;QACF,GAAG,MAAM,CAAC;IACZ;IACA,IAAI,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,OAAO,KAAK,MAAM;QAC9F,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,aAAa,OAAO;gBACnD,aAAa,EAAE;YACjB,CAAC;IACH;IACA,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;YACxB,OAAO;YACP,aAAa,EAAE;QACjB,CAAC;AACH;AACA,IAAI,0BAA0B,IAAM;AACpC,IAAI,6BAA6B,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iBAAc;IAAE,sKAAA,CAAA,yBAAsB;IAAE;IAAyB;IAAsC;CAAwB,EAAE,sKAAA,CAAA,yBAAsB;AACjM,IAAI,wBAAwB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iBAAc;IAAE,gKAAA,CAAA,oBAAiB;IAAE;IAA0B;IAA0B,2KAAA,CAAA,wBAAqB;IAAE,qKAAA,CAAA,eAAY;IAAE;CAA2B,EAAE,sKAAA,CAAA,oBAAiB;AACtN,IAAI,uBAAuB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAuB,sKAAA,CAAA,iBAAc;IAAE,sKAAA,CAAA,sBAAmB;CAAC,EAAE,sKAAA,CAAA,mBAAgB;AACxH,IAAI,0CAA0C,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iBAAc;IAAE;IAAuB;IAAsB,qKAAA,CAAA,eAAY;CAAC,EAAE,sKAAA,CAAA,iCAA8B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4989, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/pieSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { computePieSectors } from '../../polar/Pie';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getTooltipNameProp, getValueByDataKey } from '../../util/ChartUtils';\nimport { selectUnfilteredPolarItems } from './polarSelectors';\nvar pickPieSettings = (_state, pieSettings) => pieSettings;\n\n// Keep stable reference to an empty array to prevent re-renders\nvar emptyArray = [];\nvar pickCells = (_state, _pieSettings, cells) => {\n  if ((cells === null || cells === void 0 ? void 0 : cells.length) === 0) {\n    return emptyArray;\n  }\n  return cells;\n};\nexport var selectDisplayedData = createSelector([selectChartDataAndAlwaysIgnoreIndexes, pickPieSettings, pickCells], (_ref, pieSettings, cells) => {\n  var {\n    chartData\n  } = _ref;\n  var displayedData;\n  if ((pieSettings === null || pieSettings === void 0 ? void 0 : pieSettings.data) != null && pieSettings.data.length > 0) {\n    displayedData = pieSettings.data;\n  } else {\n    displayedData = chartData;\n  }\n  if ((!displayedData || !displayedData.length) && cells != null) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, pieSettings.presentationProps), cell.props));\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData;\n});\nexport var selectPieLegend = createSelector([selectDisplayedData, pickPieSettings, pickCells], (displayedData, pieSettings, cells) => {\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData.map((entry, i) => {\n    var _cells$i;\n    var name = getValueByDataKey(entry, pieSettings.nameKey, pieSettings.name);\n    var color;\n    if (cells !== null && cells !== void 0 && (_cells$i = cells[i]) !== null && _cells$i !== void 0 && (_cells$i = _cells$i.props) !== null && _cells$i !== void 0 && _cells$i.fill) {\n      color = cells[i].props.fill;\n    } else if (typeof entry === 'object' && entry != null && 'fill' in entry) {\n      color = entry.fill;\n    } else {\n      color = pieSettings.fill;\n    }\n    return {\n      value: getTooltipNameProp(name, pieSettings.dataKey),\n      color,\n      payload: entry,\n      type: pieSettings.legendType\n    };\n  });\n});\nvar selectSynchronisedPieSettings = createSelector([selectUnfilteredPolarItems, pickPieSettings], (graphicalItems, pieSettingsFromProps) => {\n  if (graphicalItems.some(pgis => pgis.type === 'pie' && pieSettingsFromProps.dataKey === pgis.dataKey && pieSettingsFromProps.data === pgis.data)) {\n    return pieSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectPieSectors = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells, selectChartOffsetInternal], (displayedData, pieSettings, cells, offset) => {\n  if (pieSettings == null || displayedData == null) {\n    return undefined;\n  }\n  return computePieSectors({\n    offset,\n    pieSettings,\n    displayedData,\n    cells\n  });\n});"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,IAAI,eAAe,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAAE,OAAO;QAAG,YAAY,CAAC;QAAG,cAAc,CAAC;QAAG,UAAU,CAAC;IAAE,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AAAG;AACnL,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;;;;;AAOvT,IAAI,kBAAkB,CAAC,QAAQ,cAAgB;AAE/C,gEAAgE;AAChE,IAAI,aAAa,EAAE;AACnB,IAAI,YAAY,CAAC,QAAQ,cAAc;IACrC,IAAI,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG;QACtE,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,wCAAqC;IAAE;IAAiB;CAAU,EAAE,CAAC,MAAM,aAAa;IACvI,IAAI,EACF,SAAS,EACV,GAAG;IACJ,IAAI;IACJ,IAAI,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,IAAI,KAAK,QAAQ,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;QACvH,gBAAgB,YAAY,IAAI;IAClC,OAAO;QACL,gBAAgB;IAClB;IACA,IAAI,CAAC,CAAC,iBAAiB,CAAC,cAAc,MAAM,KAAK,SAAS,MAAM;QAC9D,gBAAgB,MAAM,GAAG,CAAC,CAAA,OAAQ,cAAc,cAAc,CAAC,GAAG,YAAY,iBAAiB,GAAG,KAAK,KAAK;IAC9G;IACA,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB;IAAiB;CAAU,EAAE,CAAC,eAAe,aAAa;IAC1H,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,OAAO,cAAc,GAAG,CAAC,CAAC,OAAO;QAC/B,IAAI;QACJ,IAAI,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY,OAAO,EAAE,YAAY,IAAI;QACzE,IAAI;QACJ,IAAI,UAAU,QAAQ,UAAU,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC,EAAE,MAAM,QAAQ,aAAa,KAAK,KAAK,CAAC,WAAW,SAAS,KAAK,MAAM,QAAQ,aAAa,KAAK,KAAK,SAAS,IAAI,EAAE;YAC/K,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI;QAC7B,OAAO,IAAI,OAAO,UAAU,YAAY,SAAS,QAAQ,UAAU,OAAO;YACxE,QAAQ,MAAM,IAAI;QACpB,OAAO;YACL,QAAQ,YAAY,IAAI;QAC1B;QACA,OAAO;YACL,OAAO,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,YAAY,OAAO;YACnD;YACA,SAAS;YACT,MAAM,YAAY,UAAU;QAC9B;IACF;AACF;AACA,IAAI,gCAAgC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,uKAAA,CAAA,6BAA0B;IAAE;CAAgB,EAAE,CAAC,gBAAgB;IACjH,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,qBAAqB,OAAO,KAAK,KAAK,OAAO,IAAI,qBAAqB,IAAI,KAAK,KAAK,IAAI,GAAG;QAChJ,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,mBAAmB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAAqB;IAA+B;IAAW,kLAAA,CAAA,4BAAyB;CAAC,EAAE,CAAC,eAAe,aAAa,OAAO;IAC3K,IAAI,eAAe,QAAQ,iBAAiB,MAAM;QAChD,OAAO;IACT;IACA,OAAO,CAAA,GAAA,+IAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5134, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/areaSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeArea } from '../../cartesian/Area';\nimport { selectAxisWithScale, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { getBandSizeOfAxis, getNormalizedStackId, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar selectGraphicalItemStackedData = (state, xAxisId, yAxisId, isPanorama, areaSettings) => {\n  var _stackGroups$stackId;\n  var layout = selectChartLayout(state);\n  var isXAxisCategorical = isCategoricalAxis(layout, 'xAxis');\n  var stackGroups;\n  if (isXAxisCategorical) {\n    stackGroups = selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  } else {\n    stackGroups = selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n  }\n  if (stackGroups == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    stackId\n  } = areaSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var groups = (_stackGroups$stackId = stackGroups[stackId]) === null || _stackGroups$stackId === void 0 ? void 0 : _stackGroups$stackId.stackedData;\n  return groups === null || groups === void 0 ? void 0 : groups.find(v => v.key === dataKey);\n};\nvar pickAreaSettings = (_state, _xAxisId, _yAxisId, _isPanorama, areaSettings) => areaSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedAreaSettings = createSelector([selectUnfilteredCartesianItems, pickAreaSettings], (graphicalItems, areaSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'area' && areaSettingsFromProps.dataKey === cgis.dataKey && getNormalizedStackId(areaSettingsFromProps.stackId) === cgis.stackId && areaSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the areas has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Areas with the same dataKey\n     * and the same stackId and the same data but still different areas, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the areaSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return areaSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectArea = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectGraphicalItemStackedData, selectChartDataWithIndexesIfNotInPanorama, selectBandSize, selectSynchronisedAreaSettings], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, stackedData, _ref, bandSize, areaSettings) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (areaSettings == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = areaSettings;\n  var displayedData;\n  if (data && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n\n  // Where is this supposed to come from? No charts have that as a prop.\n  var chartBaseValue = undefined;\n  return computeArea({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataStartIndex,\n    areaSettings,\n    stackedData,\n    displayedData,\n    chartBaseValue,\n    bandSize\n  });\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,mBAAmB,CAAC,OAAO,SAAS,UAAU,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACrH,IAAI,uBAAuB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AAClH,IAAI,mBAAmB,CAAC,OAAO,UAAU,SAAS,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACrH,IAAI,iBAAiB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAsB;IAAsB;IAAkB;CAAiB,EAAE,CAAC,QAAQ,OAAO,OAAO,YAAY;IAC1K,IAAI,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,UAAU;QACtC,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;IAC9C;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;AAC9C;AACA,IAAI,iCAAiC,CAAC,OAAO,SAAS,SAAS,YAAY;IACzE,IAAI;IACJ,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,IAAI,qBAAqB,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IACnD,IAAI;IACJ,IAAI,oBAAoB;QACtB,cAAc,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,SAAS,SAAS;IAC3D,OAAO;QACL,cAAc,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,SAAS,SAAS;IAC3D;IACA,IAAI,eAAe,MAAM;QACvB,OAAO;IACT;IACA,IAAI,EACF,OAAO,EACP,OAAO,EACR,GAAG;IACJ,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IACA,IAAI,SAAS,CAAC,uBAAuB,WAAW,CAAC,QAAQ,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,WAAW;IAClJ,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;AACpF;AACA,IAAI,mBAAmB,CAAC,QAAQ,UAAU,UAAU,aAAa,eAAiB;AAElF;;;;;;;;CAQC,GACD,IAAI,iCAAiC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iCAA8B;IAAE;CAAiB,EAAE,CAAC,gBAAgB;IACvH,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,UAAU,sBAAsB,OAAO,KAAK,KAAK,OAAO,IAAI,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,sBAAsB,OAAO,MAAM,KAAK,OAAO,IAAI,sBAAsB,IAAI,KAAK,KAAK,IAAI,GAAG;QAC3N;;;;;;;KAOC,GACD,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,aAAa,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,gKAAA,CAAA,oBAAiB;IAAE;IAAsB;IAAsB;IAAkB;IAAkB;IAAgC,sKAAA,CAAA,4CAAyC;IAAE;IAAgB;CAA+B,EAAE,CAAC,QAAQ,OAAO,OAAO,YAAY,YAAY,aAAa,MAAM,UAAU;IACjV,IAAI,EACF,SAAS,EACT,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,gBAAgB,QAAQ,WAAW,gBAAgB,WAAW,cAAc,SAAS,QAAQ,SAAS,QAAQ,cAAc,QAAQ,cAAc,QAAQ,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,YAAY,MAAM;QACpO,OAAO;IACT;IACA,IAAI,EACF,IAAI,EACL,GAAG;IACJ,IAAI;IACJ,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC3B,gBAAgB;IAClB,OAAO;QACL,gBAAgB,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC,gBAAgB,eAAe;IACvH;IACA,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IAEA,sEAAsE;IACtE,IAAI,iBAAiB;IACrB,OAAO,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/node_modules/recharts/es6/state/selectors/scatterSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeScatterPoints } from '../../cartesian/Scatter';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectAxisWithScale, selectZAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectZAxis = (state, _xAxisId, _yAxisId, zAxisId) => selectZAxisWithScale(state, 'zAxis', zAxisId, false);\nvar pickScatterSettings = (_state, _xAxisId, _yAxisId, _zAxisId, scatterSettings) => scatterSettings;\nvar pickCells = (_state, _xAxisId, _yAxisId, _zAxisId, _scatterSettings, cells) => cells;\nvar scatterChartDataSelector = (state, xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectChartDataWithIndexesIfNotInPanorama(state, xAxisId, yAxisId, isPanorama);\nvar selectSynchronisedScatterSettings = createSelector([selectUnfilteredCartesianItems, pickScatterSettings], (graphicalItems, scatterSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'scatter' && scatterSettingsFromProps.dataKey === cgis.dataKey && scatterSettingsFromProps.data === cgis.data)) {\n    return scatterSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectScatterPoints = createSelector([scatterChartDataSelector, selectXAxisWithScale, selectXAxisTicks, selectYAxisWithScale, selectYAxisTicks, selectZAxis, selectSynchronisedScatterSettings, pickCells], (_ref, xAxis, xAxisTicks, yAxis, yAxisTicks, zAxis, scatterSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (scatterSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((scatterSettings === null || scatterSettings === void 0 ? void 0 : scatterSettings.data) != null && scatterSettings.data.length > 0) {\n    displayedData = scatterSettings.data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || (xAxisTicks === null || xAxisTicks === void 0 ? void 0 : xAxisTicks.length) === 0 || (yAxisTicks === null || yAxisTicks === void 0 ? void 0 : yAxisTicks.length) === 0) {\n    return undefined;\n  }\n  return computeScatterPoints({\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  });\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,UAAU,kBAAkB,QAAQ,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AACtJ,IAAI,mBAAmB,CAAC,OAAO,SAAS,UAAU,UAAU,kBAAkB,QAAQ,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACzJ,IAAI,uBAAuB,CAAC,OAAO,UAAU,SAAS,UAAU,kBAAkB,QAAQ,aAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,SAAS;AACtJ,IAAI,mBAAmB,CAAC,OAAO,UAAU,SAAS,UAAU,kBAAkB,QAAQ,aAAe,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,SAAS,SAAS;AACzJ,IAAI,cAAc,CAAC,OAAO,UAAU,UAAU,UAAY,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,SAAS,SAAS;AACxG,IAAI,sBAAsB,CAAC,QAAQ,UAAU,UAAU,UAAU,kBAAoB;AACrF,IAAI,YAAY,CAAC,QAAQ,UAAU,UAAU,UAAU,kBAAkB,QAAU;AACnF,IAAI,2BAA2B,CAAC,OAAO,SAAS,SAAS,UAAU,kBAAkB,QAAQ,aAAe,CAAA,GAAA,sKAAA,CAAA,4CAAyC,AAAD,EAAE,OAAO,SAAS,SAAS;AAC/K,IAAI,oCAAoC,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC,sKAAA,CAAA,iCAA8B;IAAE;CAAoB,EAAE,CAAC,gBAAgB;IAC7H,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,yBAAyB,OAAO,KAAK,KAAK,OAAO,IAAI,yBAAyB,IAAI,KAAK,KAAK,IAAI,GAAG;QAC5J,OAAO;IACT;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAAC;IAA0B;IAAsB;IAAkB;IAAsB;IAAkB;IAAa;IAAmC;CAAU,EAAE,CAAC,MAAM,OAAO,YAAY,OAAO,YAAY,OAAO,iBAAiB;IAC1R,IAAI,EACF,SAAS,EACT,cAAc,EACd,YAAY,EACb,GAAG;IACJ,IAAI,mBAAmB,MAAM;QAC3B,OAAO;IACT;IACA,IAAI;IACJ,IAAI,CAAC,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,IAAI,KAAK,QAAQ,gBAAgB,IAAI,CAAC,MAAM,GAAG,GAAG;QACvI,gBAAgB,gBAAgB,IAAI;IACtC,OAAO;QACL,gBAAgB,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC,gBAAgB,eAAe;IACvH;IACA,IAAI,iBAAiB,QAAQ,SAAS,QAAQ,SAAS,QAAQ,cAAc,QAAQ,cAAc,QAAQ,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM,KAAK,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM,GAAG;QACjR,OAAO;IACT;IACA,OAAO,CAAA,GAAA,uJAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}]}