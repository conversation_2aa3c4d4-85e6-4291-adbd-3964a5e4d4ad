{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4309aa17._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_677a64d5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jJOfLRavQepizs4yWvQTfpSefxa5PmTIaoXdCYuDGsc=", "__NEXT_PREVIEW_MODE_ID": "084e617ef5b6c97473638d713f931643", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5b300d7d6b590f1b8f27921b04444ae248273003ccf4d005196bc2882985f1ee", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50451c71c2da0f0bb77e245a3370692f9f62489e9b472b729e46795ca97286d2"}}}, "sortedMiddleware": ["/"], "functions": {}}