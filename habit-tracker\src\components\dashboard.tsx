'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Calendar,
  Target,
  TrendingUp,
  Heart,
  Settings,
  Plus,
  BarChart3,
  Flame,
  Award,
  Moon,
  Sun
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { ThemeSelectorButton } from './theme-selector';
import { HabitManagement } from './habit-management';
import { Analytics } from './analytics';
import { MoodTracking } from './mood-tracking';
import { Settings as SettingsPanel } from './settings';

// Mock data for demonstration
const mockHabits = [
  { id: 1, name: 'Drink Water', streak: 7, completed: true, category: 'Health' },
  { id: 2, name: 'Exercise', streak: 3, completed: false, category: 'Fitness' },
  { id: 3, name: 'Read', streak: 12, completed: true, category: 'Learning' },
  { id: 4, name: 'Meditate', streak: 5, completed: true, category: 'Wellness' },
];

const mockMoodData = [
  { date: '2024-01-15', mood: 4, habits: 3 },
  { date: '2024-01-16', mood: 5, habits: 4 },
  { date: '2024-01-17', mood: 3, habits: 2 },
  { date: '2024-01-18', mood: 4, habits: 4 },
  { date: '2024-01-19', mood: 5, habits: 4 },
];

export function Dashboard() {
  const { theme, setTheme } = useTheme();
  const [selectedTab, setSelectedTab] = useState('dashboard');

  const completedToday = mockHabits.filter(h => h.completed).length;
  const totalHabits = mockHabits.length;
  const completionRate = Math.round((completedToday / totalHabits) * 100);
  const longestStreak = Math.max(...mockHabits.map(h => h.streak));

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'habits', label: 'Habits', icon: Target },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'mood', label: 'Mood', icon: Heart },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Flame className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold text-foreground">
                  HabitFlow
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeSelectorButton />
              <button
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="p-2 rounded-lg hover:bg-accent transition-colors"
                title="Toggle Dark Mode"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </button>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Create Habit</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      selectedTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-accent text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedTab === 'dashboard' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Today's Progress</p>
                        <p className="text-2xl font-bold text-foreground">
                          {completedToday}/{totalHabits}
                        </p>
                        <p className="text-sm text-muted-foreground">{completionRate}% Complete</p>
                      </div>
                      <Target className="h-8 w-8 text-primary" />
                    </div>
                  </div>

                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Longest Streak</p>
                        <p className="text-2xl font-bold text-foreground">{longestStreak}</p>
                        <p className="text-sm text-muted-foreground">Days</p>
                      </div>
                      <Flame className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>

                  <div className="bg-card p-6 rounded-lg border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Achievements</p>
                        <p className="text-2xl font-bold text-foreground">12</p>
                        <p className="text-sm text-muted-foreground">Unlocked</p>
                      </div>
                      <Award className="h-8 w-8 text-yellow-500" />
                    </div>
                  </div>
                </div>

                {/* Today's Habits */}
                <div className="bg-card p-6 rounded-lg border border-border">
                  <h2 className="text-xl font-semibold mb-4">Today's Habits</h2>
                  <div className="space-y-3">
                    {mockHabits.map((habit) => (
                      <div
                        key={habit.id}
                        className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                              habit.completed
                                ? 'bg-primary border-primary'
                                : 'border-muted-foreground'
                            }`}
                          >
                            {habit.completed && (
                              <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{habit.name}</p>
                            <p className="text-sm text-muted-foreground">{habit.category}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Flame className="h-4 w-4 text-orange-500" />
                          <span className="text-sm font-medium">{habit.streak}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-card p-6 rounded-lg border border-border">
                  <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Plus className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Add Habit</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Heart className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Track Mood</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <TrendingUp className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">View Analytics</span>
                    </button>
                    <button className="p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center">
                      <Calendar className="h-6 w-6 mx-auto mb-2 text-primary" />
                      <span className="text-sm font-medium">Calendar</span>
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {selectedTab === 'habits' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <HabitManagement />
              </motion.div>
            )}

            {selectedTab === 'analytics' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Analytics />
              </motion.div>
            )}

            {selectedTab === 'mood' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MoodTracking />
              </motion.div>
            )}

            {selectedTab === 'settings' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <SettingsPanel />
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
