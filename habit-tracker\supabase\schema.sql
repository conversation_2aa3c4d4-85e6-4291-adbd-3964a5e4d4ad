-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
CREATE TYPE habit_frequency AS ENUM ('daily', 'weekly', 'weekdays', 'weekends', 'custom');
CREATE TYPE mood_level AS ENUM ('terrible', 'poor', 'okay', 'good', 'excellent');
CREATE TYPE notification_type AS ENUM ('push', 'email', 'sms');
CREATE TYPE achievement_type AS ENUM ('streak', 'completion', 'consistency', 'milestone');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    preferences JSONB DEFAULT '{
        "theme": "light",
        "language": "en",
        "notifications": {
            "push": true,
            "email": false,
            "sound": true,
            "reminders": true,
            "achievements": true,
            "weeklyReport": true
        },
        "privacy": {
            "analytics": true,
            "crashReports": true,
            "dataSharing": false
        }
    }'::jsonb
);

-- Habit categories
CREATE TABLE public.habit_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    color TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habits table
CREATE TABLE public.habits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.habit_categories(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT DEFAULT 'target',
    color TEXT DEFAULT '#3b82f6',
    frequency habit_frequency DEFAULT 'daily',
    target_value INTEGER DEFAULT 1,
    unit TEXT DEFAULT 'times',
    reminder_time TIME,
    reminder_enabled BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    custom_frequency JSONB, -- For custom frequency patterns
    tags TEXT[] DEFAULT '{}',
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    estimated_duration INTEGER, -- in minutes
    CONSTRAINT habits_name_user_unique UNIQUE (user_id, name)
);

-- Habit completions/logs
CREATE TABLE public.habit_completions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    habit_id UUID REFERENCES public.habits(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    value INTEGER DEFAULT 1,
    notes TEXT,
    mood_before INTEGER CHECK (mood_before >= 1 AND mood_before <= 5),
    mood_after INTEGER CHECK (mood_after >= 1 AND mood_after <= 5),
    location TEXT,
    weather TEXT,
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mood entries
CREATE TABLE public.mood_entries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    mood mood_level NOT NULL,
    mood_value INTEGER NOT NULL CHECK (mood_value >= 1 AND mood_value <= 5),
    notes TEXT,
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 5),
    stress_level INTEGER CHECK (stress_level >= 1 AND stress_level <= 5),
    sleep_quality INTEGER CHECK (sleep_quality >= 1 AND sleep_level <= 5),
    weather TEXT,
    location TEXT,
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    recorded_for_date DATE DEFAULT CURRENT_DATE
);

-- Achievements
CREATE TABLE public.achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    type achievement_type NOT NULL,
    criteria JSONB NOT NULL, -- Conditions for unlocking
    points INTEGER DEFAULT 0,
    rarity TEXT DEFAULT 'common', -- common, rare, epic, legendary
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements
CREATE TABLE public.user_achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    achievement_id UUID REFERENCES public.achievements(id) ON DELETE CASCADE NOT NULL,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress JSONB DEFAULT '{}',
    UNIQUE(user_id, achievement_id)
);

-- Habit streaks (calculated and cached)
CREATE TABLE public.habit_streaks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    habit_id UUID REFERENCES public.habits(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_completed_date DATE,
    streak_start_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(habit_id, user_id)
);

-- Notifications
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type notification_type NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habit stacks (habit chains)
CREATE TABLE public.habit_stacks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habit stack items
CREATE TABLE public.habit_stack_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    stack_id UUID REFERENCES public.habit_stacks(id) ON DELETE CASCADE NOT NULL,
    habit_id UUID REFERENCES public.habits(id) ON DELETE CASCADE NOT NULL,
    order_index INTEGER NOT NULL,
    is_required BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(stack_id, habit_id),
    UNIQUE(stack_id, order_index)
);

-- Analytics cache for performance
CREATE TABLE public.analytics_cache (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    metric_type TEXT NOT NULL,
    time_period TEXT NOT NULL, -- daily, weekly, monthly, yearly
    date_key DATE NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, metric_type, time_period, date_key)
);

-- Create indexes for performance
CREATE INDEX idx_habits_user_id ON public.habits(user_id);
CREATE INDEX idx_habits_active ON public.habits(user_id, is_active);
CREATE INDEX idx_habit_completions_habit_user ON public.habit_completions(habit_id, user_id);
CREATE INDEX idx_habit_completions_date ON public.habit_completions(user_id, completed_at);
CREATE INDEX idx_mood_entries_user_date ON public.mood_entries(user_id, recorded_for_date);
CREATE INDEX idx_habit_streaks_user ON public.habit_streaks(user_id);
CREATE INDEX idx_notifications_user_scheduled ON public.notifications(user_id, scheduled_for);
CREATE INDEX idx_analytics_cache_lookup ON public.analytics_cache(user_id, metric_type, time_period, date_key);

-- Row Level Security (RLS) policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.habits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.habit_completions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mood_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.habit_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.habit_stacks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.habit_stack_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_cache ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can manage own habits" ON public.habits FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own completions" ON public.habit_completions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own mood entries" ON public.mood_entries FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own achievements" ON public.user_achievements FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own streaks" ON public.habit_streaks FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own notifications" ON public.notifications FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own stacks" ON public.habit_stacks FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own stack items" ON public.habit_stack_items FOR ALL USING (
    auth.uid() = (SELECT user_id FROM public.habit_stacks WHERE id = stack_id)
);
CREATE POLICY "Users can view own analytics" ON public.analytics_cache FOR ALL USING (auth.uid() = user_id);

-- Public read access for categories and achievements
CREATE POLICY "Anyone can view categories" ON public.habit_categories FOR SELECT USING (true);
CREATE POLICY "Anyone can view achievements" ON public.achievements FOR SELECT USING (true);

-- Functions and triggers for automatic updates
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'avatar_url');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update streak data
CREATE OR REPLACE FUNCTION public.update_habit_streak(habit_uuid UUID, user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    last_completion_date DATE;
    current_streak_count INTEGER := 0;
    longest_streak_count INTEGER := 0;
    streak_start DATE;
BEGIN
    -- Get the most recent completion
    SELECT DATE(completed_at) INTO last_completion_date
    FROM public.habit_completions
    WHERE habit_id = habit_uuid AND user_id = user_uuid
    ORDER BY completed_at DESC
    LIMIT 1;

    -- Calculate current streak
    IF last_completion_date IS NOT NULL THEN
        -- Calculate streak logic here (simplified)
        current_streak_count := 1; -- This would be more complex in reality
        longest_streak_count := current_streak_count;
        streak_start := last_completion_date;
    END IF;

    -- Upsert streak record
    INSERT INTO public.habit_streaks (habit_id, user_id, current_streak, longest_streak, last_completed_date, streak_start_date, updated_at)
    VALUES (habit_uuid, user_uuid, current_streak_count, longest_streak_count, last_completion_date, streak_start, NOW())
    ON CONFLICT (habit_id, user_id)
    DO UPDATE SET
        current_streak = EXCLUDED.current_streak,
        longest_streak = GREATEST(habit_streaks.longest_streak, EXCLUDED.longest_streak),
        last_completed_date = EXCLUDED.last_completed_date,
        streak_start_date = EXCLUDED.streak_start_date,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update streaks on completion
CREATE OR REPLACE FUNCTION public.handle_habit_completion()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM public.update_habit_streak(NEW.habit_id, NEW.user_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_habit_completion
    AFTER INSERT ON public.habit_completions
    FOR EACH ROW EXECUTE FUNCTION public.handle_habit_completion();

-- Insert default categories
INSERT INTO public.habit_categories (name, icon, color, description) VALUES
('Health', 'heart', '#ef4444', 'Physical and mental health habits'),
('Fitness', 'dumbbell', '#f97316', 'Exercise and physical activity'),
('Learning', 'book', '#3b82f6', 'Education and skill development'),
('Wellness', 'star', '#8b5cf6', 'Mental wellness and self-care'),
('Productivity', 'zap', '#eab308', 'Work and productivity habits'),
('Lifestyle', 'coffee', '#10b981', 'Daily life and routine habits'),
('Mindfulness', 'moon', '#6366f1', 'Meditation and mindfulness'),
('Social', 'users', '#ec4899', 'Relationships and social activities'),
('Creative', 'palette', '#f59e0b', 'Creative and artistic pursuits'),
('Financial', 'dollar-sign', '#059669', 'Money and financial habits');

-- Insert sample achievements
INSERT INTO public.achievements (name, description, icon, type, criteria, points, rarity) VALUES
('First Step', 'Complete your first habit', 'star', 'milestone', '{"completions": 1}', 10, 'common'),
('Week Warrior', 'Maintain a 7-day streak', 'flame', 'streak', '{"streak_days": 7}', 50, 'common'),
('Month Master', 'Maintain a 30-day streak', 'crown', 'streak', '{"streak_days": 30}', 200, 'rare'),
('Perfectionist', 'Complete all habits for a day', 'check-circle', 'completion', '{"daily_completion": 100}', 25, 'common'),
('Habit Collector', 'Create 10 different habits', 'collection', 'milestone', '{"total_habits": 10}', 100, 'rare'),
('Consistency King', 'Complete habits for 100 days total', 'calendar', 'consistency', '{"total_completion_days": 100}', 500, 'epic'),
('Mood Tracker', 'Log your mood for 30 days', 'smile', 'milestone', '{"mood_entries": 30}', 150, 'rare'),
('Early Bird', 'Complete morning habits before 8 AM for 7 days', 'sunrise', 'consistency', '{"morning_completions": 7}', 75, 'common'),
('Night Owl', 'Complete evening habits after 8 PM for 7 days', 'moon', 'consistency', '{"evening_completions": 7}', 75, 'common'),
('Legendary', 'Maintain a 365-day streak', 'trophy', 'streak', '{"streak_days": 365}', 2000, 'legendary');
