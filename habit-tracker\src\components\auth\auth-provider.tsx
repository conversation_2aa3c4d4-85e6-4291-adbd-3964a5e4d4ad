'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { localStorageAPI, User, Profile } from '@/lib/local-storage';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signOut: () => Promise<any>;
  updateProfile: (updates: Partial<Profile>) => Promise<any>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize local storage and load user data
  useEffect(() => {
    localStorageAPI.initialize();

    const storedUser = localStorageAPI.getUser();
    const storedProfile = localStorageAPI.getProfile();

    setUser(storedUser);
    setProfile(storedProfile);
    setLoading(false);
  }, []);

  const refreshProfile = async () => {
    const storedProfile = localStorageAPI.getProfile();
    setProfile(storedProfile);
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: 'No user logged in' };

    const updatedProfile = localStorageAPI.updateProfile(updates);
    if (updatedProfile) {
      setProfile(updatedProfile);
      return { error: null };
    }
    return { error: 'Failed to update profile' };
  };

  const signOut = async () => {
    // For local storage, we don't actually sign out, just return success
    return { error: null };
  };

  const value = {
    user,
    profile,
    loading,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Protected route wrapper - now just shows loading and then content
export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
}


