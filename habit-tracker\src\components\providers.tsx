'use client';

import { ThemeProvider } from 'next-themes';
import { AuthProvider, ProtectedRoute } from '@/components/auth/auth-provider';

// Available themes
const themes = [
  'light',
  'dark',
  'ocean',
  'forest',
  'sunset',
  'minimal',
  'vibrant',
  'pastel'
];

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      themes={themes}
      storageKey="habitflow-theme"
    >
      <AuthProvider>
        <ProtectedRoute>
          {children}
        </ProtectedRoute>
      </AuthProvider>
    </ThemeProvider>
  );
}

export { themes };
