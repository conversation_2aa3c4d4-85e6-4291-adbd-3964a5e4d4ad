{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/theme-selector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTheme } from 'next-themes';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Palette, \n  Check, \n  Sun, \n  Moon, \n  Monitor,\n  Waves,\n  Trees,\n  Sunset,\n  Minimize,\n  Sparkles,\n  Heart\n} from 'lucide-react';\n\nconst themeConfig = {\n  light: {\n    name: 'Light',\n    icon: Sun,\n    description: 'Clean and bright',\n    preview: 'bg-white border-gray-200'\n  },\n  dark: {\n    name: 'Dark',\n    icon: <PERSON>,\n    description: 'Easy on the eyes',\n    preview: 'bg-gray-900 border-gray-700'\n  },\n  system: {\n    name: 'System',\n    icon: Monitor,\n    description: 'Follows your device',\n    preview: 'bg-gradient-to-br from-white to-gray-900 border-gray-400'\n  },\n  ocean: {\n    name: 'Ocean',\n    icon: Waves,\n    description: 'Deep blue serenity',\n    preview: 'bg-gradient-to-br from-blue-50 to-blue-500 border-blue-300'\n  },\n  forest: {\n    name: 'Forest',\n    icon: Trees,\n    description: 'Natural green calm',\n    preview: 'bg-gradient-to-br from-green-50 to-green-600 border-green-300'\n  },\n  sunset: {\n    name: 'Sunset',\n    icon: Sunset,\n    description: 'Warm orange glow',\n    preview: 'bg-gradient-to-br from-orange-50 to-orange-500 border-orange-300'\n  },\n  minimal: {\n    name: 'Minimal',\n    icon: Minimize,\n    description: 'Pure simplicity',\n    preview: 'bg-gradient-to-br from-gray-50 to-gray-200 border-gray-300'\n  },\n  vibrant: {\n    name: 'Vibrant',\n    icon: Sparkles,\n    description: 'Bold and energetic',\n    preview: 'bg-gradient-to-br from-purple-50 to-purple-500 border-purple-300'\n  },\n  pastel: {\n    name: 'Pastel',\n    icon: Heart,\n    description: 'Soft and gentle',\n    preview: 'bg-gradient-to-br from-pink-50 to-pink-300 border-pink-200'\n  }\n};\n\ninterface ThemeSelectorProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ThemeSelector({ isOpen, onClose }: ThemeSelectorProps) {\n  const { theme, setTheme, themes } = useTheme();\n  const [selectedTheme, setSelectedTheme] = useState(theme);\n\n  const handleThemeChange = (newTheme: string) => {\n    setSelectedTheme(newTheme);\n    setTheme(newTheme);\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 z-40\"\n            onClick={onClose}\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-card border border-border rounded-lg shadow-lg z-50 w-full max-w-2xl max-h-[80vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex items-center space-x-3\">\n                  <Palette className=\"h-6 w-6 text-primary\" />\n                  <h2 className=\"text-xl font-semibold\">Choose Theme</h2>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {Object.entries(themeConfig).map(([themeKey, config]) => {\n                  const Icon = config.icon;\n                  const isSelected = selectedTheme === themeKey;\n                  const isAvailable = themes?.includes(themeKey) || themeKey === 'system';\n\n                  if (!isAvailable) return null;\n\n                  return (\n                    <motion.button\n                      key={themeKey}\n                      onClick={() => handleThemeChange(themeKey)}\n                      className={`relative p-4 rounded-lg border-2 transition-all duration-200 text-left ${\n                        isSelected\n                          ? 'border-primary bg-primary/5'\n                          : 'border-border hover:border-primary/50 hover:bg-accent/50'\n                      }`}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      {/* Theme Preview */}\n                      <div className={`w-full h-16 rounded-md mb-3 ${config.preview}`} />\n\n                      {/* Theme Info */}\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Icon className=\"h-4 w-4\" />\n                          <span className=\"font-medium\">{config.name}</span>\n                        </div>\n                        {isSelected && (\n                          <motion.div\n                            initial={{ scale: 0 }}\n                            animate={{ scale: 1 }}\n                            className=\"text-primary\"\n                          >\n                            <Check className=\"h-4 w-4\" />\n                          </motion.div>\n                        )}\n                      </div>\n\n                      <p className=\"text-sm text-muted-foreground\">{config.description}</p>\n\n                      {/* Selection Indicator */}\n                      {isSelected && (\n                        <motion.div\n                          layoutId=\"theme-selector\"\n                          className=\"absolute inset-0 border-2 border-primary rounded-lg pointer-events-none\"\n                          transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                        />\n                      )}\n                    </motion.button>\n                  );\n                })}\n              </div>\n\n              <div className=\"mt-6 pt-4 border-t border-border\">\n                <p className=\"text-sm text-muted-foreground\">\n                  Themes are automatically saved and will be applied across all your devices.\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\nexport function ThemeSelectorButton() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"p-2 rounded-lg hover:bg-accent transition-colors flex items-center space-x-2\"\n        title=\"Change Theme\"\n      >\n        <Palette className=\"h-5 w-5\" />\n        <span className=\"hidden sm:inline\">Theme</span>\n      </button>\n\n      <ThemeSelector isOpen={isOpen} onClose={() => setIsOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAmBA,MAAM,cAAc;IAClB,OAAO;QACL,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,SAAS;IACX;IACA,MAAM;QACJ,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,aAAa;QACb,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;QACb,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,SAAS;IACX;AACF;AAOO,SAAS,cAAc,KAAuC;QAAvC,EAAE,MAAM,EAAE,OAAO,EAAsB,GAAvC;;IAC5B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,SAAS;IACX;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBACvC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAG,WAAU;0DAAwB;;;;;;;;;;;;kDAExC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC;wCAAC,CAAC,UAAU,OAAO;oCAClD,MAAM,OAAO,OAAO,IAAI;oCACxB,MAAM,aAAa,kBAAkB;oCACrC,MAAM,cAAc,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,CAAC,cAAa,aAAa;oCAE/D,IAAI,CAAC,aAAa,OAAO;oCAEzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,0EAIX,OAHC,aACI,gCACA;wCAEN,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAGxB,6LAAC;gDAAI,WAAW,AAAC,+BAA6C,OAAf,OAAO,OAAO;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;0EAAe,OAAO,IAAI;;;;;;;;;;;;oDAE3C,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,WAAU;kEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKvB,6LAAC;gDAAE,WAAU;0DAAiC,OAAO,WAAW;;;;;;4CAG/D,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,UAAS;gDACT,WAAU;gDACV,YAAY;oDAAE,MAAM;oDAAU,QAAQ;oDAAK,UAAU;gDAAI;;;;;;;uCArCxD;;;;;gCA0CX;;;;;;0CAGF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D;GA7GgB;;QACsB,mJAAA,CAAA,WAAQ;;;KAD9B;AA+GT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,6LAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;gBACV,OAAM;;kCAEN,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAmB;;;;;;;;;;;;0BAGrC,6LAAC;gBAAc,QAAQ;gBAAQ,SAAS,IAAM,UAAU;;;;;;;;AAG9D;IAjBgB;MAAA", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/habit-management.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Plus, \n  Edit, \n  Trash2, \n  Target, \n  Calendar,\n  Clock,\n  Tag,\n  Palette,\n  Save,\n  X,\n  Check,\n  Flame,\n  Star,\n  Heart,\n  Zap,\n  Coffee,\n  Book,\n  Dumbbell,\n  Droplets,\n  Moon,\n  Sun\n} from 'lucide-react';\n\n// Habit categories with icons and colors\nconst categories = [\n  { id: 'health', name: 'Health', icon: Heart, color: 'text-red-500' },\n  { id: 'fitness', name: 'Fitness', icon: Dumb<PERSON>, color: 'text-orange-500' },\n  { id: 'learning', name: 'Learning', icon: Book, color: 'text-blue-500' },\n  { id: 'wellness', name: 'Wellness', icon: Star, color: 'text-purple-500' },\n  { id: 'productivity', name: 'Productivity', icon: Zap, color: 'text-yellow-500' },\n  { id: 'lifestyle', name: 'Lifestyle', icon: Coffee, color: 'text-green-500' },\n  { id: 'mindfulness', name: 'Mindfulness', icon: <PERSON>, color: 'text-indigo-500' },\n  { id: 'other', name: 'Other', icon: Target, color: 'text-gray-500' },\n];\n\n// Habit icons\nconst habitIcons = [\n  Droplets, Heart, Dumbbell, Book, Coffee, Moon, Sun, Star, \n  Zap, Target, Calendar, Clock, Tag, Flame\n];\n\n// Frequency options\nconst frequencies = [\n  { id: 'daily', name: 'Daily', description: 'Every day' },\n  { id: 'weekly', name: 'Weekly', description: 'Once a week' },\n  { id: 'weekdays', name: 'Weekdays', description: 'Monday to Friday' },\n  { id: 'weekends', name: 'Weekends', description: 'Saturday and Sunday' },\n  { id: 'custom', name: 'Custom', description: 'Choose specific days' },\n];\n\ninterface Habit {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  icon: string;\n  color: string;\n  frequency: string;\n  reminderTime?: string;\n  streak: number;\n  completedToday: boolean;\n  createdAt: Date;\n  lastCompleted?: Date;\n}\n\ninterface HabitFormProps {\n  habit?: Habit;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (habit: Partial<Habit>) => void;\n}\n\nfunction HabitForm({ habit, isOpen, onClose, onSave }: HabitFormProps) {\n  const [formData, setFormData] = useState({\n    name: habit?.name || '',\n    description: habit?.description || '',\n    category: habit?.category || 'health',\n    icon: habit?.icon || 'Heart',\n    color: habit?.color || 'text-red-500',\n    frequency: habit?.frequency || 'daily',\n    reminderTime: habit?.reminderTime || '',\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(formData);\n    onClose();\n  };\n\n  const selectedCategory = categories.find(c => c.id === formData.category);\n  const SelectedIcon = habitIcons.find(icon => icon.name === formData.icon) || Heart;\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 z-40\"\n            onClick={onClose}\n          />\n\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-card border border-border rounded-lg shadow-lg z-50 w-full max-w-md max-h-[90vh] overflow-y-auto\"\n          >\n            <form onSubmit={handleSubmit} className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-xl font-semibold\">\n                  {habit ? 'Edit Habit' : 'Create New Habit'}\n                </h2>\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {/* Habit Name */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Habit Name</label>\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n                    placeholder=\"e.g., Drink 8 glasses of water\"\n                    required\n                  />\n                </div>\n\n                {/* Description */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Description (Optional)</label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n                    placeholder=\"Add more details about your habit...\"\n                    rows={3}\n                  />\n                </div>\n\n                {/* Category */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Category</label>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {categories.map((category) => {\n                      const Icon = category.icon;\n                      return (\n                        <button\n                          key={category.id}\n                          type=\"button\"\n                          onClick={() => setFormData({ ...formData, category: category.id, color: category.color })}\n                          className={`p-3 rounded-lg border transition-all ${\n                            formData.category === category.id\n                              ? 'border-primary bg-primary/10'\n                              : 'border-border hover:border-primary/50'\n                          }`}\n                        >\n                          <Icon className={`h-5 w-5 mx-auto mb-1 ${category.color}`} />\n                          <span className=\"text-xs\">{category.name}</span>\n                        </button>\n                      );\n                    })}\n                  </div>\n                </div>\n\n                {/* Icon Selection */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Icon</label>\n                  <div className=\"grid grid-cols-7 gap-2\">\n                    {habitIcons.map((Icon, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        onClick={() => setFormData({ ...formData, icon: Icon.name })}\n                        className={`p-2 rounded-lg border transition-all ${\n                          formData.icon === Icon.name\n                            ? 'border-primary bg-primary/10'\n                            : 'border-border hover:border-primary/50'\n                        }`}\n                      >\n                        <Icon className={`h-4 w-4 mx-auto ${selectedCategory?.color || 'text-gray-500'}`} />\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Frequency */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Frequency</label>\n                  <select\n                    value={formData.frequency}\n                    onChange={(e) => setFormData({ ...formData, frequency: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n                  >\n                    {frequencies.map((freq) => (\n                      <option key={freq.id} value={freq.id}>\n                        {freq.name} - {freq.description}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Reminder Time */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Reminder Time (Optional)</label>\n                  <input\n                    type=\"time\"\n                    value={formData.reminderTime}\n                    onChange={(e) => setFormData({ ...formData, reminderTime: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3 mt-6\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  <span>{habit ? 'Update' : 'Create'}</span>\n                </button>\n              </div>\n            </form>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface HabitCardProps {\n  habit: Habit;\n  onEdit: (habit: Habit) => void;\n  onDelete: (id: string) => void;\n  onToggleComplete: (id: string) => void;\n}\n\nfunction HabitCard({ habit, onEdit, onDelete, onToggleComplete }: HabitCardProps) {\n  const category = categories.find(c => c.id === habit.category);\n  const Icon = habitIcons.find(icon => icon.name === habit.icon) || Heart;\n\n  return (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      className=\"bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow\"\n    >\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`p-2 rounded-lg bg-accent/50`}>\n            <Icon className={`h-5 w-5 ${category?.color || 'text-gray-500'}`} />\n          </div>\n          <div>\n            <h3 className=\"font-medium\">{habit.name}</h3>\n            <p className=\"text-sm text-muted-foreground\">{category?.name}</p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={() => onEdit(habit)}\n            className=\"p-1 text-muted-foreground hover:text-foreground transition-colors\"\n          >\n            <Edit className=\"h-4 w-4\" />\n          </button>\n          <button\n            onClick={() => onDelete(habit.id)}\n            className=\"p-1 text-muted-foreground hover:text-destructive transition-colors\"\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n\n      {habit.description && (\n        <p className=\"text-sm text-muted-foreground mb-3\">{habit.description}</p>\n      )}\n\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-1\">\n            <Flame className=\"h-4 w-4 text-orange-500\" />\n            <span className=\"text-sm font-medium\">{habit.streak}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n            <span className=\"text-sm text-muted-foreground capitalize\">{habit.frequency}</span>\n          </div>\n        </div>\n\n        <button\n          onClick={() => onToggleComplete(habit.id)}\n          className={`p-2 rounded-lg transition-all ${\n            habit.completedToday\n              ? 'bg-primary text-primary-foreground'\n              : 'border border-border hover:border-primary'\n          }`}\n        >\n          <Check className=\"h-4 w-4\" />\n        </button>\n      </div>\n    </motion.div>\n  );\n}\n\nexport function HabitManagement() {\n  const [habits, setHabits] = useState<Habit[]>([\n    {\n      id: '1',\n      name: 'Drink Water',\n      description: 'Drink 8 glasses of water throughout the day',\n      category: 'health',\n      icon: 'Droplets',\n      color: 'text-blue-500',\n      frequency: 'daily',\n      streak: 7,\n      completedToday: true,\n      createdAt: new Date(),\n    },\n    {\n      id: '2',\n      name: 'Exercise',\n      description: '30 minutes of physical activity',\n      category: 'fitness',\n      icon: 'Dumbbell',\n      color: 'text-orange-500',\n      frequency: 'daily',\n      streak: 3,\n      completedToday: false,\n      createdAt: new Date(),\n    },\n  ]);\n\n  const [isFormOpen, setIsFormOpen] = useState(false);\n  const [editingHabit, setEditingHabit] = useState<Habit | undefined>();\n  const [filter, setFilter] = useState('all');\n\n  const handleSaveHabit = (habitData: Partial<Habit>) => {\n    if (editingHabit) {\n      setHabits(habits.map(h => h.id === editingHabit.id ? { ...h, ...habitData } : h));\n    } else {\n      const newHabit: Habit = {\n        id: Date.now().toString(),\n        streak: 0,\n        completedToday: false,\n        createdAt: new Date(),\n        ...habitData as Habit,\n      };\n      setHabits([...habits, newHabit]);\n    }\n    setEditingHabit(undefined);\n  };\n\n  const handleEditHabit = (habit: Habit) => {\n    setEditingHabit(habit);\n    setIsFormOpen(true);\n  };\n\n  const handleDeleteHabit = (id: string) => {\n    setHabits(habits.filter(h => h.id !== id));\n  };\n\n  const handleToggleComplete = (id: string) => {\n    setHabits(habits.map(h => {\n      if (h.id === id) {\n        const newCompleted = !h.completedToday;\n        return {\n          ...h,\n          completedToday: newCompleted,\n          streak: newCompleted ? h.streak + 1 : Math.max(0, h.streak - 1),\n          lastCompleted: newCompleted ? new Date() : h.lastCompleted,\n        };\n      }\n      return h;\n    }));\n  };\n\n  const filteredHabits = habits.filter(habit => {\n    if (filter === 'all') return true;\n    if (filter === 'completed') return habit.completedToday;\n    if (filter === 'pending') return !habit.completedToday;\n    return habit.category === filter;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold\">Habit Management</h2>\n        <button\n          onClick={() => {\n            setEditingHabit(undefined);\n            setIsFormOpen(true);\n          }}\n          className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          <span>Add Habit</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap gap-2\">\n        {[\n          { id: 'all', name: 'All Habits' },\n          { id: 'completed', name: 'Completed Today' },\n          { id: 'pending', name: 'Pending' },\n          ...categories,\n        ].map((filterOption) => (\n          <button\n            key={filterOption.id}\n            onClick={() => setFilter(filterOption.id)}\n            className={`px-3 py-1 rounded-full text-sm transition-colors ${\n              filter === filterOption.id\n                ? 'bg-primary text-primary-foreground'\n                : 'bg-accent hover:bg-accent/80'\n            }`}\n          >\n            {filterOption.name}\n          </button>\n        ))}\n      </div>\n\n      {/* Habits Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        <AnimatePresence>\n          {filteredHabits.map((habit) => (\n            <HabitCard\n              key={habit.id}\n              habit={habit}\n              onEdit={handleEditHabit}\n              onDelete={handleDeleteHabit}\n              onToggleComplete={handleToggleComplete}\n            />\n          ))}\n        </AnimatePresence>\n      </div>\n\n      {filteredHabits.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Target className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium mb-2\">No habits found</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            {filter === 'all' \n              ? \"Start building better habits by creating your first one!\"\n              : \"No habits match your current filter.\"\n            }\n          </p>\n          {filter === 'all' && (\n            <button\n              onClick={() => {\n                setEditingHabit(undefined);\n                setIsFormOpen(true);\n              }}\n              className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Create Your First Habit\n            </button>\n          )}\n        </div>\n      )}\n\n      {/* Habit Form Modal */}\n      <HabitForm\n        habit={editingHabit}\n        isOpen={isFormOpen}\n        onClose={() => {\n          setIsFormOpen(false);\n          setEditingHabit(undefined);\n        }}\n        onSave={handleSaveHabit}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA4BA,yCAAyC;AACzC,MAAM,aAAa;IACjB;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM,uMAAA,CAAA,QAAK;QAAE,OAAO;IAAe;IACnE;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;IAAkB;IAC3E;QAAE,IAAI;QAAY,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAgB;IACvE;QAAE,IAAI;QAAY,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAkB;IACzE;QAAE,IAAI;QAAgB,MAAM;QAAgB,MAAM,mMAAA,CAAA,MAAG;QAAE,OAAO;IAAkB;IAChF;QAAE,IAAI;QAAa,MAAM;QAAa,MAAM,yMAAA,CAAA,SAAM;QAAE,OAAO;IAAiB;IAC5E;QAAE,IAAI;QAAe,MAAM;QAAe,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;IAAkB;IAC/E;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM,yMAAA,CAAA,SAAM;QAAE,OAAO;IAAgB;CACpE;AAED,cAAc;AACd,MAAM,aAAa;IACjB,6MAAA,CAAA,WAAQ;IAAE,uMAAA,CAAA,QAAK;IAAE,6MAAA,CAAA,WAAQ;IAAE,qMAAA,CAAA,OAAI;IAAE,yMAAA,CAAA,SAAM;IAAE,qMAAA,CAAA,OAAI;IAAE,mMAAA,CAAA,MAAG;IAAE,qMAAA,CAAA,OAAI;IACxD,mMAAA,CAAA,MAAG;IAAE,yMAAA,CAAA,SAAM;IAAE,6MAAA,CAAA,WAAQ;IAAE,uMAAA,CAAA,QAAK;IAAE,mMAAA,CAAA,MAAG;IAAE,uMAAA,CAAA,QAAK;CACzC;AAED,oBAAoB;AACpB,MAAM,cAAc;IAClB;QAAE,IAAI;QAAS,MAAM;QAAS,aAAa;IAAY;IACvD;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAc;IAC3D;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAAmB;IACpE;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAAsB;IACvE;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAuB;CACrE;AAwBD,SAAS,UAAU,KAAkD;QAAlD,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAkB,GAAlD;;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,CAAA,kBAAA,4BAAA,MAAO,IAAI,KAAI;QACrB,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;QACnC,UAAU,CAAA,kBAAA,4BAAA,MAAO,QAAQ,KAAI;QAC7B,MAAM,CAAA,kBAAA,4BAAA,MAAO,IAAI,KAAI;QACrB,OAAO,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI;QACvB,WAAW,CAAA,kBAAA,4BAAA,MAAO,SAAS,KAAI;QAC/B,cAAc,CAAA,kBAAA,4BAAA,MAAO,YAAY,KAAI;IACvC;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;QACP;IACF;IAEA,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,QAAQ;IACxE,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,uMAAA,CAAA,QAAK;IAElF,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAGX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBACvC,WAAU;8BAEV,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,QAAQ,eAAe;;;;;;kDAE1B,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAKZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,WAAU;gDACV,aAAY;gDACZ,MAAM;;;;;;;;;;;;kDAKV,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC;oDACf,MAAM,OAAO,SAAS,IAAI;oDAC1B,qBACE,6LAAC;wDAEC,MAAK;wDACL,SAAS,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,SAAS,EAAE;gEAAE,OAAO,SAAS,KAAK;4DAAC;wDACvF,WAAW,AAAC,wCAIX,OAHC,SAAS,QAAQ,KAAK,SAAS,EAAE,GAC7B,iCACA;;0EAGN,6LAAC;gEAAK,WAAW,AAAC,wBAAsC,OAAf,SAAS,KAAK;;;;;;0EACvD,6LAAC;gEAAK,WAAU;0EAAW,SAAS,IAAI;;;;;;;uDAVnC,SAAS,EAAE;;;;;gDAatB;;;;;;;;;;;;kDAKJ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wDAEC,MAAK;wDACL,SAAS,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,MAAM,KAAK,IAAI;4DAAC;wDAC1D,WAAW,AAAC,wCAIX,OAHC,SAAS,IAAI,KAAK,KAAK,IAAI,GACvB,iCACA;kEAGN,cAAA,6LAAC;4DAAK,WAAW,AAAC,mBAA6D,OAA3C,CAAA,6BAAA,uCAAA,iBAAkB,KAAK,KAAI;;;;;;uDAT1D;;;;;;;;;;;;;;;;kDAgBb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtE,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;wDAAqB,OAAO,KAAK,EAAE;;4DACjC,KAAK,IAAI;4DAAC;4DAAI,KAAK,WAAW;;uDADpB,KAAK,EAAE;;;;;;;;;;;;;;;;kDAQ1B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzE,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAM,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GA9KS;KAAA;AAuLT,SAAS,UAAU,KAA6D;QAA7D,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAkB,GAA7D;IACjB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,QAAQ;IAC7D,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,uMAAA,CAAA,QAAK;IAEvE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAY;0CACf,cAAA,6LAAC;oCAAK,WAAW,AAAC,WAA6C,OAAnC,CAAA,qBAAA,+BAAA,SAAU,KAAK,KAAI;;;;;;;;;;;0CAEjD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAe,MAAM,IAAI;;;;;;kDACvC,6LAAC;wCAAE,WAAU;kDAAiC,qBAAA,+BAAA,SAAU,IAAI;;;;;;;;;;;;;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO;gCACtB,WAAU;0CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCACC,SAAS,IAAM,SAAS,MAAM,EAAE;gCAChC,WAAU;0CAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAKvB,MAAM,WAAW,kBAChB,6LAAC;gBAAE,WAAU;0BAAsC,MAAM,WAAW;;;;;;0BAGtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAuB,MAAM,MAAM;;;;;;;;;;;;0CAErD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAA4C,MAAM,SAAS;;;;;;;;;;;;;;;;;;kCAI/E,6LAAC;wBACC,SAAS,IAAM,iBAAiB,MAAM,EAAE;wBACxC,WAAW,AAAC,iCAIX,OAHC,MAAM,cAAc,GAChB,uCACA;kCAGN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3B;MAnES;AAqEF,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,gBAAgB;YAChB,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,gBAAgB;YAChB,WAAW,IAAI;QACjB;KACD;IAED,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc;YAChB,UAAU,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,GAAG,SAAS;gBAAC,IAAI;QAChF,OAAO;YACL,MAAM,WAAkB;gBACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,QAAQ;gBACR,gBAAgB;gBAChB,WAAW,IAAI;gBACf,GAAG,SAAS;YACd;YACA,UAAU;mBAAI;gBAAQ;aAAS;QACjC;QACA,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,UAAU,OAAO,GAAG,CAAC,CAAA;YACnB,IAAI,EAAE,EAAE,KAAK,IAAI;gBACf,MAAM,eAAe,CAAC,EAAE,cAAc;gBACtC,OAAO;oBACL,GAAG,CAAC;oBACJ,gBAAgB;oBAChB,QAAQ,eAAe,EAAE,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG;oBAC7D,eAAe,eAAe,IAAI,SAAS,EAAE,aAAa;gBAC5D;YACF;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,aAAa,OAAO,MAAM,cAAc;QACvD,IAAI,WAAW,WAAW,OAAO,CAAC,MAAM,cAAc;QACtD,OAAO,MAAM,QAAQ,KAAK;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,cAAc;wBAChB;wBACA,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,IAAI;wBAAO,MAAM;oBAAa;oBAChC;wBAAE,IAAI;wBAAa,MAAM;oBAAkB;oBAC3C;wBAAE,IAAI;wBAAW,MAAM;oBAAU;uBAC9B;iBACJ,CAAC,GAAG,CAAC,CAAC,6BACL,6LAAC;wBAEC,SAAS,IAAM,UAAU,aAAa,EAAE;wBACxC,WAAW,AAAC,oDAIX,OAHC,WAAW,aAAa,EAAE,GACtB,uCACA;kCAGL,aAAa,IAAI;uBARb,aAAa,EAAE;;;;;;;;;;0BAc1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;4BAEC,OAAO;4BACP,QAAQ;4BACR,UAAU;4BACV,kBAAkB;2BAJb,MAAM,EAAE;;;;;;;;;;;;;;;YAUpB,eAAe,MAAM,KAAK,mBACzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCACV,WAAW,QACR,6DACA;;;;;;oBAGL,WAAW,uBACV,6LAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,cAAc;wBAChB;wBACA,WAAU;kCACX;;;;;;;;;;;;0BAQP,6LAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,SAAS;oBACP,cAAc;oBACd,gBAAgB;gBAClB;gBACA,QAAQ;;;;;;;;;;;;AAIhB;IAzKgB;MAAA", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  LineChart,\n  Line,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  Area,\n  AreaChart\n} from 'recharts';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Calendar,\n  Target,\n  Flame,\n  Award,\n  Clock,\n  BarChart3,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Activity\n} from 'lucide-react';\n\n// Mock data for analytics\nconst mockStreakData = [\n  { date: '2024-01-01', streaks: 5 },\n  { date: '2024-01-02', streaks: 6 },\n  { date: '2024-01-03', streaks: 4 },\n  { date: '2024-01-04', streaks: 7 },\n  { date: '2024-01-05', streaks: 8 },\n  { date: '2024-01-06', streaks: 6 },\n  { date: '2024-01-07', streaks: 9 },\n  { date: '2024-01-08', streaks: 7 },\n  { date: '2024-01-09', streaks: 8 },\n  { date: '2024-01-10', streaks: 10 },\n  { date: '2024-01-11', streaks: 9 },\n  { date: '2024-01-12', streaks: 11 },\n  { date: '2024-01-13', streaks: 8 },\n  { date: '2024-01-14', streaks: 12 },\n];\n\nconst mockCompletionData = [\n  { name: 'Mon', completed: 85, total: 100 },\n  { name: 'Tue', completed: 92, total: 100 },\n  { name: 'Wed', completed: 78, total: 100 },\n  { name: 'Thu', completed: 88, total: 100 },\n  { name: 'Fri', completed: 95, total: 100 },\n  { name: 'Sat', completed: 72, total: 100 },\n  { name: 'Sun', completed: 68, total: 100 },\n];\n\nconst mockCategoryData = [\n  { name: 'Health', value: 35, color: '#ef4444' },\n  { name: 'Fitness', value: 25, color: '#f97316' },\n  { name: 'Learning', value: 20, color: '#3b82f6' },\n  { name: 'Wellness', value: 15, color: '#8b5cf6' },\n  { name: 'Other', value: 5, color: '#6b7280' },\n];\n\nconst mockHabitProgress = [\n  { date: '2024-01-01', 'Drink Water': 8, 'Exercise': 1, 'Read': 1, 'Meditate': 1 },\n  { date: '2024-01-02', 'Drink Water': 7, 'Exercise': 1, 'Read': 1, 'Meditate': 0 },\n  { date: '2024-01-03', 'Drink Water': 8, 'Exercise': 0, 'Read': 1, 'Meditate': 1 },\n  { date: '2024-01-04', 'Drink Water': 9, 'Exercise': 1, 'Read': 1, 'Meditate': 1 },\n  { date: '2024-01-05', 'Drink Water': 8, 'Exercise': 1, 'Read': 0, 'Meditate': 1 },\n  { date: '2024-01-06', 'Drink Water': 7, 'Exercise': 1, 'Read': 1, 'Meditate': 1 },\n  { date: '2024-01-07', 'Drink Water': 8, 'Exercise': 1, 'Read': 1, 'Meditate': 1 },\n];\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon: React.ComponentType<any>;\n  color?: string;\n}\n\nfunction StatCard({ title, value, change, icon: Icon, color = 'text-primary' }: StatCardProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-card p-6 rounded-lg border border-border\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm text-muted-foreground\">{title}</p>\n          <p className=\"text-2xl font-bold mt-1\">{value}</p>\n          {change !== undefined && (\n            <div className={`flex items-center mt-2 text-sm ${\n              change >= 0 ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {change >= 0 ? (\n                <TrendingUp className=\"h-4 w-4 mr-1\" />\n              ) : (\n                <TrendingDown className=\"h-4 w-4 mr-1\" />\n              )}\n              <span>{Math.abs(change)}% from last week</span>\n            </div>\n          )}\n        </div>\n        <Icon className={`h-8 w-8 ${color}`} />\n      </div>\n    </motion.div>\n  );\n}\n\ninterface ChartCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ComponentType<any>;\n}\n\nfunction ChartCard({ title, children, icon: Icon }: ChartCardProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-card p-6 rounded-lg border border-border\"\n    >\n      <div className=\"flex items-center space-x-2 mb-4\">\n        {Icon && <Icon className=\"h-5 w-5 text-primary\" />}\n        <h3 className=\"text-lg font-semibold\">{title}</h3>\n      </div>\n      {children}\n    </motion.div>\n  );\n}\n\nexport function Analytics() {\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedChart, setSelectedChart] = useState('streaks');\n\n  const stats = useMemo(() => {\n    return {\n      totalStreaks: 156,\n      longestStreak: 45,\n      currentStreak: 12,\n      completionRate: 87,\n      totalHabits: 8,\n      activeHabits: 6,\n    };\n  }, []);\n\n  const chartOptions = [\n    { id: 'streaks', name: 'Streak Trends', icon: Flame },\n    { id: 'completion', name: 'Completion Rate', icon: Target },\n    { id: 'categories', name: 'Categories', icon: PieChartIcon },\n    { id: 'progress', name: 'Habit Progress', icon: Activity },\n  ];\n\n  const timeRanges = [\n    { id: '7d', name: '7 Days' },\n    { id: '30d', name: '30 Days' },\n    { id: '90d', name: '90 Days' },\n    { id: '1y', name: '1 Year' },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold\">Analytics & Insights</h2>\n        <div className=\"flex items-center space-x-2\">\n          {timeRanges.map((range) => (\n            <button\n              key={range.id}\n              onClick={() => setTimeRange(range.id)}\n              className={`px-3 py-1 rounded-lg text-sm transition-colors ${\n                timeRange === range.id\n                  ? 'bg-primary text-primary-foreground'\n                  : 'bg-accent hover:bg-accent/80'\n              }`}\n            >\n              {range.name}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        <StatCard\n          title=\"Total Streaks\"\n          value={stats.totalStreaks}\n          change={12}\n          icon={Flame}\n          color=\"text-orange-500\"\n        />\n        <StatCard\n          title=\"Longest Streak\"\n          value={`${stats.longestStreak} days`}\n          change={5}\n          icon={Award}\n          color=\"text-yellow-500\"\n        />\n        <StatCard\n          title=\"Current Streak\"\n          value={`${stats.currentStreak} days`}\n          change={-2}\n          icon={Calendar}\n          color=\"text-blue-500\"\n        />\n        <StatCard\n          title=\"Completion Rate\"\n          value={`${stats.completionRate}%`}\n          change={8}\n          icon={Target}\n          color=\"text-green-500\"\n        />\n        <StatCard\n          title=\"Active Habits\"\n          value={`${stats.activeHabits}/${stats.totalHabits}`}\n          icon={BarChart3}\n          color=\"text-purple-500\"\n        />\n        <StatCard\n          title=\"Average Daily\"\n          value=\"6.2 habits\"\n          change={3}\n          icon={Clock}\n          color=\"text-indigo-500\"\n        />\n      </div>\n\n      {/* Chart Selection */}\n      <div className=\"flex flex-wrap gap-2\">\n        {chartOptions.map((option) => {\n          const Icon = option.icon;\n          return (\n            <button\n              key={option.id}\n              onClick={() => setSelectedChart(option.id)}\n              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${\n                selectedChart === option.id\n                  ? 'bg-primary text-primary-foreground'\n                  : 'bg-accent hover:bg-accent/80'\n              }`}\n            >\n              <Icon className=\"h-4 w-4\" />\n              <span>{option.name}</span>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {selectedChart === 'streaks' && (\n          <ChartCard title=\"Streak Trends Over Time\" icon={Flame}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={mockStreakData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"date\" \n                  tickFormatter={(value) => new Date(value).toLocaleDateString()}\n                />\n                <YAxis />\n                <Tooltip \n                  labelFormatter={(value) => new Date(value).toLocaleDateString()}\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"streaks\" \n                  stroke=\"hsl(var(--primary))\" \n                  fill=\"hsl(var(--primary))\"\n                  fillOpacity={0.3}\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </ChartCard>\n        )}\n\n        {selectedChart === 'completion' && (\n          <ChartCard title=\"Weekly Completion Rate\" icon={Target}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={mockCompletionData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"name\" />\n                <YAxis />\n                <Tooltip />\n                <Bar dataKey=\"completed\" fill=\"hsl(var(--primary))\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </ChartCard>\n        )}\n\n        {selectedChart === 'categories' && (\n          <ChartCard title=\"Habits by Category\" icon={PieChartIcon}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={mockCategoryData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {mockCategoryData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </ChartCard>\n        )}\n\n        {selectedChart === 'progress' && (\n          <ChartCard title=\"Individual Habit Progress\" icon={Activity}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={mockHabitProgress}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"date\" \n                  tickFormatter={(value) => new Date(value).toLocaleDateString()}\n                />\n                <YAxis />\n                <Tooltip \n                  labelFormatter={(value) => new Date(value).toLocaleDateString()}\n                />\n                <Line type=\"monotone\" dataKey=\"Drink Water\" stroke=\"#3b82f6\" strokeWidth={2} />\n                <Line type=\"monotone\" dataKey=\"Exercise\" stroke=\"#ef4444\" strokeWidth={2} />\n                <Line type=\"monotone\" dataKey=\"Read\" stroke=\"#10b981\" strokeWidth={2} />\n                <Line type=\"monotone\" dataKey=\"Meditate\" stroke=\"#8b5cf6\" strokeWidth={2} />\n              </LineChart>\n            </ResponsiveContainer>\n          </ChartCard>\n        )}\n\n        {/* Insights Panel */}\n        <ChartCard title=\"Key Insights\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n              <div>\n                <p className=\"font-medium\">Strong Weekend Performance</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  Your completion rate on weekends has improved by 15% this month.\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n              <div>\n                <p className=\"font-medium\">Longest Streak: Exercise</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  Your exercise habit has the longest current streak at 12 days.\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n              <div>\n                <p className=\"font-medium\">Best Time: Morning</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  Habits completed before 10 AM have a 92% success rate.\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full mt-2\"></div>\n              <div>\n                <p className=\"font-medium\">Improvement Opportunity</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  Consider adding a reminder for your reading habit to improve consistency.\n                </p>\n              </div>\n            </div>\n          </div>\n        </ChartCard>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AApBA;;;;;AAiCA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAG;IAClC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAG;IAClC;QAAE,MAAM;QAAc,SAAS;IAAE;IACjC;QAAE,MAAM;QAAc,SAAS;IAAG;CACnC;AAED,MAAM,qBAAqB;IACzB;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,WAAW;QAAI,OAAO;IAAI;CAC1C;AAED,MAAM,mBAAmB;IACvB;QAAE,MAAM;QAAU,OAAO;QAAI,OAAO;IAAU;IAC9C;QAAE,MAAM;QAAW,OAAO;QAAI,OAAO;IAAU;IAC/C;QAAE,MAAM;QAAY,OAAO;QAAI,OAAO;IAAU;IAChD;QAAE,MAAM;QAAY,OAAO;QAAI,OAAO;IAAU;IAChD;QAAE,MAAM;QAAS,OAAO;QAAG,OAAO;IAAU;CAC7C;AAED,MAAM,oBAAoB;IACxB;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;IAChF;QAAE,MAAM;QAAc,eAAe;QAAG,YAAY;QAAG,QAAQ;QAAG,YAAY;IAAE;CACjF;AAUD,SAAS,SAAS,KAA2E;QAA3E,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,QAAQ,cAAc,EAAiB,GAA3E;IAChB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;wBACvC,WAAW,2BACV,6LAAC;4BAAI,WAAW,AAAC,kCAEhB,OADC,UAAU,IAAI,mBAAmB;;gCAEhC,UAAU,kBACT,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;yDAEtB,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CAE1B,6LAAC;;wCAAM,KAAK,GAAG,CAAC;wCAAQ;;;;;;;;;;;;;;;;;;;8BAI9B,6LAAC;oBAAK,WAAW,AAAC,WAAgB,OAAN;;;;;;;;;;;;;;;;;AAIpC;KA5BS;AAoCT,SAAS,UAAU,KAA+C;QAA/C,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,IAAI,EAAkB,GAA/C;IACjB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;oBACZ,sBAAQ,6LAAC;wBAAK,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAAyB;;;;;;;;;;;;YAExC;;;;;;;AAGP;MAdS;AAgBF,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE;YACpB,OAAO;gBACL,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,aAAa;gBACb,cAAc;YAChB;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe;QACnB;YAAE,IAAI;YAAW,MAAM;YAAiB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACpD;YAAE,IAAI;YAAc,MAAM;YAAmB,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM,iNAAA,CAAA,WAAY;QAAC;QAC3D;YAAE,IAAI;YAAY,MAAM;YAAkB,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAC1D;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAM,MAAM;QAAS;QAC3B;YAAE,IAAI;YAAO,MAAM;QAAU;QAC7B;YAAE,IAAI;YAAO,MAAM;QAAU;QAC7B;YAAE,IAAI;YAAM,MAAM;QAAS;KAC5B;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC;gCAEC,SAAS,IAAM,aAAa,MAAM,EAAE;gCACpC,WAAW,AAAC,kDAIX,OAHC,cAAc,MAAM,EAAE,GAClB,uCACA;0CAGL,MAAM,IAAI;+BARN,MAAM,EAAE;;;;;;;;;;;;;;;;0BAerB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,OAAO,MAAM,YAAY;wBACzB,QAAQ;wBACR,MAAM,uMAAA,CAAA,QAAK;wBACX,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,OAAO,AAAC,GAAsB,OAApB,MAAM,aAAa,EAAC;wBAC9B,QAAQ;wBACR,MAAM,uMAAA,CAAA,QAAK;wBACX,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,OAAO,AAAC,GAAsB,OAApB,MAAM,aAAa,EAAC;wBAC9B,QAAQ,CAAC;wBACT,MAAM,6MAAA,CAAA,WAAQ;wBACd,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,OAAO,AAAC,GAAuB,OAArB,MAAM,cAAc,EAAC;wBAC/B,QAAQ;wBACR,MAAM,yMAAA,CAAA,SAAM;wBACZ,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,OAAO,AAAC,GAAwB,OAAtB,MAAM,YAAY,EAAC,KAAqB,OAAlB,MAAM,WAAW;wBACjD,MAAM,qNAAA,CAAA,YAAS;wBACf,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAQ;wBACR,MAAM,uMAAA,CAAA,QAAK;wBACX,OAAM;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC;oBACjB,MAAM,OAAO,OAAO,IAAI;oBACxB,qBACE,6LAAC;wBAEC,SAAS,IAAM,iBAAiB,OAAO,EAAE;wBACzC,WAAW,AAAC,sEAIX,OAHC,kBAAkB,OAAO,EAAE,GACvB,uCACA;;0CAGN,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAM,OAAO,IAAI;;;;;;;uBATb,OAAO,EAAE;;;;;gBAYpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;oBACZ,kBAAkB,2BACjB,6LAAC;wBAAU,OAAM;wBAA0B,MAAM,uMAAA,CAAA,QAAK;kCACpD,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAQ;sCACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gCAAC,MAAM;;kDACf,6LAAC,gKAAA,CAAA,gBAAa;wCAAC,iBAAgB;;;;;;kDAC/B,6LAAC,wJAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,eAAe,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;;;;;;kDAE9D,6LAAC,wJAAA,CAAA,QAAK;;;;;kDACN,6LAAC,0JAAA,CAAA,UAAO;wCACN,gBAAgB,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;;;;;;kDAE/D,6LAAC,uJAAA,CAAA,OAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,MAAK;wCACL,aAAa;;;;;;;;;;;;;;;;;;;;;;oBAOtB,kBAAkB,8BACjB,6LAAC;wBAAU,OAAM;wBAAyB,MAAM,yMAAA,CAAA,SAAM;kCACpD,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAQ;sCACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gCAAC,MAAM;;kDACd,6LAAC,gKAAA,CAAA,gBAAa;wCAAC,iBAAgB;;;;;;kDAC/B,6LAAC,wJAAA,CAAA,QAAK;wCAAC,SAAQ;;;;;;kDACf,6LAAC,wJAAA,CAAA,QAAK;;;;;kDACN,6LAAC,0JAAA,CAAA,UAAO;;;;;kDACR,6LAAC,sJAAA,CAAA,MAAG;wCAAC,SAAQ;wCAAY,MAAK;;;;;;;;;;;;;;;;;;;;;;oBAMrC,kBAAkB,8BACjB,6LAAC;wBAAU,OAAM;wBAAqB,MAAM,iNAAA,CAAA,WAAY;kCACtD,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAQ;sCACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kDACP,6LAAC,kJAAA,CAAA,MAAG;wCACF,MAAM;wCACN,IAAG;wCACH,IAAG;wCACH,WAAW;wCACX,OAAO;gDAAC,EAAE,IAAI,EAAE,OAAO,EAAE;mDAAK,AAAC,GAAU,OAAR,MAAK,KAA8B,OAA3B,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,IAAG;;wCACpE,aAAa;wCACb,MAAK;wCACL,SAAQ;kDAEP,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,uJAAA,CAAA,OAAI;gDAAuB,MAAM,MAAM,KAAK;+CAAlC,AAAC,QAAa,OAAN;;;;;;;;;;kDAGvB,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;oBAMf,kBAAkB,4BACjB,6LAAC;wBAAU,OAAM;wBAA4B,MAAM,6MAAA,CAAA,WAAQ;kCACzD,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAQ;sCACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gCAAC,MAAM;;kDACf,6LAAC,gKAAA,CAAA,gBAAa;wCAAC,iBAAgB;;;;;;kDAC/B,6LAAC,wJAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,eAAe,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;;;;;;kDAE9D,6LAAC,wJAAA,CAAA,QAAK;;;;;kDACN,6LAAC,0JAAA,CAAA,UAAO;wCACN,gBAAgB,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;;;;;;kDAE/D,6LAAC,uJAAA,CAAA,OAAI;wCAAC,MAAK;wCAAW,SAAQ;wCAAc,QAAO;wCAAU,aAAa;;;;;;kDAC1E,6LAAC,uJAAA,CAAA,OAAI;wCAAC,MAAK;wCAAW,SAAQ;wCAAW,QAAO;wCAAU,aAAa;;;;;;kDACvE,6LAAC,uJAAA,CAAA,OAAI;wCAAC,MAAK;wCAAW,SAAQ;wCAAO,QAAO;wCAAU,aAAa;;;;;;kDACnE,6LAAC,uJAAA,CAAA,OAAI;wCAAC,MAAK;wCAAW,SAAQ;wCAAW,QAAO;wCAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;kCAO/E,6LAAC;wBAAU,OAAM;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAKjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAKjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAKjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D;GAxPgB;MAAA", "debugId": null}}, {"offset": {"line": 2401, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/mood-tracking.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  <PERSON><PERSON>hart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>\n} from 'recharts';\nimport {\n  Heart,\n  Smile,\n  Meh,\n  Frown,\n  Plus,\n  Calendar,\n  TrendingUp,\n  BarChart3,\n  MessageSquare,\n  Save,\n  X,\n  Target\n} from 'lucide-react';\n\n// Mood levels with emojis and colors\nconst moodLevels = [\n  { value: 5, label: 'Excellent', emoji: '😄', color: 'text-green-500', bg: 'bg-green-100' },\n  { value: 4, label: 'Good', emoji: '😊', color: 'text-blue-500', bg: 'bg-blue-100' },\n  { value: 3, label: 'Okay', emoji: '😐', color: 'text-yellow-500', bg: 'bg-yellow-100' },\n  { value: 2, label: 'Poor', emoji: '😔', color: 'text-orange-500', bg: 'bg-orange-100' },\n  { value: 1, label: 'Terrible', emoji: '😢', color: 'text-red-500', bg: 'bg-red-100' },\n];\n\n// Mock mood data\nconst mockMoodHistory = [\n  { date: '2024-01-01', mood: 4, habits: 3, notes: 'Great start to the year!' },\n  { date: '2024-01-02', mood: 5, habits: 4, notes: 'Feeling amazing today' },\n  { date: '2024-01-03', mood: 3, habits: 2, notes: 'Bit tired but okay' },\n  { date: '2024-01-04', mood: 4, habits: 4, notes: 'Good productive day' },\n  { date: '2024-01-05', mood: 5, habits: 4, notes: 'Everything went perfectly' },\n  { date: '2024-01-06', mood: 2, habits: 1, notes: 'Stressful day at work' },\n  { date: '2024-01-07', mood: 4, habits: 3, notes: 'Relaxing weekend' },\n  { date: '2024-01-08', mood: 4, habits: 3, notes: 'Steady progress' },\n  { date: '2024-01-09', mood: 3, habits: 2, notes: 'Average day' },\n  { date: '2024-01-10', mood: 5, habits: 4, notes: 'Breakthrough moment!' },\n  { date: '2024-01-11', mood: 4, habits: 4, notes: 'Consistent and happy' },\n  { date: '2024-01-12', mood: 3, habits: 3, notes: 'Neutral but stable' },\n  { date: '2024-01-13', mood: 4, habits: 3, notes: 'Good energy today' },\n  { date: '2024-01-14', mood: 5, habits: 4, notes: 'Perfect day!' },\n];\n\nconst mockCorrelationData = [\n  { habits: 1, mood: 2.5, count: 3 },\n  { habits: 2, mood: 3.2, count: 8 },\n  { habits: 3, mood: 3.8, count: 12 },\n  { habits: 4, mood: 4.5, count: 15 },\n];\n\ninterface MoodEntryProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (entry: { mood: number; notes: string }) => void;\n}\n\nfunction MoodEntry({ isOpen, onClose, onSave }: MoodEntryProps) {\n  const [selectedMood, setSelectedMood] = useState<number | null>(null);\n  const [notes, setNotes] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (selectedMood) {\n      onSave({ mood: selectedMood, notes });\n      setSelectedMood(null);\n      setNotes('');\n      onClose();\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 z-40\"\n            onClick={onClose}\n          />\n\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-card border border-border rounded-lg shadow-lg z-50 w-full max-w-md\"\n          >\n            <form onSubmit={handleSubmit} className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-xl font-semibold\">How are you feeling?</h2>\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Mood Selection */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-3\">Select your mood</label>\n                  <div className=\"grid grid-cols-5 gap-2\">\n                    {moodLevels.map((mood) => (\n                      <motion.button\n                        key={mood.value}\n                        type=\"button\"\n                        onClick={() => setSelectedMood(mood.value)}\n                        className={`p-3 rounded-lg border-2 transition-all text-center ${\n                          selectedMood === mood.value\n                            ? 'border-primary bg-primary/10'\n                            : 'border-border hover:border-primary/50'\n                        }`}\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <div className=\"text-2xl mb-1\">{mood.emoji}</div>\n                        <div className=\"text-xs font-medium\">{mood.label}</div>\n                      </motion.button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Notes */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    Notes (Optional)\n                  </label>\n                  <textarea\n                    value={notes}\n                    onChange={(e) => setNotes(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n                    placeholder=\"What's on your mind today?\"\n                    rows={3}\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3 mt-6\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={!selectedMood}\n                  className=\"flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  <span>Save</span>\n                </button>\n              </div>\n            </form>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface MoodCardProps {\n  entry: typeof mockMoodHistory[0];\n}\n\nfunction MoodCard({ entry }: MoodCardProps) {\n  const mood = moodLevels.find(m => m.value === entry.mood);\n  \n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-card border border-border rounded-lg p-4\"\n    >\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`text-2xl`}>{mood?.emoji}</div>\n          <div>\n            <p className=\"font-medium\">{mood?.label}</p>\n            <p className=\"text-sm text-muted-foreground\">\n              {new Date(entry.date).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Target className=\"h-4 w-4 text-muted-foreground\" />\n          <span className=\"text-sm\">{entry.habits} habits</span>\n        </div>\n      </div>\n      {entry.notes && (\n        <p className=\"text-sm text-muted-foreground mt-2\">{entry.notes}</p>\n      )}\n    </motion.div>\n  );\n}\n\nexport function MoodTracking() {\n  const [isEntryOpen, setIsEntryOpen] = useState(false);\n  const [selectedView, setSelectedView] = useState('overview');\n\n  const handleSaveMood = (entry: { mood: number; notes: string }) => {\n    // In a real app, this would save to the database\n    console.log('Saving mood entry:', entry);\n  };\n\n  const averageMood = mockMoodHistory.reduce((sum, entry) => sum + entry.mood, 0) / mockMoodHistory.length;\n  const moodTrend = mockMoodHistory.slice(-7).reduce((sum, entry) => sum + entry.mood, 0) / 7;\n  const previousWeekMood = mockMoodHistory.slice(-14, -7).reduce((sum, entry) => sum + entry.mood, 0) / 7;\n  const moodChange = ((moodTrend - previousWeekMood) / previousWeekMood) * 100;\n\n  const viewOptions = [\n    { id: 'overview', name: 'Overview', icon: BarChart3 },\n    { id: 'trends', name: 'Trends', icon: TrendingUp },\n    { id: 'correlation', name: 'Habit Correlation', icon: Target },\n    { id: 'history', name: 'History', icon: Calendar },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold\">Mood Tracking</h2>\n        <button\n          onClick={() => setIsEntryOpen(true)}\n          className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          <span>Track Mood</span>\n        </button>\n      </div>\n\n      {/* View Selection */}\n      <div className=\"flex flex-wrap gap-2\">\n        {viewOptions.map((option) => {\n          const Icon = option.icon;\n          return (\n            <button\n              key={option.id}\n              onClick={() => setSelectedView(option.id)}\n              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${\n                selectedView === option.id\n                  ? 'bg-primary text-primary-foreground'\n                  : 'bg-accent hover:bg-accent/80'\n              }`}\n            >\n              <Icon className=\"h-4 w-4\" />\n              <span>{option.name}</span>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Content based on selected view */}\n      {selectedView === 'overview' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Stats */}\n          <div className=\"space-y-4\">\n            <div className=\"bg-card p-6 rounded-lg border border-border\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">Average Mood</p>\n                  <p className=\"text-2xl font-bold\">{averageMood.toFixed(1)}/5</p>\n                  <div className={`flex items-center mt-2 text-sm ${\n                    moodChange >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {moodChange >= 0 ? (\n                      <TrendingUp className=\"h-4 w-4 mr-1\" />\n                    ) : (\n                      <TrendingUp className=\"h-4 w-4 mr-1 rotate-180\" />\n                    )}\n                    <span>{Math.abs(moodChange).toFixed(1)}% from last week</span>\n                  </div>\n                </div>\n                <Heart className=\"h-8 w-8 text-red-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-card p-6 rounded-lg border border-border\">\n              <h3 className=\"font-semibold mb-3\">Mood Distribution</h3>\n              <div className=\"space-y-2\">\n                {moodLevels.reverse().map((mood) => {\n                  const count = mockMoodHistory.filter(entry => entry.mood === mood.value).length;\n                  const percentage = (count / mockMoodHistory.length) * 100;\n                  return (\n                    <div key={mood.value} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <span>{mood.emoji}</span>\n                        <span className=\"text-sm\">{mood.label}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-16 h-2 bg-muted rounded-full\">\n                          <div \n                            className={`h-full rounded-full ${mood.bg.replace('bg-', 'bg-')}`}\n                            style={{ width: `${percentage}%` }}\n                          />\n                        </div>\n                        <span className=\"text-sm text-muted-foreground w-8\">{count}</span>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Mood Trend Chart */}\n          <div className=\"lg:col-span-2 bg-card p-6 rounded-lg border border-border\">\n            <h3 className=\"font-semibold mb-4\">Mood Trends</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={mockMoodHistory}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"date\" \n                  tickFormatter={(value) => new Date(value).toLocaleDateString()}\n                />\n                <YAxis domain={[1, 5]} />\n                <Tooltip \n                  labelFormatter={(value) => new Date(value).toLocaleDateString()}\n                  formatter={(value: number) => [\n                    `${value}/5 (${moodLevels.find(m => m.value === value)?.label})`,\n                    'Mood'\n                  ]}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"mood\" \n                  stroke=\"hsl(var(--primary))\" \n                  strokeWidth={2}\n                  dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {selectedView === 'correlation' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"bg-card p-6 rounded-lg border border-border\">\n            <h3 className=\"font-semibold mb-4\">Habits vs Mood Correlation</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <ScatterChart data={mockMoodHistory}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"habits\" name=\"Habits Completed\" />\n                <YAxis dataKey=\"mood\" name=\"Mood\" domain={[1, 5]} />\n                <Tooltip \n                  formatter={(value, name) => [\n                    name === 'mood' ? `${value}/5` : value,\n                    name === 'mood' ? 'Mood' : 'Habits'\n                  ]}\n                />\n                <Scatter dataKey=\"mood\" fill=\"hsl(var(--primary))\" />\n              </ScatterChart>\n            </ResponsiveContainer>\n          </div>\n\n          <div className=\"bg-card p-6 rounded-lg border border-border\">\n            <h3 className=\"font-semibold mb-4\">Average Mood by Habits Completed</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={mockCorrelationData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"habits\" />\n                <YAxis domain={[1, 5]} />\n                <Tooltip />\n                <Bar dataKey=\"mood\" fill=\"hsl(var(--primary))\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {selectedView === 'history' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {mockMoodHistory.slice().reverse().map((entry, index) => (\n            <MoodCard key={index} entry={entry} />\n          ))}\n        </div>\n      )}\n\n      {/* Mood Entry Modal */}\n      <MoodEntry\n        isOpen={isEntryOpen}\n        onClose={() => setIsEntryOpen(false)}\n        onSave={handleSaveMood}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAjBA;;;;;AAgCA,qCAAqC;AACrC,MAAM,aAAa;IACjB;QAAE,OAAO;QAAG,OAAO;QAAa,OAAO;QAAM,OAAO;QAAkB,IAAI;IAAe;IACzF;QAAE,OAAO;QAAG,OAAO;QAAQ,OAAO;QAAM,OAAO;QAAiB,IAAI;IAAc;IAClF;QAAE,OAAO;QAAG,OAAO;QAAQ,OAAO;QAAM,OAAO;QAAmB,IAAI;IAAgB;IACtF;QAAE,OAAO;QAAG,OAAO;QAAQ,OAAO;QAAM,OAAO;QAAmB,IAAI;IAAgB;IACtF;QAAE,OAAO;QAAG,OAAO;QAAY,OAAO;QAAM,OAAO;QAAgB,IAAI;IAAa;CACrF;AAED,iBAAiB;AACjB,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAA2B;IAC5E;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAwB;IACzE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAqB;IACtE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAsB;IACvE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAA4B;IAC7E;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAwB;IACzE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAmB;IACpE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAkB;IACnE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAc;IAC/D;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAuB;IACxE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAuB;IACxE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAqB;IACtE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAoB;IACrE;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;QAAG,OAAO;IAAe;CACjE;AAED,MAAM,sBAAsB;IAC1B;QAAE,QAAQ;QAAG,MAAM;QAAK,OAAO;IAAE;IACjC;QAAE,QAAQ;QAAG,MAAM;QAAK,OAAO;IAAE;IACjC;QAAE,QAAQ;QAAG,MAAM;QAAK,OAAO;IAAG;IAClC;QAAE,QAAQ;QAAG,MAAM;QAAK,OAAO;IAAG;CACnC;AAQD,SAAS,UAAU,KAA2C;QAA3C,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAkB,GAA3C;;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,cAAc;YAChB,OAAO;gBAAE,MAAM;gBAAc;YAAM;YACnC,gBAAgB;YAChB,SAAS;YACT;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAGX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBACvC,WAAU;8BAEV,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDAEZ,MAAK;wDACL,SAAS,IAAM,gBAAgB,KAAK,KAAK;wDACzC,WAAW,AAAC,sDAIX,OAHC,iBAAiB,KAAK,KAAK,GACvB,iCACA;wDAEN,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,6LAAC;gEAAI,WAAU;0EAAiB,KAAK,KAAK;;;;;;0EAC1C,6LAAC;gEAAI,WAAU;0EAAuB,KAAK,KAAK;;;;;;;uDAZ3C,KAAK,KAAK;;;;;;;;;;;;;;;;kDAmBvB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;gDACV,aAAY;gDACZ,MAAM;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU,CAAC;wCACX,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA3GS;KAAA;AAiHT,SAAS,SAAS,KAAwB;QAAxB,EAAE,KAAK,EAAiB,GAAxB;IAChB,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,MAAM,IAAI;IAExD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAY;0CAAY,iBAAA,2BAAA,KAAM,KAAK;;;;;;0CACxC,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAe,iBAAA,2BAAA,KAAM,KAAK;;;;;;kDACvC,6LAAC;wCAAE,WAAU;kDACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;;oCAAW,MAAM,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;YAG3C,MAAM,KAAK,kBACV,6LAAC;gBAAE,WAAU;0BAAsC,MAAM,KAAK;;;;;;;;;;;;AAItE;MA7BS;AA+BF,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,CAAC;QACtB,iDAAiD;QACjD,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,cAAc,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,IAAI,EAAE,KAAK,gBAAgB,MAAM;IACxG,MAAM,YAAY,gBAAgB,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,IAAI,EAAE,KAAK;IAC1F,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,IAAI,EAAE,KAAK;IACtG,MAAM,aAAa,AAAC,CAAC,YAAY,gBAAgB,IAAI,mBAAoB;IAEzE,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM,qNAAA,CAAA,YAAS;QAAC;QACpD;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM,qNAAA,CAAA,aAAU;QAAC;QACjD;YAAE,IAAI;YAAe,MAAM;YAAqB,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC7D;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAClD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC;oBAChB,MAAM,OAAO,OAAO,IAAI;oBACxB,qBACE,6LAAC;wBAEC,SAAS,IAAM,gBAAgB,OAAO,EAAE;wBACxC,WAAW,AAAC,sEAIX,OAHC,iBAAiB,OAAO,EAAE,GACtB,uCACA;;0CAGN,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAM,OAAO,IAAI;;;;;;;uBATb,OAAO,EAAE;;;;;gBAYpB;;;;;;YAID,iBAAiB,4BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;;wDAAsB,YAAY,OAAO,CAAC;wDAAG;;;;;;;8DAC1D,6LAAC;oDAAI,WAAW,AAAC,kCAEhB,OADC,cAAc,IAAI,mBAAmB;;wDAEpC,cAAc,kBACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEAExB,6LAAC;;gEAAM,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAG3C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,GAAG,GAAG,CAAC,CAAC;4CACzB,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,MAAM;4CAC/E,MAAM,aAAa,AAAC,QAAQ,gBAAgB,MAAM,GAAI;4CACtD,qBACE,6LAAC;gDAAqB,WAAU;;kEAC9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,KAAK;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAW,KAAK,KAAK;;;;;;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAW,AAAC,uBAAoD,OAA9B,KAAK,EAAE,CAAC,OAAO,CAAC,OAAO;oEACzD,OAAO;wEAAE,OAAO,AAAC,GAAa,OAAX,YAAW;oEAAG;;;;;;;;;;;0EAGrC,6LAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;;+CAZ/C,KAAK,KAAK;;;;;wCAgBxB;;;;;;;;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,6LAAC,wJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,eAAe,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;;;;;;sDAE9D,6LAAC,wJAAA,CAAA,QAAK;4CAAC,QAAQ;gDAAC;gDAAG;6CAAE;;;;;;sDACrB,6LAAC,0JAAA,CAAA,UAAO;4CACN,gBAAgB,CAAC,QAAU,IAAI,KAAK,OAAO,kBAAkB;4CAC7D,WAAW,CAAC;oDACK;uDADa;oDAC3B,UAAE,OAAM,QAAqD,QAA/C,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,oBAAjC,uCAAA,iBAAyC,KAAK,EAAC;oDAC9D;iDACD;;;;;;;sDAEH,6LAAC,uJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,KAAK;gDAAE,MAAM;gDAAuB,aAAa;gDAAG,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpE,iBAAiB,+BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,6LAAC,2JAAA,CAAA,eAAY;oCAAC,MAAM;;sDAClB,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAS,MAAK;;;;;;sDAC7B,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAK;4CAAO,QAAQ;gDAAC;gDAAG;6CAAE;;;;;;sDAChD,6LAAC,0JAAA,CAAA,UAAO;4CACN,WAAW,CAAC,OAAO,OAAS;oDAC1B,SAAS,SAAS,AAAC,GAAQ,OAAN,OAAM,QAAM;oDACjC,SAAS,SAAS,SAAS;iDAC5B;;;;;;sDAEH,6LAAC,0JAAA,CAAA,UAAO;4CAAC,SAAQ;4CAAO,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC,sKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;oCAAC,MAAM;;sDACd,6LAAC,gKAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,6LAAC,wJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,6LAAC,wJAAA,CAAA,QAAK;4CAAC,QAAQ;gDAAC;gDAAG;6CAAE;;;;;;sDACrB,6LAAC,0JAAA,CAAA,UAAO;;;;;sDACR,6LAAC,sJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAO,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC,iBAAiB,2BAChB,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC7C,6LAAC;wBAAqB,OAAO;uBAAd;;;;;;;;;;0BAMrB,6LAAC;gBACC,QAAQ;gBACR,SAAS,IAAM,eAAe;gBAC9B,QAAQ;;;;;;;;;;;;AAIhB;IA/LgB;MAAA", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/settings.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useTheme } from 'next-themes';\nimport {\n  Settings as SettingsIcon,\n  Palette,\n  Globe,\n  Bell,\n  User,\n  Shield,\n  Download,\n  Upload,\n  Trash2,\n  Save,\n  Moon,\n  Sun,\n  Monitor,\n  Volume2,\n  VolumeX,\n  Smartphone,\n  Mail,\n  Clock,\n  Languages\n} from 'lucide-react';\n\nconst languages = [\n  { code: 'en', name: 'English', nativeName: 'English' },\n  { code: 'zh', name: 'Chinese', nativeName: '中文' },\n  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },\n  { code: 'es', name: 'Spanish', nativeName: 'Español' },\n  { code: 'ar', name: 'Arabic', nativeName: 'العربية' },\n  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা' },\n  { code: 'fr', name: 'French', nativeName: 'Français' },\n  { code: 'ru', name: 'Russian', nativeName: 'Русский' },\n  { code: 'pt', name: 'Portuguese', nativeName: 'Português' },\n  { code: 'ur', name: 'Urdu', nativeName: 'اردو' },\n  { code: 'id', name: 'Indonesian', nativeName: 'Bahasa Indonesia' },\n  { code: 'sw', name: 'Swahili', nativeName: 'Kiswahili' },\n  { code: 'pa', name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ' },\n  { code: 'ko', name: 'Korean', nativeName: '한국어' },\n  { code: 'ja', name: 'Japanese', nativeName: '日本語' },\n  { code: 'de', name: 'German', nativeName: 'Deutsch' },\n];\n\nconst themes = [\n  { id: 'light', name: 'Light', icon: Sun },\n  { id: 'dark', name: 'Dark', icon: Moon },\n  { id: 'system', name: 'System', icon: Monitor },\n];\n\ninterface SettingsSectionProps {\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  children: React.ReactNode;\n}\n\nfunction SettingsSection({ title, description, icon: Icon, children }: SettingsSectionProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-card p-6 rounded-lg border border-border\"\n    >\n      <div className=\"flex items-center space-x-3 mb-4\">\n        <Icon className=\"h-5 w-5 text-primary\" />\n        <div>\n          <h3 className=\"text-lg font-semibold\">{title}</h3>\n          <p className=\"text-sm text-muted-foreground\">{description}</p>\n        </div>\n      </div>\n      {children}\n    </motion.div>\n  );\n}\n\ninterface ToggleProps {\n  enabled: boolean;\n  onChange: (enabled: boolean) => void;\n  label: string;\n  description?: string;\n}\n\nfunction Toggle({ enabled, onChange, label, description }: ToggleProps) {\n  return (\n    <div className=\"flex items-center justify-between py-2\">\n      <div>\n        <p className=\"font-medium\">{label}</p>\n        {description && <p className=\"text-sm text-muted-foreground\">{description}</p>}\n      </div>\n      <button\n        onClick={() => onChange(!enabled)}\n        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n          enabled ? 'bg-primary' : 'bg-muted'\n        }`}\n      >\n        <span\n          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n            enabled ? 'translate-x-6' : 'translate-x-1'\n          }`}\n        />\n      </button>\n    </div>\n  );\n}\n\nexport function Settings() {\n  const { theme, setTheme } = useTheme();\n  const [selectedLanguage, setSelectedLanguage] = useState('en');\n  const [notifications, setNotifications] = useState({\n    push: true,\n    email: false,\n    sound: true,\n    reminders: true,\n    achievements: true,\n    weeklyReport: true,\n  });\n  const [privacy, setPrivacy] = useState({\n    analytics: true,\n    crashReports: true,\n    dataSharing: false,\n  });\n\n  const handleExportData = () => {\n    // In a real app, this would export user data\n    const data = {\n      habits: [],\n      moods: [],\n      settings: { theme, language: selectedLanguage, notifications, privacy },\n      exportDate: new Date().toISOString(),\n    };\n    \n    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `habitflow-export-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleImportData = () => {\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = '.json';\n    input.onchange = (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          try {\n            const data = JSON.parse(e.target?.result as string);\n            console.log('Imported data:', data);\n            // In a real app, this would restore user data\n          } catch (error) {\n            console.error('Failed to import data:', error);\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    input.click();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold\">Settings</h2>\n        <button className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2\">\n          <Save className=\"h-4 w-4\" />\n          <span>Save Changes</span>\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Theme Settings */}\n        <SettingsSection\n          title=\"Appearance\"\n          description=\"Customize the look and feel of your app\"\n          icon={Palette}\n        >\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Theme</label>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {themes.map((themeOption) => {\n                  const Icon = themeOption.icon;\n                  return (\n                    <button\n                      key={themeOption.id}\n                      onClick={() => setTheme(themeOption.id)}\n                      className={`p-3 rounded-lg border transition-all flex flex-col items-center space-y-2 ${\n                        theme === themeOption.id\n                          ? 'border-primary bg-primary/10'\n                          : 'border-border hover:border-primary/50'\n                      }`}\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"text-sm\">{themeOption.name}</span>\n                    </button>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n        </SettingsSection>\n\n        {/* Language Settings */}\n        <SettingsSection\n          title=\"Language & Region\"\n          description=\"Choose your preferred language\"\n          icon={Languages}\n        >\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Language</label>\n            <select\n              value={selectedLanguage}\n              onChange={(e) => setSelectedLanguage(e.target.value)}\n              className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n            >\n              {languages.map((lang) => (\n                <option key={lang.code} value={lang.code}>\n                  {lang.nativeName} ({lang.name})\n                </option>\n              ))}\n            </select>\n          </div>\n        </SettingsSection>\n\n        {/* Notification Settings */}\n        <SettingsSection\n          title=\"Notifications\"\n          description=\"Manage how you receive updates and reminders\"\n          icon={Bell}\n        >\n          <div className=\"space-y-3\">\n            <Toggle\n              enabled={notifications.push}\n              onChange={(enabled) => setNotifications({ ...notifications, push: enabled })}\n              label=\"Push Notifications\"\n              description=\"Receive notifications on your device\"\n            />\n            <Toggle\n              enabled={notifications.email}\n              onChange={(enabled) => setNotifications({ ...notifications, email: enabled })}\n              label=\"Email Notifications\"\n              description=\"Get updates via email\"\n            />\n            <Toggle\n              enabled={notifications.sound}\n              onChange={(enabled) => setNotifications({ ...notifications, sound: enabled })}\n              label=\"Sound\"\n              description=\"Play sounds for notifications\"\n            />\n            <Toggle\n              enabled={notifications.reminders}\n              onChange={(enabled) => setNotifications({ ...notifications, reminders: enabled })}\n              label=\"Habit Reminders\"\n              description=\"Get reminded about your habits\"\n            />\n            <Toggle\n              enabled={notifications.achievements}\n              onChange={(enabled) => setNotifications({ ...notifications, achievements: enabled })}\n              label=\"Achievement Alerts\"\n              description=\"Celebrate your milestones\"\n            />\n            <Toggle\n              enabled={notifications.weeklyReport}\n              onChange={(enabled) => setNotifications({ ...notifications, weeklyReport: enabled })}\n              label=\"Weekly Reports\"\n              description=\"Receive weekly progress summaries\"\n            />\n          </div>\n        </SettingsSection>\n\n        {/* Privacy Settings */}\n        <SettingsSection\n          title=\"Privacy & Data\"\n          description=\"Control your data and privacy preferences\"\n          icon={Shield}\n        >\n          <div className=\"space-y-3\">\n            <Toggle\n              enabled={privacy.analytics}\n              onChange={(enabled) => setPrivacy({ ...privacy, analytics: enabled })}\n              label=\"Analytics\"\n              description=\"Help improve the app with usage data\"\n            />\n            <Toggle\n              enabled={privacy.crashReports}\n              onChange={(enabled) => setPrivacy({ ...privacy, crashReports: enabled })}\n              label=\"Crash Reports\"\n              description=\"Automatically send crash reports\"\n            />\n            <Toggle\n              enabled={privacy.dataSharing}\n              onChange={(enabled) => setPrivacy({ ...privacy, dataSharing: enabled })}\n              label=\"Data Sharing\"\n              description=\"Share anonymized data for research\"\n            />\n          </div>\n        </SettingsSection>\n\n        {/* Data Management */}\n        <SettingsSection\n          title=\"Data Management\"\n          description=\"Import, export, and manage your data\"\n          icon={Download}\n        >\n          <div className=\"space-y-3\">\n            <button\n              onClick={handleExportData}\n              className=\"w-full flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>Export Data</span>\n            </button>\n            <button\n              onClick={handleImportData}\n              className=\"w-full flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\"\n            >\n              <Upload className=\"h-4 w-4\" />\n              <span>Import Data</span>\n            </button>\n            <button className=\"w-full flex items-center justify-center space-x-2 px-4 py-2 border border-destructive text-destructive rounded-lg hover:bg-destructive/10 transition-colors\">\n              <Trash2 className=\"h-4 w-4\" />\n              <span>Delete All Data</span>\n            </button>\n          </div>\n        </SettingsSection>\n\n        {/* Account Settings */}\n        <SettingsSection\n          title=\"Account\"\n          description=\"Manage your account and profile\"\n          icon={User}\n        >\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Display Name</label>\n              <input\n                type=\"text\"\n                defaultValue=\"John Doe\"\n                className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Email</label>\n              <input\n                type=\"email\"\n                defaultValue=\"<EMAIL>\"\n                className=\"w-full px-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary\"\n              />\n            </div>\n            <button className=\"w-full bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\">\n              Update Profile\n            </button>\n          </div>\n        </SettingsSection>\n      </div>\n\n      {/* App Info */}\n      <div className=\"bg-card p-6 rounded-lg border border-border\">\n        <h3 className=\"text-lg font-semibold mb-4\">About HabitFlow</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n          <div>\n            <p className=\"font-medium\">Version</p>\n            <p className=\"text-muted-foreground\">1.0.0</p>\n          </div>\n          <div>\n            <p className=\"font-medium\">Last Updated</p>\n            <p className=\"text-muted-foreground\">January 2024</p>\n          </div>\n          <div>\n            <p className=\"font-medium\">Support</p>\n            <p className=\"text-muted-foreground\"><EMAIL></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AA2BA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAU;IACrD;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAK;IAChD;QAAE,MAAM;QAAM,MAAM;QAAS,YAAY;IAAS;IAClD;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAU;IACrD;QAAE,MAAM;QAAM,MAAM;QAAU,YAAY;IAAU;IACpD;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAQ;IACnD;QAAE,MAAM;QAAM,MAAM;QAAU,YAAY;IAAW;IACrD;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAU;IACrD;QAAE,MAAM;QAAM,MAAM;QAAc,YAAY;IAAY;IAC1D;QAAE,MAAM;QAAM,MAAM;QAAQ,YAAY;IAAO;IAC/C;QAAE,MAAM;QAAM,MAAM;QAAc,YAAY;IAAmB;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAY;IACvD;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;IAAS;IACpD;QAAE,MAAM;QAAM,MAAM;QAAU,YAAY;IAAM;IAChD;QAAE,MAAM;QAAM,MAAM;QAAY,YAAY;IAAM;IAClD;QAAE,MAAM;QAAM,MAAM;QAAU,YAAY;IAAU;CACrD;AAED,MAAM,SAAS;IACb;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM,mMAAA,CAAA,MAAG;IAAC;IACxC;QAAE,IAAI;QAAQ,MAAM;QAAQ,MAAM,qMAAA,CAAA,OAAI;IAAC;IACvC;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM,2MAAA,CAAA,UAAO;IAAC;CAC/C;AASD,SAAS,gBAAgB,KAAkE;QAAlE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,IAAI,EAAE,QAAQ,EAAwB,GAAlE;IACvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;YAGjD;;;;;;;AAGP;KAjBS;AA0BT,SAAS,OAAO,KAAsD;QAAtD,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAe,GAAtD;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;oBAC3B,6BAAe,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;;;;;;;0BAEhE,6LAAC;gBACC,SAAS,IAAM,SAAS,CAAC;gBACzB,WAAW,AAAC,6EAEX,OADC,UAAU,eAAe;0BAG3B,cAAA,6LAAC;oBACC,WAAW,AAAC,6EAEX,OADC,UAAU,kBAAkB;;;;;;;;;;;;;;;;;AAMxC;MArBS;AAuBF,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW;QACX,cAAc;QACd,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,WAAW;QACX,cAAc;QACd,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,6CAA6C;QAC7C,MAAM,OAAO;YACX,QAAQ,EAAE;YACV,OAAO,EAAE;YACT,UAAU;gBAAE;gBAAO,UAAU;gBAAkB;gBAAe;YAAQ;YACtE,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,MAAM,MAAM;SAAG,EAAE;YAAE,MAAM;QAAmB;QAClF,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,oBAA0D,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;QACxE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;gBACH;YAAb,MAAM,QAAO,SAAA,AAAC,EAAE,MAAM,CAAsB,KAAK,cAApC,6BAAA,MAAsC,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI;4BACsB;wBAAxB,MAAM,OAAO,KAAK,KAAK,EAAC,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;wBACxC,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,8CAA8C;oBAChD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C;gBACF;gBACA,OAAO,UAAU,CAAC;YACpB;QACF;QACA,MAAM,KAAK;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,2MAAA,CAAA,UAAO;kCAEb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC;4CACX,MAAM,OAAO,YAAY,IAAI;4CAC7B,qBACE,6LAAC;gDAEC,SAAS,IAAM,SAAS,YAAY,EAAE;gDACtC,WAAW,AAAC,6EAIX,OAHC,UAAU,YAAY,EAAE,GACpB,iCACA;;kEAGN,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAW,YAAY,IAAI;;;;;;;+CATtC,YAAY,EAAE;;;;;wCAYzB;;;;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,+MAAA,CAAA,YAAS;kCAEf,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;8CAET,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;4CAAuB,OAAO,KAAK,IAAI;;gDACrC,KAAK,UAAU;gDAAC;gDAAG,KAAK,IAAI;gDAAC;;2CADnB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;kCAS9B,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,qMAAA,CAAA,OAAI;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,cAAc,IAAI;oCAC3B,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,MAAM;wCAAQ;oCAC1E,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,cAAc,KAAK;oCAC5B,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,OAAO;wCAAQ;oCAC3E,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,cAAc,KAAK;oCAC5B,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,OAAO;wCAAQ;oCAC3E,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,cAAc,SAAS;oCAChC,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,WAAW;wCAAQ;oCAC/E,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,cAAc,YAAY;oCACnC,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,cAAc;wCAAQ;oCAClF,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,cAAc,YAAY;oCACnC,UAAU,CAAC,UAAY,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,cAAc;wCAAQ;oCAClF,OAAM;oCACN,aAAY;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,yMAAA,CAAA,SAAM;kCAEZ,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,QAAQ,SAAS;oCAC1B,UAAU,CAAC,UAAY,WAAW;4CAAE,GAAG,OAAO;4CAAE,WAAW;wCAAQ;oCACnE,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,QAAQ,YAAY;oCAC7B,UAAU,CAAC,UAAY,WAAW;4CAAE,GAAG,OAAO;4CAAE,cAAc;wCAAQ;oCACtE,OAAM;oCACN,aAAY;;;;;;8CAEd,6LAAC;oCACC,SAAS,QAAQ,WAAW;oCAC5B,UAAU,CAAC,UAAY,WAAW;4CAAE,GAAG,OAAO;4CAAE,aAAa;wCAAQ;oCACrE,OAAM;oCACN,aAAY;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,6MAAA,CAAA,WAAQ;kCAEd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAM,qMAAA,CAAA,OAAI;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,MAAK;4CACL,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,MAAK;4CACL,cAAa;4CACb,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAO,WAAU;8CAAuG;;;;;;;;;;;;;;;;;;;;;;;0BAQ/H,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAc;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAc;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAc;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAtRgB;;QACc,mJAAA,CAAA,WAAQ;;;MADtB", "debugId": null}}, {"offset": {"line": 4450, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/4/habit-tracker/src/components/dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Calendar,\n  Target,\n  TrendingUp,\n  Heart,\n  Settings,\n  Plus,\n  BarChart3,\n  Flame,\n  Award,\n  Moon,\n  Sun\n} from 'lucide-react';\nimport { useTheme } from 'next-themes';\nimport { ThemeSelectorButton } from './theme-selector';\nimport { HabitManagement } from './habit-management';\nimport { Analytics } from './analytics';\nimport { MoodTracking } from './mood-tracking';\nimport { Settings as SettingsPanel } from './settings';\n\n// Mock data for demonstration\nconst mockHabits = [\n  { id: 1, name: 'Drink Water', streak: 7, completed: true, category: 'Health' },\n  { id: 2, name: 'Exercise', streak: 3, completed: false, category: 'Fitness' },\n  { id: 3, name: 'Read', streak: 12, completed: true, category: 'Learning' },\n  { id: 4, name: 'Meditate', streak: 5, completed: true, category: 'Wellness' },\n];\n\nconst mockMoodData = [\n  { date: '2024-01-15', mood: 4, habits: 3 },\n  { date: '2024-01-16', mood: 5, habits: 4 },\n  { date: '2024-01-17', mood: 3, habits: 2 },\n  { date: '2024-01-18', mood: 4, habits: 4 },\n  { date: '2024-01-19', mood: 5, habits: 4 },\n];\n\nexport function Dashboard() {\n  const { theme, setTheme } = useTheme();\n  const [selectedTab, setSelectedTab] = useState('dashboard');\n\n  const completedToday = mockHabits.filter(h => h.completed).length;\n  const totalHabits = mockHabits.length;\n  const completionRate = Math.round((completedToday / totalHabits) * 100);\n  const longestStreak = Math.max(...mockHabits.map(h => h.streak));\n\n  const tabs = [\n    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },\n    { id: 'habits', label: 'Habits', icon: Target },\n    { id: 'analytics', label: 'Analytics', icon: TrendingUp },\n    { id: 'mood', label: 'Mood', icon: Heart },\n    { id: 'settings', label: 'Settings', icon: Settings },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b border-border bg-card\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Flame className=\"h-8 w-8 text-primary\" />\n                <h1 className=\"text-2xl font-bold text-foreground\">\n                  HabitFlow\n                </h1>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <ThemeSelectorButton />\n              <button\n                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n                className=\"p-2 rounded-lg hover:bg-accent transition-colors\"\n                title=\"Toggle Dark Mode\"\n              >\n                {theme === 'dark' ? (\n                  <Sun className=\"h-5 w-5\" />\n                ) : (\n                  <Moon className=\"h-5 w-5\" />\n                )}\n              </button>\n              <button className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2\">\n                <Plus className=\"h-4 w-4\" />\n                <span>Create Habit</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* Sidebar Navigation */}\n          <div className=\"lg:col-span-1\">\n            <nav className=\"space-y-2\">\n              {tabs.map((tab) => {\n                const Icon = tab.icon;\n                return (\n                  <button\n                    key={tab.id}\n                    onClick={() => setSelectedTab(tab.id)}\n                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                      selectedTab === tab.id\n                        ? 'bg-primary text-primary-foreground'\n                        : 'hover:bg-accent text-muted-foreground hover:text-foreground'\n                    }`}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                    <span>{tab.label}</span>\n                  </button>\n                );\n              })}\n            </nav>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:col-span-3\">\n            {selectedTab === 'dashboard' && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"space-y-6\"\n              >\n                {/* Stats Cards */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"bg-card p-6 rounded-lg border border-border\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm text-muted-foreground\">Today's Progress</p>\n                        <p className=\"text-2xl font-bold text-foreground\">\n                          {completedToday}/{totalHabits}\n                        </p>\n                        <p className=\"text-sm text-muted-foreground\">{completionRate}% Complete</p>\n                      </div>\n                      <Target className=\"h-8 w-8 text-primary\" />\n                    </div>\n                  </div>\n\n                  <div className=\"bg-card p-6 rounded-lg border border-border\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm text-muted-foreground\">Longest Streak</p>\n                        <p className=\"text-2xl font-bold text-foreground\">{longestStreak}</p>\n                        <p className=\"text-sm text-muted-foreground\">Days</p>\n                      </div>\n                      <Flame className=\"h-8 w-8 text-orange-500\" />\n                    </div>\n                  </div>\n\n                  <div className=\"bg-card p-6 rounded-lg border border-border\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm text-muted-foreground\">Achievements</p>\n                        <p className=\"text-2xl font-bold text-foreground\">12</p>\n                        <p className=\"text-sm text-muted-foreground\">Unlocked</p>\n                      </div>\n                      <Award className=\"h-8 w-8 text-yellow-500\" />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Today's Habits */}\n                <div className=\"bg-card p-6 rounded-lg border border-border\">\n                  <h2 className=\"text-xl font-semibold mb-4\">Today's Habits</h2>\n                  <div className=\"space-y-3\">\n                    {mockHabits.map((habit) => (\n                      <div\n                        key={habit.id}\n                        className=\"flex items-center justify-between p-3 rounded-lg bg-muted/50\"\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <div\n                            className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${\n                              habit.completed\n                                ? 'bg-primary border-primary'\n                                : 'border-muted-foreground'\n                            }`}\n                          >\n                            {habit.completed && (\n                              <div className=\"w-2 h-2 bg-primary-foreground rounded-full\" />\n                            )}\n                          </div>\n                          <div>\n                            <p className=\"font-medium\">{habit.name}</p>\n                            <p className=\"text-sm text-muted-foreground\">{habit.category}</p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Flame className=\"h-4 w-4 text-orange-500\" />\n                          <span className=\"text-sm font-medium\">{habit.streak}</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Quick Actions */}\n                <div className=\"bg-card p-6 rounded-lg border border-border\">\n                  <h2 className=\"text-xl font-semibold mb-4\">Quick Actions</h2>\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                    <button className=\"p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center\">\n                      <Plus className=\"h-6 w-6 mx-auto mb-2 text-primary\" />\n                      <span className=\"text-sm font-medium\">Add Habit</span>\n                    </button>\n                    <button className=\"p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center\">\n                      <Heart className=\"h-6 w-6 mx-auto mb-2 text-primary\" />\n                      <span className=\"text-sm font-medium\">Track Mood</span>\n                    </button>\n                    <button className=\"p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center\">\n                      <TrendingUp className=\"h-6 w-6 mx-auto mb-2 text-primary\" />\n                      <span className=\"text-sm font-medium\">View Analytics</span>\n                    </button>\n                    <button className=\"p-4 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-center\">\n                      <Calendar className=\"h-6 w-6 mx-auto mb-2 text-primary\" />\n                      <span className=\"text-sm font-medium\">Calendar</span>\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n\n            {selectedTab === 'habits' && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HabitManagement />\n              </motion.div>\n            )}\n\n            {selectedTab === 'analytics' && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <Analytics />\n              </motion.div>\n            )}\n\n            {selectedTab === 'mood' && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <MoodTracking />\n              </motion.div>\n            )}\n\n            {selectedTab === 'settings' && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <SettingsPanel />\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;AAtBA;;;;;;;;;;AAwBA,8BAA8B;AAC9B,MAAM,aAAa;IACjB;QAAE,IAAI;QAAG,MAAM;QAAe,QAAQ;QAAG,WAAW;QAAM,UAAU;IAAS;IAC7E;QAAE,IAAI;QAAG,MAAM;QAAY,QAAQ;QAAG,WAAW;QAAO,UAAU;IAAU;IAC5E;QAAE,IAAI;QAAG,MAAM;QAAQ,QAAQ;QAAI,WAAW;QAAM,UAAU;IAAW;IACzE;QAAE,IAAI;QAAG,MAAM;QAAY,QAAQ;QAAG,WAAW;QAAM,UAAU;IAAW;CAC7E;AAED,MAAM,eAAe;IACnB;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;IAAE;IACzC;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;IAAE;IACzC;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;IAAE;IACzC;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;IAAE;IACzC;QAAE,MAAM;QAAc,MAAM;QAAG,QAAQ;IAAE;CAC1C;AAEM,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;IACjE,MAAM,cAAc,WAAW,MAAM;IACrC,MAAM,iBAAiB,KAAK,KAAK,CAAC,AAAC,iBAAiB,cAAe;IACnE,MAAM,gBAAgB,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;IAE9D,MAAM,OAAO;QACX;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,qNAAA,CAAA,YAAS;QAAC;QACvD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,qNAAA,CAAA,aAAU;QAAC;QACxD;YAAE,IAAI;YAAQ,OAAO;YAAQ,MAAM,uMAAA,CAAA,QAAK;QAAC;QACzC;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACrD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;;;;;;;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0IAAA,CAAA,sBAAmB;;;;;kDACpB,6LAAC;wCACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;wCACrD,WAAU;wCACV,OAAM;kDAEL,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGpB,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC;oCACT,MAAM,OAAO,IAAI,IAAI;oCACrB,qBACE,6LAAC;wCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;wCACpC,WAAW,AAAC,6EAIX,OAHC,gBAAgB,IAAI,EAAE,GAClB,uCACA;;0DAGN,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;0DAAM,IAAI,KAAK;;;;;;;uCATX,IAAI,EAAE;;;;;gCAYjB;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;gCACZ,gBAAgB,6BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;kFAC7C,6LAAC;wEAAE,WAAU;;4EACV;4EAAe;4EAAE;;;;;;;kFAEpB,6LAAC;wEAAE,WAAU;;4EAAiC;4EAAe;;;;;;;;;;;;;0EAE/D,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAItB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;kFAC7C,6LAAC;wEAAE,WAAU;kFAAsC;;;;;;kFACnD,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAIrB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;kFAC7C,6LAAC;wEAAE,WAAU;kFAAqC;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC;4DAEC,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAW,AAAC,kEAIX,OAHC,MAAM,SAAS,GACX,8BACA;sFAGL,MAAM,SAAS,kBACd,6LAAC;gFAAI,WAAU;;;;;;;;;;;sFAGnB,6LAAC;;8FACC,6LAAC;oFAAE,WAAU;8FAAe,MAAM,IAAI;;;;;;8FACtC,6LAAC;oFAAE,WAAU;8FAAiC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;8EAGhE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;4EAAK,WAAU;sFAAuB,MAAM,MAAM;;;;;;;;;;;;;2DAtBhD,MAAM,EAAE;;;;;;;;;;;;;;;;sDA8BrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAO/C,gBAAgB,0BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,4IAAA,CAAA,kBAAe;;;;;;;;;;gCAInB,gBAAgB,6BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,kIAAA,CAAA,YAAS;;;;;;;;;;gCAIb,gBAAgB,wBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,yIAAA,CAAA,eAAY;;;;;;;;;;gCAIhB,gBAAgB,4BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,iIAAA,CAAA,WAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9B;GArOgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}]}